FROM ubuntu:20.04

# 安装python和build工具，用于编译
RUN apt-get update && \
    apt-get install -y python3 python3-pip python3-dev && \
    apt-get install -y build-essential && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# 将下面的<USERNAME>和<PASSWORD>替换为可以发布python包的用户名和密码
RUN echo "[distutils]" > /root/.pypirc && \
    echo "index-servers =" >> /root/.pypirc && \
    echo "    wypypi" >> /root/.pypirc && \
    echo "    wypractice" >> /root/.pypirc && \
    echo "" >> /root/.pypirc && \
    echo "[wypypi]" >> /root/.pypirc && \
    echo "repository: http://nexus.wycluster.com/repository/wy/" >> /root/.pypirc && \
    echo "username: <USERNAME>" >> /root/.pypirc && \
    echo "password: <PASSWORD>" >> /root/.pypirc && \
    echo "" >> /root/.pypirc && \
    echo "[wypractice]" >> /root/.pypirc && \
    echo "repository: http://nexus.wycluster.com/repository/wypractice/" >> /root/.pypirc && \
    echo "username: <USERNAME>" >> /root/.pypirc && \
    echo "password: <PASSWORD>" >> /root/.pypirc

WORKDIR /app

COPY . .

# 安装依赖
RUN pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple -r requirements-dev.txt
