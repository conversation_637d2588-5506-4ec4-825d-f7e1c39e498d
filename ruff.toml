# 排除特定路径或文件
exclude = [
  "**/.venv/**",
  "**/build/**",
  "**/dist/**",
  "*.pyi", # "*.ipynb",,
]
include = [
  "kunpeng_replay/**/*.py",
]
# 设定缩进宽度
indent-width = 4
# 设定最大行宽
line-length = 120

[lint]
# 启用额外的警告规则
select = [
  "E", # pycodestyle errors
  "W", # pycodestyle warnings
  "F", # pyflakes
  "I", # isort
  "B", # flake8-bugbear
  "C90", # mccabe
  "UP", # pyupgrade
  "SIM", # flake8-simplify
]
# 忽略的规则
ignore = [
  "UP015", # https://docs.astral.sh/ruff/rules/redundant-open-modes/
  "UP035", # https://docs.astral.sh/ruff/rules/deprecated-import/
  "UP006", # https://docs.astral.sh/ruff/rules/non-pep585-annotation/
  "B028", # https://docs.astral.sh/ruff/rules/no-explicit-stacklevel/
  "B017", # https://docs.astral.sh/ruff/rules/assert-raises-exception/
]
# 允许修正所有启用的规则（当提供 `-fix`）
fixable = ["ALL"]
# 禁用规则，如未使用导入的修复 (`F401`)
unfixable = []
# 允许使用下划线前缀的未使用变量
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# 忽略所有 `__init__.py` 文件和选定子目录中的 `E402`（import违规）
[lint.per-file-ignores]
"**/{tests,docs,tools}/*" = ["E402"]
"__init__.py" = ["E402"]
"ai_queue_server/proto/*" = ["E712"]
"tools/download_models_from_rockminds.py" = ["E402", "E501", "SIM112"]

[lint.mccabe]
# Flag errors (`C901`) whenever the complexity level exceeds 10.
max-complexity = 20
