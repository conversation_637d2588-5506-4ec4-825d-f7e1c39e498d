# Kunpeng Replay

高效的金融数据事件驱动回放引擎。

## 特性

- 🚀 **事件驱动架构**: 精确的时间控制，消除等待时间
- ⚡ **高性能**: 优化的事件处理流水线
- 🔧 **灵活配置**: 支持多种回放模式和自定义处理器
- 📊 **详细统计**: 完整的性能监控和进度追踪
- 🔌 **易于集成**: 简单的API接口，支持现有系统集成

## 快速开始

### 安装

```bash
pip install kunpeng-replay
```

### 基本使用

```python
from kunpeng_replay import ReplayEngine, ReplayConfig
from kunpeng_replay.data_source.base_reader import MockDataReader
from kunpeng_replay.processors.base_processor import CallbackProcessor

# 创建配置
config = ReplayConfig(
    mode=ReplayMode.EVENT_DRIVEN,
    tradingday="20250218",
    progress_bar=True
)

# 创建引擎
engine = ReplayEngine(config)

# 设置数据源
reader = MockDataReader(config)
engine.set_data_reader(reader)

# 添加处理器
def process_event(event):
    print(f"Processing: {event}")
    return True

processor = CallbackProcessor(process_event)
engine.set_processor(processor)

# 运行回放
import asyncio
stats = asyncio.run(engine.run())
print(f"Completed: {stats}")
```

### 高级使用

```python
from kunpeng_replay import MarketDataReplayEngine, ReplayConfig, ReplayMode
from kunpeng_replay.data_source.base_reader import MockDataReader
from kunpeng_replay.processors.base_processor import MessageQueueProcessor

# 创建配置
config = ReplayConfig(
    mode=ReplayMode.EVENT_DRIVEN,
    tradingday="20250218",
    progress_bar=True
)

# 创建引擎
engine = MarketDataReplayEngine(config)

# 设置数据源
reader = MockDataReader(config)
engine.set_data_reader(reader)

# 设置消息队列处理器
mq_processor = MessageQueueProcessor(your_mq_producer)
engine.set_processor(mq_processor)

# 运行回放
import asyncio
stats = asyncio.run(engine.run())
print(f"Completed: {stats}")
```

### 命令行工具

```bash
# 使用默认配置运行
kunpeng-replay --mode event_driven --events 1000

# 使用配置文件运行
kunpeng-replay --config config.json
```

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    ReplayEngine                             │
├─────────────────────────────────────────────────────────────┤
│                EventDrivenTimeController                    │
├─────────────────────────────────────────────────────────────┤
│  BaseDataReader  │  BaseEventProcessor  │  BaseEvent       │
├─────────────────────────────────────────────────────────────┤
│  KunpengReader   │  MessageQueueProc    │  MarketDataEvent │
└─────────────────────────────────────────────────────────────┘
```

## 配置选项

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `mode` | ReplayMode | CONTINUOUS | 回放模式 |
| `tradingday` | str | "20250218" | 交易日 |
| `start_time` | str | "09:23:00" | 开始时间 |
| `end_time` | str | "15:00:00" | 结束时间 |
| `event_processing_timeout` | int | 10 | 事件处理超时(秒) |
| `progress_bar` | bool | True | 显示进度条 |

## 许可证

MIT License