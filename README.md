# 天璇特征计算框架 之 基础特征


## 特征列表


1. 

```
"limit_idx",

"bid_tvr_ed",
"bid_tvr_avg",
"bid_tvr_pmr",
"bid_tvr_navg",
"ask_tvr_ed",
"ask_tvr_avg",
"ask_tvr_pmr",
"ask_tvr_navg",
"bid_tr_ed",
"bid_tr_avg",
"bid_tr_pmr",
"bid_tr_navg",
"ask_tr_ed",
"ask_tr_avg",
"ask_tr_pmr",
"ask_tr_navg",
"ba_tvr_diff_ed",
"ba_tvr_diff_avg",
"ba_tvr_diff_pmr",
"ba_tvr_diff_navg",
"ba_tr_diff_ed",
"ba_tr_diff_avg",
"ba_tr_diff_pmr",
"ba_tr_diff_navg",
"ba_tvr_imba_ed",
"ba_tvr_imba_avg",
"ba_tvr_imba_pmr",
"ba_tvr_imba_navg",
"ba_wp_imba_ed",
"ba_wp_imba_avg",
"ba_wp_imba_pmr",
"ba_wp_imba_navg",
"bid_wp_imba_ed",
"bid_wp_imba_avg",
"bid_wp_imba_pmr",
"bid_wp_imba_navg",
"ask_wp_imba_ed",
"ask_wp_imba_avg",
"ask_wp_imba_pmr",
"ask_wp_imba_navg",


"mp2pre_cp_ed",
"mp2pre_cp_navg",
"mp2op_ed",
"mp2op_navg",
"mp2vwap_ed",
"mp2vwap_navg",
"mp2twap_ed",
"mp2twap_navg",
"avg_p2mp_lag_ed",
"avg_p2mp_lag_navg",
"mp2pre_hp_ed",
"mp2pre_lp_ed",
"mp2hp_ed",
"mp2lp_ed",
"mp_up_ret",
"mp_down_ret",
"mp_up_ret_ptk",
"mp_down_ret_ptk",
"mp_trend",
"mp_ud_ret_ptk",
"mp_ret_std",
"mp_vol_corr",

"voi_ed",
"voi_navg",
"voi2vol_ed",
"voi2vol_navg",

"vol_pct_ed",
"vol_pct_navg",

"bid_lv5_pct_ed",
"bid_lv5_pct_navg",
"ask_lv5_pct_ed",
"ask_lv5_pct_navg",
"bid_lv10_pct_ed",
"bid_lv10_pct_navg",
"ask_lv10_pct_ed",
"ask_lv10_pct_navg",


"mp_ed2mp_st",
"mp_ed2mp_st_navg",
"mp_range",
"mp_range_navg",
"mp_arpp",
"mp_arpp_navg",
"mp_ed2pre_cp",
"mp_ed2pre_hp",
"mp_ed2pre_lp",
"mp_ret_ud",
"mp_ret_upcnt_pct",
"mp_trendstr",
"mp_ret_upstd_pct",
"mp_ret_skew",
"mp_ret_kurt",
"mp_pvt_corr",


"tech_v2_crsi5&3&60",
"tech_v2_crsi5&3&60_15s",
"tech_v2_crsi5&3&60_60s",
"tech_v2_crsi5&3&60_600s",
"tech_v2_crsi5&3&60_1800s",
"tech_v2_pnvi_cor20",
"tech_v2_pnvi_cor20_15s",
"tech_v2_pnvi_cor20_60s",
"tech_v2_pnvi_cor20_600s",
"tech_v2_pnvi_cor20_1800s",
"tech_v2_dhilo90",
"tech_v2_dhilo90_15s",
"tech_v2_dhilo90_60s",
"tech_v2_dhilo90_600s",
"tech_v2_dhilo90_1800s",
"tech_v2_stdamb10_tvr_cor20",
"tech_v2_stdamb10_tvr_cor20_15s",
"tech_v2_stdamb10_tvr_cor20_60s",
"tech_v2_stdamb10_tvr_cor20_600s",
"tech_v2_stdamb10_tvr_cor20_1800s",
"tech_v2_lstdamb10_tvr_ratio20",
"tech_v2_lstdamb10_tvr_ratio20_15s",
"tech_v2_lstdamb10_tvr_ratio20_60s",
"tech_v2_lstdamb10_tvr_ratio20_600s",
"tech_v2_lstdamb10_tv_ratio20_dif_600s",
"tech_v2_lstdamb10_tv_ratio20_dif_1800s",
"tech_v2_aroon_up27",
"tech_v2_aroon_up27_15s",
"tech_v2_aroon_up27_60s",
"tech_v2_aroon_up27_600s",
"tech_v2_aroon_up27_1800s",
"tech_v2_aroon_down27",
"tech_v2_aroon_down27_15s",
"tech_v2_aroon_down27_60s",
"tech_v2_aroon_down27_600s",
"tech_v2_aroon_down27_1800s",
"tech_v2_aroon27",
"tech_v2_aroon27_15s",
"tech_v2_aroon27_60s",
"tech_v2_aroon27_600s",
"tech_v2_aroon27_1800s",
"tech_v2_dbcd5&16&17",
"tech_v2_dbcd5&16&17_15s",
"tech_v2_dbcd5&16&17_60s",
"tech_v2_dbcd5&16&17_600s",
"tech_v2_dbcd5&16&17_1800s",
"tech_v2_tbias10",
"tech_v2_tbias10_15s",
"tech_v2_tbias10_60s",
"tech_v2_tbias10_600s",
"tech_v2_tbias10_1800s",
"tech_v2_apb10",
"tech_v2_apb10_15s",
"tech_v2_apb10_60s",
"tech_v2_apb10_600s",
"tech_v2_apb10_1800s",
"tech_v2_tbias60",
"tech_v2_tbias60_15s",
"tech_v2_tbias60_60s",
"tech_v2_tbias60_600s",
"tech_v2_tbias60_1800s",
"tech_v2_apb60",
"tech_v2_apb60_15s",
"tech_v2_apb60_60s",
"tech_v2_apb60_600s",
"tech_v2_apb60_1800s",
"tech_v2_srmi5",
"tech_v2_srmi5_15s",
"tech_v2_srmi5_60s",
"tech_v2_srmi5_600s",
"tech_v2_srmi5_1800s",
"tech_v2_srmi20",
"tech_v2_srmi20_15s",
"tech_v2_srmi20_60s",
"tech_v2_srmi20_600s",
"tech_v2_srmi20_1800s",
"tech_v2_srmi60",
"tech_v2_srmi60_15s",
"tech_v2_srmi60_60s",
"tech_v2_srmi60_600s",
"tech_v2_srmi60_1800s",
"tech_v2_srmio5&20",
"tech_v2_srmio5&20_15s",
"tech_v2_srmio5&20_60s",
"tech_v2_srmio5&20_600s",
"tech_v2_srmio5&20_1800s",
"tech_v2_srmio5&60",
"tech_v2_srmio5&60_15s",
"tech_v2_srmio5&60_60s",
"tech_v2_srmio5&60_600s",
"tech_v2_srmio5&60_1800s",
"tech_v2_rvol10",
"tech_v2_rvol10_15s",
"tech_v2_rvol10_60s",
"tech_v2_rvol10_600s",
"tech_v2_rvol10_1800s",
"tech_v2_rvol60",
"tech_v2_rvol60_15s",
"tech_v2_rvol60_60s",
"tech_v2_rvol60_600s",
"tech_v2_rvol60_1800s",
"tech_v2_tr120",
"tech_v2_tr120_15s",
"tech_v2_tr120_60s",
"tech_v2_tr120_600s",
"tech_v2_tr120_1800s",
"tech_v2_tro5&20",
"tech_v2_tro5&20_15s",
"tech_v2_tro5&20_60s",
"tech_v2_tro5&20_600s",
"tech_v2_tro5&20_1800s",
"tech_v2_tro5&60",
"tech_v2_tro5&60_15s",
"tech_v2_tro5&60_60s",
"tech_v2_tro5&60_600s",
"tech_v2_tro5&60_1800s",
"tech_v2_tvrshp5",
"tech_v2_tvrshp5_15s",
"tech_v2_tvrshp5_60s",
"tech_v2_tvrshp5_600s",
"tech_v2_tvrshp5_1800s",
"tech_v2_tvrshp20",
"tech_v2_tvrshp20_15s",
"tech_v2_tvrshp20_60s",
"tech_v2_tvrshp20_600s",
"tech_v2_tvrshp20_1800s",
"tech_v2_si_ewa5",
"tech_v2_si_ewa5_15s",
"tech_v2_si_ewa5_60s",
"tech_v2_si_ewa5_600s",
"tech_v2_si_ewa5_1800s",
"tech_v2_cko3&10",
"tech_v2_cko3&10_15s",
"tech_v2_cko3&10_60s",
"tech_v2_cko3&10_600s",
"tech_v2_cko3&10_1800s",
"tech_v2_kvo34&55",
"tech_v2_kvo34&55_15s",
"tech_v2_kvo34&55_60s",
"tech_v2_kvo34&55_600s",
"tech_v2_kvo34&55_1800s",
"tech_v2_forceindex10",
"tech_v2_forceindex10_15s",
"tech_v2_forceindex10_60s",
"tech_v2_forceindex10_600s",
"tech_v2_forceindex10_1800s",
"tech_v2_bop10",
"tech_v2_bop10_15s",
"tech_v2_bop10_60s",
"tech_v2_bop10_600s",
"tech_v2_bop10_1800s",
"tech_v2_cmo10",
"tech_v2_cmo10_15s",
"tech_v2_cmo10_60s",
"tech_v2_cmo10_600s",
"tech_v2_cmo10_1800s",
"tech_v2_rsi20",
"tech_v2_rsi20_15s",
"tech_v2_rsi20_60s",
"tech_v2_rsi20_600s",
"tech_v2_rsi20_1800s",
"tech_v2_srsi20",
"tech_v2_srsi20_15s",
"tech_v2_srsi20_60s",
"tech_v2_srsi20_600s",
"tech_v2_srsi20_1800s",
"tech_v2_cmb27&9&5&3",
"tech_v2_cmb27&9&5&3_15s",
"tech_v2_cmb27&9&5&3_60s",
"tech_v2_cmb27&9&5&3_600s",
"tech_v2_cmb27&9&5&3_1800s",
"tech_v2_rvi10&20",
"tech_v2_rvi10&20_15s",
"tech_v2_rvi10&20_60s",
"tech_v2_rvi10&20_600s",
"tech_v2_rvi10&20_1800s",
"tech_v2_mfi10",
"tech_v2_mfi10_15s",
"tech_v2_mfi10_60s",
"tech_v2_mfi10_600s",
"tech_v2_mfi10_1800s",
"tech_v2_vr10",
"tech_v2_vr10_15s",
"tech_v2_vr10_60s",
"tech_v2_vr10_600s",
"tech_v2_vr10_1800s",
"tech_v2_ar10",
"tech_v2_ar10_15s",
"tech_v2_ar10_60s",
"tech_v2_ar10_600s",
"tech_v2_ar10_1800s",
"tech_v2_br10",
"tech_v2_br10_15s",
"tech_v2_br10_60s",
"tech_v2_br10_600s",
"tech_v2_br10_1800s",
"tech_v2_arbr10",
"tech_v2_arbr10_15s",
"tech_v2_arbr10_60s",
"tech_v2_arbr10_600s",
"tech_v2_arbr10_1800s",
"tech_v2_cr10",
"tech_v2_cr10_15s",
"tech_v2_cr10_60s",
"tech_v2_cr10_600s",
"tech_v2_cr10_1800s",
"tech_v2_ddi10",
"tech_v2_ddi10_15s",
"tech_v2_ddi10_60s",
"tech_v2_ddi10_600s",
"tech_v2_ddi10_1800s",
"tech_v2_adtm10",
"tech_v2_adtm10_15s",
"tech_v2_adtm10_60s",
"tech_v2_adtm10_600s",
"tech_v2_adtm10_1800s",
"tech_v2_cmf10",
"tech_v2_cmf10_15s",
"tech_v2_cmf10_60s",
"tech_v2_cmf10_600s",
"tech_v2_cmf10_1800s",
"tech_v2_acd10",
"tech_v2_acd10_15s",
"tech_v2_acd10_60s",
"tech_v2_acd10_600s",
"tech_v2_acd10_1800s",
"tech_v2_dev5",
"tech_v2_dev5_15s",
"tech_v2_dev5_60s",
"tech_v2_dev5_600s",
"tech_v2_dev5_1800s",
"tech_v2_cci5",
"tech_v2_cci5_15s",
"tech_v2_cci5_60s",
"tech_v2_cci5_600s",
"tech_v2_cci5_1800s",
"tech_v2_dev60",
"tech_v2_dev60_15s",
"tech_v2_dev60_60s",
"tech_v2_dev60_600s",
"tech_v2_dev60_1800s",
"tech_v2_cci60",
"tech_v2_cci60_15s",
"tech_v2_cci60_60s",
"tech_v2_cci60_600s",
"tech_v2_cci60_1800s",
"tech_v2_tsi20&10",
"tech_v2_tsi20&10_15s",
"tech_v2_tsi20&10_60s",
"tech_v2_tsi20&10_600s",
"tech_v2_tsi20&10_1800s",
"tech_v2_pvi",
"tech_v2_pvi_15s",
"tech_v2_pvi_60s",
"tech_v2_pvi_600s",
"tech_v2_pvi_1800s",
"tech_v2_nvi",
"tech_v2_nvi_15s",
"tech_v2_nvi_60s",
"tech_v2_nvi_600s",
"tech_v2_nvi_1800s",
"tech_v2_atrp20",
"tech_v2_atrp20_15s",
"tech_v2_atrp20_60s",
"tech_v2_atrp20_600s",
"tech_v2_atrp20_1800s",
"tech_v2_plusdi20",
"tech_v2_plusdi20_15s",
"tech_v2_plusdi20_60s",
"tech_v2_plusdi20_600s",
"tech_v2_plusdi20_1800s",
"tech_v2_minusdi20",
"tech_v2_minusdi20_15s",
"tech_v2_minusdi20_60s",
"tech_v2_minusdi20_600s",
"tech_v2_minusdi20_1800s",
"tech_v2_adxr20",
"tech_v2_adxr20_15s",
"tech_v2_adxr20_60s",
"tech_v2_adxr20_600s",
"tech_v2_adxr20_1800s",
"tech_v2_massindex9&25",
"tech_v2_massindex9&25_15s",
"tech_v2_massindex9&25_60s",
"tech_v2_massindex9&25_600s",
"tech_v2_massindex9&25_1800s",
"tech_v2_ulcer10",
"tech_v2_ulcer10_15s",
"tech_v2_ulcer10_60s",
"tech_v2_ulcer10_600s",
"tech_v2_ulcer10_1800s",
"tech_v2_emv10",
"tech_v2_emv10_15s",
"tech_v2_emv10_60s",
"tech_v2_emv10_600s",
"tech_v2_emv10_1800s",
"tech_v2_ckv10",
"tech_v2_ckv10_15s",
"tech_v2_ckv10_60s",
"tech_v2_ckv10_600s",
"tech_v2_ckv10_1800s",
"tech_v2_stdamb10",
"tech_v2_stdamb10_15s",
"tech_v2_stdamb10_60s",
"tech_v2_stdamb10_600s",
"tech_v2_stdamb10_1800s",
"tech_v2_pvtx10",
"tech_v2_pvtx10_15s",
"tech_v2_pvtx10_60s",
"tech_v2_pvtx10_600s",
"tech_v2_pvtx10_1800s",
"tech_v2_nvtx10",
"tech_v2_nvtx10_15s",
"tech_v2_nvtx10_60s",
"tech_v2_nvtx10_600s",
"tech_v2_nvtx10_1800s",
"tech_v2_vtx10",
"tech_v2_vtx10_15s",
"tech_v2_vtx10_60s",
"tech_v2_vtx10_600s",
"tech_v2_vtx10_1800s",
"tech_v2_trix10&9",
"tech_v2_trix10&9_15s",
"tech_v2_trix10&9_60s",
"tech_v2_trix10&9_600s",
"tech_v2_trix10&9_1800s",
"tech_v2_plrc10",
"tech_v2_plrc10_15s",
"tech_v2_plrc10_60s",
"tech_v2_plrc10_600s",
"tech_v2_plrc10_1800s",
"tech_v2_macd12&26&9",
"tech_v2_macd12&26&9_15s",
"tech_v2_macd12&26&9_60s",
"tech_v2_macd12&26&9_600s",
"tech_v2_macd12&26&9_1800s",
"tech_v2_vmacd12&26&9",
"tech_v2_vmacd12&26&9_15s",
"tech_v2_vmacd12&26&9_60s",
"tech_v2_vmacd12&26&9_600s",
"tech_v2_vmacd12&26&9_1800s",
"tech_v2_dpo10",
"tech_v2_dpo10_15s",
"tech_v2_dpo10_60s",
"tech_v2_dpo10_600s",
"tech_v2_dpo10_1800s",
"tech_v2_bb_loc10&1.5",
"tech_v2_bb_loc10&1.5_15s",
"tech_v2_bb_loc10&1.5_60s",
"tech_v2_bb_loc10&1.5_600s",
"tech_v2_bb_loc10&1.5_1800s",
"tech_v2_bb_width10&1.5",
"tech_v2_bb_width10&1.5_15s",
"tech_v2_bb_width10&1.5_60s",
"tech_v2_bb_width10&1.5_600s",
"tech_v2_bb_width10&1.5_1800s",
"tech_v2_bb_loc50&2.5",
"tech_v2_bb_loc50&2.5_15s",
"tech_v2_bb_loc50&2.5_60s",
"tech_v2_bb_loc50&2.5_600s",
"tech_v2_bb_loc50&2.5_1800s",
"tech_v2_bb_width50&2.5",
"tech_v2_bb_width50&2.5_15s",
"tech_v2_bb_width50&2.5_60s",
"tech_v2_bb_width50&2.5_600s",
"tech_v2_bb_width50&2.5_1800s",
"tech_v2_kb_loc10&1.5",
"tech_v2_kb_loc10&1.5_15s",
"tech_v2_kb_loc10&1.5_60s",
"tech_v2_kb_loc10&1.5_600s",
"tech_v2_kb_width10&1.5_600s",
"tech_v2_kb_width10&1.5_1800s",
"tech_v2_kb_loc50&2.5",
"tech_v2_kb_loc50&2.5_15s",
"tech_v2_kb_loc50&2.5_60s",
"tech_v2_kb_loc50&2.5_600s",
"tech_v2_kb_loc50&2.5_1800s",
"tech_v2_kb_width50&2.5",
"tech_v2_kb_width50&2.5_15s",
"tech_v2_kb_width50&2.5_60s",
"tech_v2_kb_width50&2.5_600s",
"tech_v2_kb_width50&2.5_1800s",
"tech_v2_squeeze20",
"tech_v2_squeeze20_15s",
"tech_v2_squeeze20_60s",
"tech_v2_squeeze20_600s",
"tech_v2_squeeze20_1800s",
"tech_v2_chdexit_long20&3",
"tech_v2_chdexit_long20&3_15s",
"tech_v2_chdexit_long20&3_60s",
"tech_v2_chdexit_long20&3_600s",
"tech_v2_chdexit_long20&3_1800s",
"tech_v2_chdexit_short20&3",
"tech_v2_chdexit_short20&3_15s",
"tech_v2_chdexit_short20&3_60s",
"tech_v2_chdexit_short20&3_600s",
"tech_v2_chdexit_short20&3_1800s",
"tech_v2_fso10",
"tech_v2_fso10_15s",
"tech_v2_fso10_60s",
"tech_v2_fso10_600s",
"tech_v2_fso10_1800s",
"tech_v2_sso10",
"tech_v2_sso10_15s",
"tech_v2_sso10_60s",
"tech_v2_sso10_600s",
"tech_v2_sso10_1800s",
"tech_v2_dsso20",
"tech_v2_dsso20_15s",
"tech_v2_dsso20_60s",
"tech_v2_dsso20_600s",
"tech_v2_dsso20_1800s",
"tech_v2_uo7&14&28",
"tech_v2_uo7&14&28_15s",
"tech_v2_uo7&14&28_60s",
"tech_v2_uo7&14&28_600s",
"tech_v2_uo7&14&28_1800s",
"tech_v2_illiq10",
"tech_v2_illiq10_15s",
"tech_v2_illiq10_60s",
"tech_v2_illiq10_600s",
"tech_v2_illiq10_1800s",
"tech_v2_kst",
"tech_v2_kst_15s",
"tech_v2_kst_60s",
"tech_v2_kst_600s",
"tech_v2_kst_1800s",




"tech_v2_obv_tz10",
"tech_v2_obv_tz10_15s",
"tech_v2_obv_tz10_60s",
"tech_v2_obv_tz10_600s",
"tech_v2_obv_tz10_1800s",
"tech_v2_pvt_tz10",
"tech_v2_pvt_tz10_15s",
"tech_v2_pvt_tz10_60s",
"tech_v2_pvt_tz10_600s",
"tech_v2_pvt_tz10_1800s",
"tech_v2_cko3&10_tz10",
"tech_v2_cko3&10_tz10_15s",
"tech_v2_cko3&10_tz10_60s",
"tech_v2_cko3&10_tz10_600s",
"tech_v2_cko3&10_tz10_1800s",
"tech_v2_kvo34&55_tz10",
"tech_v2_kvo34&55_tz10_15s",
"tech_v2_kvo34&55_tz10_60s",
"tech_v2_kvo34&55_tz10_600s",
"tech_v2_kvo34&55_tz10_1800s",
"tech_v2_wvad_tz10",
"tech_v2_wvad_tz10_15s",
"tech_v2_wvad_tz10_60s",
"tech_v2_wvad_tz10_600s",
"tech_v2_wvad_tz10_1800s",
"tech_v2_forceindex10_tz10",
"tech_v2_forceindex10_tz10_15s",
"tech_v2_forceindex10_tz10_60s",
"tech_v2_forceindex10_tz10_600s",
"tech_v2_forceindex10_tz10_1800s",
"tech_v2_bop10_tz10",
"tech_v2_bop10_tz10_15s",
"tech_v2_bop10_tz10_60s",
"tech_v2_bop10_tz10_600s",
"tech_v2_bop10_tz10_1800s",
"tech_v2_cmo10_tz10",
"tech_v2_cmo10_tz10_15s",
"tech_v2_cmo10_tz10_60s",
"tech_v2_cmo10_tz10_600s",
"tech_v2_cmo10_tz10_1800s",
"tech_v2_rsi20_tz10",
"tech_v2_rsi20_tz10_15s",
"tech_v2_rsi20_tz10_60s",
"tech_v2_rsi20_tz10_600s",
"tech_v2_rsi20_tz10_1800s",
"tech_v2_srsi20_tz10",
"tech_v2_srsi20_tz10_15s",
"tech_v2_srsi20_tz10_60s",
"tech_v2_srsi20_tz10_600s",
"tech_v2_srsi20_tz10_1800s",
"tech_v2_cmb27&9&5&3_tz10",
"tech_v2_cmb27&9&5&3_tz10_15s",
"tech_v2_cmb27&9&5&3_tz10_60s",
"tech_v2_cmb27&9&5&3_tz10_600s",
"tech_v2_cmb27&9&5&3_tz10_1800s",
"tech_v2_rvi10&20_tz10",
"tech_v2_rvi10&20_tz10_15s",
"tech_v2_rvi10&20_tz10_60s",
"tech_v2_rvi10&20_tz10_600s",
"tech_v2_rvi10&20_tz10_1800s",
"tech_v2_mfi10_tz10",
"tech_v2_mfi10_tz10_15s",
"tech_v2_mfi10_tz10_60s",
"tech_v2_mfi10_tz10_600s",
"tech_v2_mfi10_tz10_1800s",
"tech_v2_vr10_tz10",
"tech_v2_vr10_tz10_15s",
"tech_v2_vr10_tz10_60s",
"tech_v2_vr10_tz10_600s",
"tech_v2_vr10_tz10_1800s",
"tech_v2_ar10_tz10",
"tech_v2_ar10_tz10_15s",
"tech_v2_ar10_tz10_60s",
"tech_v2_ar10_tz10_600s",
"tech_v2_ar10_tz10_1800s",
"tech_v2_br10_tz10",
"tech_v2_br10_tz10_15s",
"tech_v2_br10_tz10_60s",
"tech_v2_br10_tz10_600s",
"tech_v2_br10_tz10_1800s",
"tech_v2_arbr10_tz10",
"tech_v2_arbr10_tz10_15s",
"tech_v2_arbr10_tz10_60s",
"tech_v2_arbr10_tz10_600s",
"tech_v2_arbr10_tz10_1800s",
"tech_v2_cr10_tz10",
"tech_v2_cr10_tz10_15s",
"tech_v2_cr10_tz10_60s",
"tech_v2_cr10_tz10_600s",
"tech_v2_cr10_tz10_1800s",
"tech_v2_ddi10_tz10",
"tech_v2_ddi10_tz10_15s",
"tech_v2_ddi10_tz10_60s",
"tech_v2_ddi10_tz10_600s",
"tech_v2_ddi10_tz10_1800s",
"tech_v2_adtm10_tz10",
"tech_v2_adtm10_tz10_15s",
"tech_v2_adtm10_tz10_60s",
"tech_v2_adtm10_tz10_600s",
"tech_v2_adtm10_tz10_1800s",
"tech_v2_cmf10_tz10",
"tech_v2_cmf10_tz10_15s",
"tech_v2_cmf10_tz10_60s",
"tech_v2_cmf10_tz10_600s",
"tech_v2_cmf10_tz10_1800s",
"tech_v2_acd10_tz10",
"tech_v2_acd10_tz10_15s",
"tech_v2_acd10_tz10_60s",
"tech_v2_acd10_tz10_600s",
"tech_v2_acd10_tz10_1800s",
"tech_v2_dev5_tz10",
"tech_v2_dev5_tz10_15s",
"tech_v2_dev5_tz10_60s",
"tech_v2_dev5_tz10_600s",
"tech_v2_dev5_tz10_1800s",
"tech_v2_cci5_tz10",
"tech_v2_cci5_tz10_15s",
"tech_v2_cci5_tz10_60s",
"tech_v2_cci5_tz10_600s",
"tech_v2_cci5_tz10_1800s",
"tech_v2_dev60_tz10",
"tech_v2_dev60_tz10_15s",
"tech_v2_dev60_tz10_60s",
"tech_v2_dev60_tz10_600s",
"tech_v2_dev60_tz10_1800s",
"tech_v2_cci60_tz10",
"tech_v2_cci60_tz10_15s",
"tech_v2_cci60_tz10_60s",
"tech_v2_cci60_tz10_600s",
"tech_v2_cci60_tz10_1800s",
"tech_v2_tsi20&10_tz10",
"tech_v2_tsi20&10_tz10_15s",
"tech_v2_tsi20&10_tz10_60s",
"tech_v2_tsi20&10_tz10_600s",
"tech_v2_tsi20&10_tz10_1800s",
"tech_v2_pvi_tz10",
"tech_v2_pvi_tz10_15s",
"tech_v2_pvi_tz10_60s",
"tech_v2_pvi_tz10_600s",
"tech_v2_pvi_tz10_1800s",
"tech_v2_nvi_tz10",
"tech_v2_nvi_tz10_15s",
"tech_v2_nvi_tz10_60s",
"tech_v2_nvi_tz10_600s",
"tech_v2_nvi_tz10_1800s",
"tech_v2_atrp20_tz10",
"tech_v2_atrp20_tz10_15s",
"tech_v2_atrp20_tz10_60s",
"tech_v2_atrp20_tz10_600s",
"tech_v2_atrp20_tz10_1800s",
"tech_v2_plusdi20_tz10",
"tech_v2_plusdi20_tz10_15s",
"tech_v2_plusdi20_tz10_60s",
"tech_v2_plusdi20_tz10_600s",
"tech_v2_plusdi20_tz10_1800s",
"tech_v2_minusdi20_tz10",
"tech_v2_minusdi20_tz10_15s",
"tech_v2_minusdi20_tz10_60s",
"tech_v2_minusdi20_tz10_600s",
"tech_v2_minusdi20_tz10_1800s",
"tech_v2_adxr20_tz10",
"tech_v2_adxr20_tz10_15s",
"tech_v2_adxr20_tz10_60s",
"tech_v2_adxr20_tz10_600s",
"tech_v2_adxr20_tz10_1800s",
"tech_v2_massindex9&25_tz10",
"tech_v2_massindex9&25_tz10_15s",
"tech_v2_massindex9&25_tz10_60s",
"tech_v2_massindex9&25_tz10_600s",
"tech_v2_massindex9&25_tz10_1800s",
"tech_v2_ulcer10_tz10",
"tech_v2_ulcer10_tz10_15s",
"tech_v2_ulcer10_tz10_60s",
"tech_v2_ulcer10_tz10_600s",
"tech_v2_ulcer10_tz10_1800s",
"tech_v2_emv10_tz10",
"tech_v2_emv10_tz10_15s",
"tech_v2_emv10_tz10_60s",
"tech_v2_emv10_tz10_600s",
"tech_v2_emv10_tz10_1800s",
"tech_v2_ckv10_tz10",
"tech_v2_ckv10_tz10_15s",
"tech_v2_ckv10_tz10_60s",
"tech_v2_ckv10_tz10_600s",
"tech_v2_ckv10_tz10_1800s",
"tech_v2_stdamb10_tz10",
"tech_v2_stdamb10_tz10_15s",
"tech_v2_stdamb10_tz10_60s",
"tech_v2_stdamb10_tz10_600s",
"tech_v2_stdamb10_tz10_1800s",
"tech_v2_pvtx10_tz10",
"tech_v2_pvtx10_tz10_15s",
"tech_v2_pvtx10_tz10_60s",
"tech_v2_pvtx10_tz10_600s",
"tech_v2_pvtx10_tz10_1800s",
"tech_v2_nvtx10_tz10",
"tech_v2_nvtx10_tz10_15s",
"tech_v2_nvtx10_tz10_60s",
"tech_v2_nvtx10_tz10_600s",
"tech_v2_nvtx10_tz10_1800s",
"tech_v2_vtx10_tz10",
"tech_v2_vtx10_tz10_15s",
"tech_v2_vtx10_tz10_60s",
"tech_v2_vtx10_tz10_600s",
"tech_v2_vtx10_tz10_1800s",
"tech_v2_trix10&9_tz10",
"tech_v2_trix10&9_tz10_15s",
"tech_v2_trix10&9_tz10_60s",
"tech_v2_trix10&9_tz10_600s",
"tech_v2_trix10&9_tz10_1800s",
"tech_v2_plrc10_tz10",
"tech_v2_plrc10_tz10_15s",
"tech_v2_plrc10_tz10_60s",
"tech_v2_plrc10_tz10_600s",
"tech_v2_plrc10_tz10_1800s",
"tech_v2_macd12&26&9_tz10",
"tech_v2_macd12&26&9_tz10_15s",
"tech_v2_macd12&26&9_tz10_60s",
"tech_v2_macd12&26&9_tz10_600s",
"tech_v2_macd12&26&9_tz10_1800s",
"tech_v2_vmacd12&26&9_tz10",
"tech_v2_vmacd12&26&9_tz10_15s",
"tech_v2_vmacd12&26&9_tz10_60s",
"tech_v2_vmacd12&26&9_tz10_600s",
"tech_v2_vmacd12&26&9_tz10_1800s",
"tech_v2_dpo10_tz10",
"tech_v2_dpo10_tz10_15s",
"tech_v2_dpo10_tz10_60s",
"tech_v2_dpo10_tz10_600s",
"tech_v2_dpo10_tz10_1800s",
"tech_v2_bb_loc10&1.5_tz10",
"tech_v2_bb_loc10&1.5_tz10_15s",
"tech_v2_bb_loc10&1.5_tz10_60s",
"tech_v2_bb_loc10&1.5_tz10_600s",
"tech_v2_bb_loc10&1.5_tz10_1800s",
"tech_v2_bb_width10&1.5_tz10",
"tech_v2_bb_width10&1.5_tz10_15s",
"tech_v2_bb_width10&1.5_tz10_60s",
"tech_v2_bb_width10&1.5_tz10_600s",
"tech_v2_bb_width10&1.5_tz10_1800s",
"tech_v2_bb_loc50&2.5_tz10",
"tech_v2_bb_loc50&2.5_tz10_15s",
"tech_v2_bb_loc50&2.5_tz10_60s",
"tech_v2_bb_loc50&2.5_tz10_600s",
"tech_v2_bb_loc50&2.5_tz10_1800s",
"tech_v2_bb_width50&2.5_tz10",
"tech_v2_bb_width50&2.5_tz10_15s",
"tech_v2_bb_width50&2.5_tz10_60s",
"tech_v2_bb_width50&2.5_tz10_600s",
"tech_v2_bb_width50&2.5_tz10_1800s",
"tech_v2_kb_loc10&1.5_tz10",
"tech_v2_kb_loc10&1.5_tz10_15s",
"tech_v2_kb_loc10&1.5_tz10_60s",
"tech_v2_kb_loc10&1.5_tz10_600s",
"tech_v2_kb_loc10&1.5_tz10_1800s",
"tech_v2_kb_width10&1.5_tz10",
"tech_v2_kb_width10&1.5_tz10_15s",
"tech_v2_kb_width10&1.5_tz10_60s",
"tech_v2_kb_width10&1.5_tz10_600s",
"tech_v2_kb_width10&1.5_tz10_1800s",
"tech_v2_kb_loc50&2.5_tz10",
"tech_v2_kb_loc50&2.5_tz10_15s",
"tech_v2_kb_loc50&2.5_tz10_60s",
"tech_v2_kb_loc50&2.5_tz10_600s",
"tech_v2_kb_loc50&2.5_tz10_1800s",
"tech_v2_kb_width50&2.5_tz10",
"tech_v2_kb_width50&2.5_tz10_15s",
"tech_v2_kb_width50&2.5_tz10_60s",
"tech_v2_kb_width50&2.5_tz10_600s",
"tech_v2_kb_width50&2.5_tz10_1800s",
"tech_v2_squeeze20_tz10",
"tech_v2_squeeze20_tz10_15s",
"tech_v2_squeeze20_tz10_60s",
"tech_v2_squeeze20_tz10_600s",
"tech_v2_squeeze20_tz10_1800s",
"tech_v2_chdexit_long20&3_tz10",
"tech_v2_chdexit_long20&3_tz10_15s",
"tech_v2_chdexit_long20&3_tz10_60s",
"tech_v2_chdexit_long20&3_tz10_600s",
"tech_v2_chdexit_long20&3_tz10_1800s",
"tech_v2_chdexit_short20&3_tz10",
"tech_v2_chdexit_short20&3_tz10_15s",
"tech_v2_chdexit_short20&3_tz10_60s",
"tech_v2_chdexit_short20&3_tz10_600s",
"tech_v2_chdexit_short20&3_tz10_1800s",
"tech_v2_fso10_tz10",
"tech_v2_fso10_tz10_15s",
"tech_v2_fso10_tz10_60s",
"tech_v2_fso10_tz10_600s",
"tech_v2_fso10_tz10",
"tech_v2_sso10_tz10",
"tech_v2_sso10_tz10_15s",
"tech_v2_sso10_tz10_60s",
"tech_v2_sso10_tz10_600s",
"tech_v2_sso10_tz10_1800s",
"tech_v2_dsso20_tz10",
"tech_v2_dsso20_tz10_15s",
"tech_v2_dsso20_tz10_60s",
"tech_v2_dsso20_tz10_600s",
"tech_v2_dsso20_tz10_1800s",
"tech_v2_uo7&14&28_tz10",
"tech_v2_uo7&14&28_tz10_15s",
"tech_v2_uo7&14&28_tz10_60s",
"tech_v2_uo7&14&28_tz10_600s",
"tech_v2_uo7&14&28_tz10_1800s",
"tech_v2_illiq10_tz10",
"tech_v2_illiq10_tz10_15s",
"tech_v2_illiq10_tz10_60s",
"tech_v2_illiq10_tz10_600s",
"tech_v2_illiq10_tz10_1800s",
"tech_v2_kst_tz10",
"tech_v2_kst_tz10_15s",
"tech_v2_kst_tz10_60s",
"tech_v2_kst_tz10_600s",
"tech_v2_kst_tz10_1800s",




"tech_v2_adj_cp_tslope10",
"tech_v2_adj_cp_tslope10_15s",
"tech_v2_adj_cp_tslope10_60s",
"tech_v2_adj_cp_tslope10_600s",
"tech_v2_adj_cp_tslope10_1800s",
"tech_v2_obv_tslope10",
"tech_v2_obv_tslope10_15s",
"tech_v2_obv_tslope10_60s",
"tech_v2_obv_tslope10_600s",
"tech_v2_obv_tslope10_1800s",
"tech_v2_pvt_tslope10",
"tech_v2_pvt_tslope10_15s",
"tech_v2_pvt_tslope10_60s",
"tech_v2_pvt_tslope10_600s",
"tech_v2_pvt_tslope10_1800s",
"tech_v2_cko3&10_tslope10",
"tech_v2_cko3&10_tslope10_15s",
"tech_v2_cko3&10_tslope10_60s",
"tech_v2_cko3&10_tslope10_600s",
"tech_v2_cko3&10_tslope10_1800s",
"tech_v2_kvo34&55_tslope10",
"tech_v2_kvo34&55_tslope10_15s",
"tech_v2_kvo34&55_tslope10_60s",
"tech_v2_kvo34&55_tslope10_600s",
"tech_v2_kvo34&55_tslope10_1800s",
"tech_v2_wvad_tslope10",
"tech_v2_wvad_tslope10_15s",
"tech_v2_wvad_tslope10_60s",
"tech_v2_wvad_tslope10_600s",
"tech_v2_wvad_tslope10_1800s",
"tech_v2_forceindex10_tslope10",
"tech_v2_forceindex10_tslope10_15s",
"tech_v2_forceindex10_tslope10_60s",
"tech_v2_forceindex10_tslope10_600s",
"tech_v2_forceindex10_tslope10_1800s",
"tech_v2_bop10_tslope10",
"tech_v2_bop10_tslope10_15s",
"tech_v2_bop10_tslope10_60s",
"tech_v2_bop10_tslope10_600s",
"tech_v2_bop10_tslope10_1800s",
"tech_v2_cmo10_tslope10",
"tech_v2_cmo10_tslope10_15s",
"tech_v2_cmo10_tslope10_60s",
"tech_v2_cmo10_tslope10_600s",
"tech_v2_cmo10_tslope10_1800s",
"tech_v2_rsi20_tslope10",
"tech_v2_rsi20_tslope10_15s",
"tech_v2_rsi20_tslope10_60s",
"tech_v2_rsi20_tslope10_600s",
"tech_v2_rsi20_tslope10_1800s",
"tech_v2_srsi20_tslope10",
"tech_v2_srsi20_tslope10_15s",
"tech_v2_srsi20_tslope10_60s",
"tech_v2_srsi20_tslope10_600s",
"tech_v2_srsi20_tslope10_1800s",
"tech_v2_cmb27&9&5&3_tslope10",
"tech_v2_cmb27&9&5&3_tslope10_15s",
"tech_v2_cmb27&9&5&3_tslope10_60s",
"tech_v2_cmb27&9&5&3_tslope10_600s",
"tech_v2_cmb27&9&5&3_tslope10_1800s",
"tech_v2_rvi10&20_tslope10",
"tech_v2_rvi10&20_tslope10_15s",
"tech_v2_rvi10&20_tslope10_60s",
"tech_v2_rvi10&20_tslope10_600s",
"tech_v2_rvi10&20_tslope10_1800s",
"tech_v2_mfi10_tslope10",
"tech_v2_mfi10_tslope10_15s",
"tech_v2_mfi10_tslope10_60s",
"tech_v2_mfi10_tslope10_600s",
"tech_v2_mfi10_tslope10_1800s",
"tech_v2_vr10_tslope10",
"tech_v2_vr10_tslope10_15s",
"tech_v2_vr10_tslope10_60s",
"tech_v2_vr10_tslope10_600s",
"tech_v2_vr10_tslope10_1800s",
"tech_v2_ar10_tslope10",
"tech_v2_ar10_tslope10_15s",
"tech_v2_ar10_tslope10_60s",
"tech_v2_ar10_tslope10_600s",
"tech_v2_ar10_tslope10_1800s",
"tech_v2_br10_tslope10",
"tech_v2_br10_tslope10_15s",
"tech_v2_br10_tslope10_60s",
"tech_v2_br10_tslope10_600s",
"tech_v2_br10_tslope10_1800s",
"tech_v2_arbr10_tslope10",
"tech_v2_arbr10_tslope10_15s",
"tech_v2_arbr10_tslope10_60s",
"tech_v2_arbr10_tslope10_600s",
"tech_v2_arbr10_tslope10_1800s",
"tech_v2_cr10_tslope10",
"tech_v2_cr10_tslope10_15s",
"tech_v2_cr10_tslope10_60s",
"tech_v2_cr10_tslope10_600s",
"tech_v2_cr10_tslope10_1800s",
"tech_v2_ddi10_tslope10",
"tech_v2_ddi10_tslope10_15s",
"tech_v2_ddi10_tslope10_60s",
"tech_v2_ddi10_tslope10_600s",
"tech_v2_ddi10_tslope10_1800s",
"tech_v2_adtm10_tslope10",
"tech_v2_adtm10_tslope10_15s",
"tech_v2_adtm10_tslope10_60s",
"tech_v2_adtm10_tslope10_600s",
"tech_v2_adtm10_tslope10_1800s",
"tech_v2_cmf10_tslope10",
"tech_v2_cmf10_tslope10_15s",
"tech_v2_cmf10_tslope10_60s",
"tech_v2_cmf10_tslope10_600s",
"tech_v2_cmf10_tslope10_1800s",
"tech_v2_acd10_tslope10",
"tech_v2_acd10_tslope10_15s",
"tech_v2_acd10_tslope10_60s",
"tech_v2_acd10_tslope10_600s",
"tech_v2_acd10_tslope10_1800s",
"tech_v2_dev5_tslope10",
"tech_v2_dev5_tslope10_15s",
"tech_v2_dev5_tslope10_60s",
"tech_v2_dev5_tslope10_600s",
"tech_v2_dev5_tslope10_1800s",
"tech_v2_cci5_tslope10",
"tech_v2_cci5_tslope10_15s",
"tech_v2_cci5_tslope10_60s",
"tech_v2_cci5_tslope10_600s",
"tech_v2_cci5_tslope10_1800s",
"tech_v2_dev60_tslope10",
"tech_v2_dev60_tslope10_15s",
"tech_v2_dev60_tslope10_60s",
"tech_v2_dev60_tslope10_600s",
"tech_v2_dev60_tslope10_1800s",
"tech_v2_cci60_tslope10",
"tech_v2_cci60_tslope10_15s",
"tech_v2_cci60_tslope10_60s",
"tech_v2_cci60_tslope10_600s",
"tech_v2_cci60_tslope10_1800s",
"tech_v2_tsi20&10_tslope10",
"tech_v2_tsi20&10_tslope10_15s",
"tech_v2_tsi20&10_tslope10_60s",
"tech_v2_tsi20&10_tslope10_600s",
"tech_v2_tsi20&10_tslope10_1800s",
"tech_v2_pvi_tslope10",
"tech_v2_pvi_tslope10_15s",
"tech_v2_pvi_tslope10_60s",
"tech_v2_pvi_tslope10_600s",
"tech_v2_pvi_tslope10_1800s",
"tech_v2_nvi_tslope10",
"tech_v2_nvi_tslope10_15s",
"tech_v2_nvi_tslope10_60s",
"tech_v2_nvi_tslope10_600s",
"tech_v2_nvi_tslope10_1800s",
"tech_v2_atrp20_tslope10",
"tech_v2_atrp20_tslope10_15s",
"tech_v2_atrp20_tslope10_60s",
"tech_v2_atrp20_tslope10_600s",
"tech_v2_atrp20_tslope10_1800s",
"tech_v2_plusdi20_tslope10",
"tech_v2_plusdi20_tslope10_15s",
"tech_v2_plusdi20_tslope10_60s",
"tech_v2_plusdi20_tslope10_600s",
"tech_v2_plusdi20_tslope10_1800s",
"tech_v2_minusdi20_tslope10",
"tech_v2_minusdi20_tslope10_15s",
"tech_v2_minusdi20_tslope10_60s",
"tech_v2_minusdi20_tslope10_600s",
"tech_v2_minusdi20_tslope10_1800s",
"tech_v2_adxr20_tslope10",
"tech_v2_adxr20_tslope10_15s",
"tech_v2_adxr20_tslope10_60s",
"tech_v2_adxr20_tslope10_600s",
"tech_v2_adxr20_tslope10_1800s",
"tech_v2_massindex9&25_tslope10",
"tech_v2_massindex9&25_tslope10_15s",
"tech_v2_massindex9&25_tslope10_60s",
"tech_v2_massindex9&25_tslope10_600s",
"tech_v2_massindex9&25_tslope10_1800s",
"tech_v2_ulcer10_tslope10",
"tech_v2_ulcer10_tslope10_15s",
"tech_v2_ulcer10_tslope10_60s",
"tech_v2_ulcer10_tslope10_600s",
"tech_v2_ulcer10_tslope10_1800s",
"tech_v2_emv10_tslope10",
"tech_v2_emv10_tslope10_15s",
"tech_v2_emv10_tslope10_60s",
"tech_v2_emv10_tslope10_600s",
"tech_v2_emv10_tslope10_1800s",
"tech_v2_ckv10_tslope10",
"tech_v2_ckv10_tslope10_15s",
"tech_v2_ckv10_tslope10_60s",
"tech_v2_ckv10_tslope10_600s",
"tech_v2_ckv10_tslope10_1800s",
"tech_v2_stdamb10_tslope10",
"tech_v2_stdamb10_tslope10_15s",
"tech_v2_stdamb10_tslope10_60s",
"tech_v2_stdamb10_tslope10_600s",
"tech_v2_stdamb10_tslope10_1800s",
"tech_v2_pvtx10_tslope10",
"tech_v2_pvtx10_tslope10_15s",
"tech_v2_pvtx10_tslope10_60s",
"tech_v2_pvtx10_tslope10_600s",
"tech_v2_pvtx10_tslope10_1800s",
"tech_v2_nvtx10_tslope10",
"tech_v2_nvtx10_tslope10_15s",
"tech_v2_nvtx10_tslope10_60s",
"tech_v2_nvtx10_tslope10_600s",
"tech_v2_nvtx10_tslope10_1800s",
"tech_v2_vtx10_tslope10",
"tech_v2_vtx10_tslope10_15s",
"tech_v2_vtx10_tslope10_60s",
"tech_v2_vtx10_tslope10_600s",
"tech_v2_vtx10_tslope10_1800s",
"tech_v2_trix10&9_tslope10",
"tech_v2_trix10&9_tslope10_15s",
"tech_v2_trix10&9_tslope10_60s",
"tech_v2_trix10&9_tslope10_600s",
"tech_v2_trix10&9_tslope10_1800s",
"tech_v2_plrc10_tslope10",
"tech_v2_plrc10_tslope10_15s",
"tech_v2_plrc10_tslope10_60s",
"tech_v2_plrc10_tslope10_600s",
"tech_v2_plrc10_tslope10_1800s",
"tech_v2_macd12&26&9_tslope10",
"tech_v2_macd12&26&9_tslope10_15s",
"tech_v2_macd12&26&9_tslope10_60s",
"tech_v2_macd12&26&9_tslope10_600s",
"tech_v2_macd12&26&9_tslope10_1800s",
"tech_v2_vmacd12&26&9_tslope10",
"tech_v2_vmacd12&26&9_tslope10_15s",
"tech_v2_vmacd12&26&9_tslope10_60s",
"tech_v2_vmacd12&26&9_tslope10_600s",
"tech_v2_vmacd12&26&9_tslope10_1800s",
"tech_v2_dpo10_tslope10",
"tech_v2_dpo10_tslope10_15s",
"tech_v2_dpo10_tslope10_60s",
"tech_v2_dpo10_tslope10_600s",
"tech_v2_dpo10_tslope10_1800s",
"tech_v2_bb_loc10&1.5_tslope10",
"tech_v2_bb_loc10&1.5_tslope10_15s",
"tech_v2_bb_loc10&1.5_tslope10_60s",
"tech_v2_bb_loc10&1.5_tslope10_600s",
"tech_v2_bb_loc10&1.5_tslope10_1800s",
"tech_v2_bb_width10&1.5_tslope10",
"tech_v2_bb_width10&1.5_tslope10_15s",
"tech_v2_bb_width10&1.5_tslope10_60s",
"tech_v2_bb_width10&1.5_tslope10_600s",
"tech_v2_bb_width10&1.5_tslope10_1800s",
"tech_v2_bb_loc50&2.5_tslope10",
"tech_v2_bb_loc50&2.5_tslope10_15s",
"tech_v2_bb_loc50&2.5_tslope10_60s",
"tech_v2_bb_loc50&2.5_tslope10_600s",
"tech_v2_bb_loc50&2.5_tslope10_1800s",
"tech_v2_bb_width50&2.5_tslope10",
"tech_v2_bb_width50&2.5_tslope10_15s",
"tech_v2_bb_width50&2.5_tslope10_60s",
"tech_v2_bb_width50&2.5_tslope10_600s",
"tech_v2_bb_width50&2.5_tslope10_1800s",
"tech_v2_kb_loc10&1.5_tslope10",
"tech_v2_kb_loc10&1.5_tslope10_15s",
"tech_v2_kb_loc10&1.5_tslope10_60s",
"tech_v2_kb_loc10&1.5_tslope10_600s",
"tech_v2_kb_loc10&1.5_tslope10_1800s",
"tech_v2_kb_width10&1.5_tslope10",
"tech_v2_kb_width10&1.5_tslope10_15s",
"tech_v2_kb_width10&1.5_tslope10_60s",
"tech_v2_kb_width10&1.5_tslope10_600s",
"tech_v2_kb_width10&1.5_tslope10_1800s",
"tech_v2_kb_loc50&2.5_tslope10",
"tech_v2_kb_loc50&2.5_tslope10_15s",
"tech_v2_kb_loc50&2.5_tslope10_60s",
"tech_v2_kb_loc50&2.5_tslope10_600s",
"tech_v2_kb_loc50&2.5_tslope10_1800s",
"tech_v2_kb_width50&2.5_tslope10",
"tech_v2_kb_width50&2.5_tslope10_15s",
"tech_v2_kb_width50&2.5_tslope10_60s",
"tech_v2_kb_width50&2.5_tslope10_600s",
"tech_v2_kb_width50&2.5_tslope10_1800s",
"tech_v2_squeeze20_tslope10",
"tech_v2_squeeze20_tslope10_15s",
"tech_v2_squeeze20_tslope10_60s",
"tech_v2_squeeze20_tslope10_600s",
"tech_v2_squeeze20_tslope10_1800s",
"tech_v2_chdexit_long20&3_tslope10",
"tech_v2_chdexit_long20&3_tslope10_15s",
"tech_v2_chdexit_long20&3_tslope10_60s",
"tech_v2_chdexit_long20&3_tslope10_600s",
"tech_v2_chdexit_long20&3_tslope10_1800s",
"tech_v2_chdexit_short20&3_tslope10",
"tech_v2_chdexit_short20&3_tslope10_15s",
"tech_v2_chdexit_short20&3_tslope10_60s",
"tech_v2_chdexit_short20&3_tslope10_600s",
"tech_v2_chdexit_short20&3_tslope10_1800s",
"tech_v2_fso10_tslope10",
"tech_v2_fso10_tslope10_15s",
"tech_v2_fso10_tslope10_60s",
"tech_v2_fso10_tslope10_600s",
"tech_v2_fso10_tslope10_1800s",
"tech_v2_sso10_tslope10",
"tech_v2_sso10_tslope10_15s",
"tech_v2_sso10_tslope10_60s",
"tech_v2_sso10_tslope10_600s",
"tech_v2_sso10_tslope10_1800s",
"tech_v2_dsso20_tslope10",
"tech_v2_dsso20_tslope10_15s",
"tech_v2_dsso20_tslope10_60s",
"tech_v2_dsso20_tslope10_600s",
"tech_v2_dsso20_tslope10_1800s",
"tech_v2_uo7&14&28_tslope10",
"tech_v2_uo7&14&28_tslope10_15s",
"tech_v2_uo7&14&28_tslope10_60s",
"tech_v2_uo7&14&28_tslope10_600s",
"tech_v2_uo7&14&28_tslope10_1800s",
"tech_v2_illiq10_tslope10",
"tech_v2_illiq10_tslope10_15s",
"tech_v2_illiq10_tslope10_60s",
"tech_v2_illiq10_tslope10_600s",
"tech_v2_illiq10_tslope10_1800s",
"tech_v2_kst_tslope10",
"tech_v2_kst_tslope10_15s",
"tech_v2_kst_tslope10_60s",
"tech_v2_kst_tslope10_600s",
"tech_v2_kst_tslope10_1800s",
"tech_v2_adj_cp_tacce10",
"tech_v2_adj_cp_tacce10_15s",
"tech_v2_adj_cp_tacce10_60s",
"tech_v2_adj_cp_tacce10_600s",
"tech_v2_adj_cp_tacce10",
"tech_v2_adj_cp_tacce10_15s",
"tech_v2_adj_cp_tacce10_60s",
"tech_v2_adj_cp_tacce10_600s",
"tech_v2_adj_cp_tacce10_1800s",
"tech_v2_obv_tacce10",
"tech_v2_obv_tacce10_15s",
"tech_v2_obv_tacce10_60s",
"tech_v2_obv_tacce10_600s",
"tech_v2_obv_tacce10_1800s",
"tech_v2_pvt_tacce10",
"tech_v2_pvt_tacce10_15s",
"tech_v2_pvt_tacce10_60s",
"tech_v2_pvt_tacce10_600s",
"tech_v2_pvt_tacce10_1800s",
"tech_v2_cko3&10_tacce10",
"tech_v2_cko3&10_tacce10_15s",
"tech_v2_cko3&10_tacce10_60s",
"tech_v2_cko3&10_tacce10_600s",
"tech_v2_cko3&10_tacce10_1800s",
"tech_v2_kvo34&55_tacce10",
"tech_v2_kvo34&55_tacce10_15s",
"tech_v2_kvo34&55_tacce10_60s",
"tech_v2_kvo34&55_tacce10_600s",
"tech_v2_kvo34&55_tacce10_1800s",
"tech_v2_wvad_tacce10",
"tech_v2_wvad_tacce10_15s",
"tech_v2_wvad_tacce10_60s",
"tech_v2_wvad_tacce10_600s",
"tech_v2_wvad_tacce10_1800s",
"tech_v2_forceindex10_tacce10",
"tech_v2_forceindex10_tacce10_15s",
"tech_v2_forceindex10_tacce10_60s",
"tech_v2_forceindex10_tacce10_600s",
"tech_v2_forceindex10_tacce10_1800s",
"tech_v2_bop10_tacce10",
"tech_v2_bop10_tacce10_15s",
"tech_v2_bop10_tacce10_60s",
"tech_v2_bop10_tacce10_600s",
"tech_v2_bop10_tacce10_1800s",
"tech_v2_cmo10_tacce10",
"tech_v2_cmo10_tacce10_15s",
"tech_v2_cmo10_tacce10_60s",
"tech_v2_cmo10_tacce10_600s",
"tech_v2_cmo10_tacce10_1800s",
"tech_v2_rsi20_tacce10",
"tech_v2_rsi20_tacce10_15s",
"tech_v2_rsi20_tacce10_60s",
"tech_v2_rsi20_tacce10_600s",
"tech_v2_rsi20_tacce10_1800s",
"tech_v2_srsi20_tacce10",
"tech_v2_srsi20_tacce10_15s",
"tech_v2_srsi20_tacce10_60s",
"tech_v2_srsi20_tacce10_600s",
"tech_v2_srsi20_tacce10_1800s",
"tech_v2_cmb27&9&5&3_tacce10",
"tech_v2_cmb27&9&5&3_tacce10_15s",
"tech_v2_cmb27&9&5&3_tacce10_60s",
"tech_v2_cmb27&9&5&3_tacce10_600s",
"tech_v2_cmb27&9&5&3_tacce10_1800s",
"tech_v2_rvi10&20_tacce10",
"tech_v2_rvi10&20_tacce10_15s",
"tech_v2_rvi10&20_tacce10_60s",
"tech_v2_rvi10&20_tacce10_600s",
"tech_v2_rvi10&20_tacce10_1800s",
"tech_v2_mfi10_tacce10",
"tech_v2_mfi10_tacce10_15s",
"tech_v2_mfi10_tacce10_60s",
"tech_v2_mfi10_tacce10_600s",
"tech_v2_mfi10_tacce10_1800s",
"tech_v2_vr10_tacce10",
"tech_v2_vr10_tacce10_15s",
"tech_v2_vr10_tacce10_60s",
"tech_v2_vr10_tacce10_600s",
"tech_v2_vr10_tacce10_1800s",
"tech_v2_ar10_tacce10",
"tech_v2_ar10_tacce10_15s",
"tech_v2_ar10_tacce10_60s",
"tech_v2_ar10_tacce10_600s",
"tech_v2_ar10_tacce10_1800s",
"tech_v2_br10_tacce10",
"tech_v2_br10_tacce10_15s",
"tech_v2_br10_tacce10_60s",
"tech_v2_br10_tacce10_600s",
"tech_v2_br10_tacce10_1800s",
"tech_v2_arbr10_tacce10",
"tech_v2_arbr10_tacce10_15s",
"tech_v2_arbr10_tacce10_60s",
"tech_v2_arbr10_tacce10_600s",
"tech_v2_arbr10_tacce10_1800s",
"tech_v2_cr10_tacce10",
"tech_v2_cr10_tacce10_15s",
"tech_v2_cr10_tacce10_60s",
"tech_v2_cr10_tacce10_600s",
"tech_v2_cr10_tacce10_1800s",
"tech_v2_ddi10_tacce10",
"tech_v2_ddi10_tacce10_15s",
"tech_v2_ddi10_tacce10_60s",
"tech_v2_ddi10_tacce10_600s",
"tech_v2_ddi10_tacce10_1800s",
"tech_v2_adtm10_tacce10",
"tech_v2_adtm10_tacce10_15s",
"tech_v2_adtm10_tacce10_60s",
"tech_v2_adtm10_tacce10_600s",
"tech_v2_adtm10_tacce10_1800s",
"tech_v2_cmf10_tacce10",
"tech_v2_cmf10_tacce10_15s",
"tech_v2_cmf10_tacce10_60s",
"tech_v2_cmf10_tacce10_600s",
"tech_v2_cmf10_tacce10_1800s",
"tech_v2_acd10_tacce10",
"tech_v2_acd10_tacce10_15s",
"tech_v2_acd10_tacce10_60s",
"tech_v2_acd10_tacce10_600s",
"tech_v2_acd10_tacce10_1800s",
"tech_v2_dev5_tacce10",
"tech_v2_dev5_tacce10_15s",
"tech_v2_dev5_tacce10_60s",
"tech_v2_dev5_tacce10_600s",
"tech_v2_dev5_tacce10_1800s",
"tech_v2_cci5_tacce10",
"tech_v2_cci5_tacce10_15s",
"tech_v2_cci5_tacce10_60s",
"tech_v2_cci5_tacce10_600s",
"tech_v2_cci5_tacce10_1800s",
"tech_v2_dev60_tacce10",
"tech_v2_dev60_tacce10_15s",
"tech_v2_dev60_tacce10_60s",
"tech_v2_dev60_tacce10_600s",
"tech_v2_dev60_tacce10_1800s",
"tech_v2_cci60_tacce10",
"tech_v2_cci60_tacce10_15s",
"tech_v2_cci60_tacce10_60s",
"tech_v2_cci60_tacce10_600s",
"tech_v2_cci60_tacce10_1800s",
"tech_v2_tsi20&10_tacce10",
"tech_v2_tsi20&10_tacce10_15s",
"tech_v2_tsi20&10_tacce10_60s",
"tech_v2_tsi20&10_tacce10_600s",
"tech_v2_tsi20&10_tacce10_1800s",
"tech_v2_pvi_tacce10",
"tech_v2_pvi_tacce10_15s",
"tech_v2_pvi_tacce10_60s",
"tech_v2_pvi_tacce10_600s",
"tech_v2_pvi_tacce10_1800s",
"tech_v2_nvi_tacce10",
"tech_v2_nvi_tacce10_15s",
"tech_v2_nvi_tacce10_60s",
"tech_v2_nvi_tacce10_600s",
"tech_v2_nvi_tacce10_1800s",
"tech_v2_atrp20_tacce10",
"tech_v2_atrp20_tacce10_15s",
"tech_v2_atrp20_tacce10_60s",
"tech_v2_atrp20_tacce10_600s",
"tech_v2_atrp20_tacce10_1800s",
"tech_v2_plusdi20_tacce10",
"tech_v2_plusdi20_tacce10_15s",
"tech_v2_plusdi20_tacce10_60s",
"tech_v2_plusdi20_tacce10_600s",
"tech_v2_plusdi20_tacce10_1800s",
"tech_v2_minusdi20_tacce10",
"tech_v2_minusdi20_tacce10_15s",
"tech_v2_minusdi20_tacce10_60s",
"tech_v2_minusdi20_tacce10_600s",
"tech_v2_minusdi20_tacce10_1800s",
"tech_v2_adxr20_tacce10",
"tech_v2_adxr20_tacce10_15s",
"tech_v2_adxr20_tacce10_60s",
"tech_v2_adxr20_tacce10_600s",
"tech_v2_adxr20_tacce10_1800s",
"tech_v2_massindex9&25_tacce10",
"tech_v2_massindex9&25_tacce10_15s",
"tech_v2_massindex9&25_tacce10_60s",
"tech_v2_massindex9&25_tacce10_600s",
"tech_v2_massindex9&25_tacce10_1800s",
"tech_v2_ulcer10_tacce10",
"tech_v2_ulcer10_tacce10_15s",
"tech_v2_ulcer10_tacce10_60s",
"tech_v2_ulcer10_tacce10_600s",
"tech_v2_ulcer10_tacce10_1800s",
"tech_v2_emv10_tacce10",
"tech_v2_emv10_tacce10_15s",
"tech_v2_emv10_tacce10_60s",
"tech_v2_emv10_tacce10_600s",
"tech_v2_emv10_tacce10_1800s",
"tech_v2_ckv10_tacce10",
"tech_v2_ckv10_tacce10_15s",
"tech_v2_ckv10_tacce10_60s",
"tech_v2_ckv10_tacce10_600s",
"tech_v2_ckv10_tacce10_1800s",
"tech_v2_stdamb10_tacce10",
"tech_v2_stdamb10_tacce10_15s",
"tech_v2_stdamb10_tacce10_60s",
"tech_v2_stdamb10_tacce10_600s",
"tech_v2_stdamb10_tacce10_1800s",
"tech_v2_pvtx10_tacce10",
"tech_v2_pvtx10_tacce10_15s",
"tech_v2_pvtx10_tacce10_60s",
"tech_v2_pvtx10_tacce10_600s",
"tech_v2_pvtx10_tacce10_1800s",
"tech_v2_nvtx10_tacce10",
"tech_v2_nvtx10_tacce10_15s",
"tech_v2_nvtx10_tacce10_60s",
"tech_v2_nvtx10_tacce10_600s",
"tech_v2_nvtx10_tacce10_1800s",
"tech_v2_vtx10_tacce10",
"tech_v2_vtx10_tacce10_15s",
"tech_v2_vtx10_tacce10_60s",
"tech_v2_vtx10_tacce10_600s",
"tech_v2_vtx10_tacce10_1800s",
"tech_v2_trix10&9_tacce10",
"tech_v2_trix10&9_tacce10_15s",
"tech_v2_trix10&9_tacce10_60s",
"tech_v2_trix10&9_tacce10_600s",
"tech_v2_trix10&9_tacce10_1800s",
"tech_v2_plrc10_tacce10",
"tech_v2_plrc10_tacce10_15s",
"tech_v2_plrc10_tacce10_60s",
"tech_v2_plrc10_tacce10_600s",
"tech_v2_plrc10_tacce10_1800s",
"tech_v2_macd12&26&9_tacce10",
"tech_v2_macd12&26&9_tacce10_15s",
"tech_v2_macd12&26&9_tacce10_60s",
"tech_v2_macd12&26&9_tacce10_600s",
"tech_v2_macd12&26&9_tacce10_1800s",
"tech_v2_vmacd12&26&9_tacce10",
"tech_v2_vmacd12&26&9_tacce10_15s",
"tech_v2_vmacd12&26&9_tacce10_60s",
"tech_v2_vmacd12&26&9_tacce10_600s",
"tech_v2_vmacd12&26&9_tacce10_1800s",
"tech_v2_dpo10_tacce10",
"tech_v2_dpo10_tacce10_15s",
"tech_v2_dpo10_tacce10_60s",
"tech_v2_dpo10_tacce10_600s",
"tech_v2_dpo10_tacce10_1800s",
"tech_v2_bb_loc10&1.5_tacce10",
"tech_v2_bb_loc10&1.5_tacce10_15s",
"tech_v2_bb_loc10&1.5_tacce10_60s",
"tech_v2_bb_loc10&1.5_tacce10_600s",
"tech_v2_bb_loc10&1.5_tacce10_1800s",
"tech_v2_bb_width10&1.5_tacce10",
"tech_v2_bb_width10&1.5_tacce10_15s",
"tech_v2_bb_width10&1.5_tacce10_60s",
"tech_v2_bb_width10&1.5_tacce10_600s",
"tech_v2_bb_width10&1.5_tacce10_1800s",
"tech_v2_bb_loc50&2.5_tacce10",
"tech_v2_bb_loc50&2.5_tacce10_15s",
"tech_v2_bb_loc50&2.5_tacce10_60s",
"tech_v2_bb_loc50&2.5_tacce10_600s",
"tech_v2_bb_loc50&2.5_tacce10_1800s",
"tech_v2_bb_width50&2.5_tacce10",
"tech_v2_bb_width50&2.5_tacce10_15s",
"tech_v2_bb_width50&2.5_tacce10_60s",
"tech_v2_bb_width50&2.5_tacce10_600s",
"tech_v2_bb_width50&2.5_tacce10_1800s",
"tech_v2_kb_loc10&1.5_tacce10",
"tech_v2_kb_loc10&1.5_tacce10_15s",
"tech_v2_kb_loc10&1.5_tacce10_60s",
"tech_v2_kb_loc10&1.5_tacce10_600s",
"tech_v2_kb_loc10&1.5_tacce10_1800s",
"tech_v2_kb_width10&1.5_tacce10",
"tech_v2_kb_width10&1.5_tacce10_15s",
"tech_v2_kb_width10&1.5_tacce10_60s",
"tech_v2_kb_width10&1.5_tacce10_600s",
"tech_v2_kb_width10&1.5_tacce10_1800s",
"tech_v2_kb_loc50&2.5_tacce10",
"tech_v2_kb_loc50&2.5_tacce10_15s",
"tech_v2_kb_loc50&2.5_tacce10_60s",
"tech_v2_kb_loc50&2.5_tacce10_600s",
"tech_v2_kb_loc50&2.5_tacce10_1800s",
"tech_v2_kb_width50&2.5_tacce10",
"tech_v2_kb_width50&2.5_tacce10_15s",
"tech_v2_kb_width50&2.5_tacce10_60s",
"tech_v2_kb_width50&2.5_tacce10_600s",
"tech_v2_kb_width50&2.5_tacce10_1800s",
"tech_v2_squeeze20_tacce10",
"tech_v2_squeeze20_tacce10_15s",
"tech_v2_squeeze20_tacce10_60s",
"tech_v2_squeeze20_tacce10_600s",
"tech_v2_squeeze20_tacce10_1800s",
"tech_v2_chdexit_long20&3_tacce10",
"tech_v2_chdexit_long20&3_tacce10_15s",
"tech_v2_chdexit_long20&3_tacce10_60s",
"tech_v2_chdexit_long20&3_tacce10_600s",
"tech_v2_chdexit_long20&3_tacce10_1800s",
"tech_v2_chdexit_short20&3_tacce10",
"tech_v2_chdexit_short20&3_tacce10_15s",
"tech_v2_chdexit_short20&3_tacce10_60s",
"tech_v2_chdexit_short20&3_tacce10_600s",
"tech_v2_chdexit_short20&3_tacce10_1800s",
"tech_v2_fso10_tacce10",
"tech_v2_fso10_tacce10_15s",
"tech_v2_fso10_tacce10_60s",
"tech_v2_fso10_tacce10_600s",
"tech_v2_fso10_tacce10_1800s",
"tech_v2_sso10_tacce10",
"tech_v2_sso10_tacce10_15s",
"tech_v2_sso10_tacce10_60s",
"tech_v2_sso10_tacce10_600s",
"tech_v2_sso10_tacce10_1800s",
"tech_v2_dsso20_tacce10",
"tech_v2_dsso20_tacce10_15s",
"tech_v2_dsso20_tacce10_60s",
"tech_v2_dsso20_tacce10_600s",
"tech_v2_dsso20_tacce10_1800s",
"tech_v2_uo7&14&28_tacce10",
"tech_v2_uo7&14&28_tacce10_15s",
"tech_v2_uo7&14&28_tacce10_60s",
"tech_v2_uo7&14&28_tacce10_600s",
"tech_v2_uo7&14&28_tacce10_1800s",
"tech_v2_illiq10_tacce10",
"tech_v2_illiq10_tacce10_15s",
"tech_v2_illiq10_tacce10_60s",
"tech_v2_illiq10_tacce10_600s",
"tech_v2_illiq10_tacce10_1800s",
"tech_v2_kst_tacce10",
"tech_v2_kst_tacce10_15s",
"tech_v2_kst_tacce10_60s",
"tech_v2_kst_tacce10_600s",
"tech_v2_kst_tacce10_1800s",
"tech_v2_obv_pconf20",
"tech_v2_obv_pconf20_15s",
"tech_v2_obv_pconf20_60s",
"tech_v2_obv_pconf20_600s",
"tech_v2_obv_pconf20_1800s",
"tech_v2_pvt_pconf20",
"tech_v2_pvt_pconf20_15s",
"tech_v2_pvt_pconf20_60s",
"tech_v2_pvt_pconf20_600s",
"tech_v2_pvt_pconf20_1800s",
"tech_v2_cko3&10_pconf20",
"tech_v2_cko3&10_pconf20_15s",
"tech_v2_cko3&10_pconf20_60s",
"tech_v2_cko3&10_pconf20_600s",
"tech_v2_cko3&10_pconf20_1800s",
"tech_v2_kvo34&55_pconf20",
"tech_v2_kvo34&55_pconf20_15s",
"tech_v2_kvo34&55_pconf20_60s",
"tech_v2_kvo34&55_pconf20_600s",
"tech_v2_kvo34&55_pconf20_1800s",
"tech_v2_wvad_pconf20",
"tech_v2_wvad_pconf20_15s",
"tech_v2_wvad_pconf20_60s",
"tech_v2_wvad_pconf20_600s",
"tech_v2_wvad_pconf20_1800s",
"tech_v2_forceindex10_pconf20",
"tech_v2_forceindex10_pconf20_15s",
"tech_v2_forceindex10_pconf20_60s",
"tech_v2_forceindex10_pconf20_600s",
"tech_v2_forceindex10_pconf20_1800s",
"tech_v2_bop10_pconf20",
"tech_v2_bop10_pconf20_15s",
"tech_v2_bop10_pconf20_60s",
"tech_v2_bop10_pconf20_600s",
"tech_v2_bop10_pconf20",
"tech_v2_cmo10_pconf20",
"tech_v2_cmo10_pconf20_15s",
"tech_v2_cmo10_pconf20_60s",
"tech_v2_cmo10_pconf20_600s",
"tech_v2_cmo10_pconf20_1800s",
"tech_v2_rsi20_pconf20",
"tech_v2_rsi20_pconf20_15s",
"tech_v2_rsi20_pconf20_60s",
"tech_v2_rsi20_pconf20_600s",
"tech_v2_rsi20_pconf20_1800s",
"tech_v2_srsi20_pconf20",
"tech_v2_srsi20_pconf20_15s",
"tech_v2_srsi20_pconf20_60s",
"tech_v2_srsi20_pconf20_600s",
"tech_v2_srsi20_pconf20_1800s",
"tech_v2_cmb27&9&5&3_pconf20",
"tech_v2_cmb27&9&5&3_pconf20_15s",
"tech_v2_cmb27&9&5&3_pconf20_60s",
"tech_v2_cmb27&9&5&3_pconf20_600s",
"tech_v2_cmb27&9&5&3_pconf20_1800s",
"tech_v2_rvi10&20_pconf20",
"tech_v2_rvi10&20_pconf20_15s",
"tech_v2_rvi10&20_pconf20_60s",
"tech_v2_rvi10&20_pconf20_600s",
"tech_v2_rvi10&20_pconf20_1800s",
"tech_v2_mfi10_pconf20",
"tech_v2_mfi10_pconf20_15s",
"tech_v2_mfi10_pconf20_60s",
"tech_v2_mfi10_pconf20_600s",
"tech_v2_mfi10_pconf20_1800s",
"tech_v2_vr10_pconf20",
"tech_v2_vr10_pconf20_15s",
"tech_v2_vr10_pconf20_60s",
"tech_v2_vr10_pconf20_600s",
"tech_v2_vr10_pconf20_1800s",
"tech_v2_ar10_pconf20",
"tech_v2_ar10_pconf20_15s",
"tech_v2_ar10_pconf20_60s",
"tech_v2_ar10_pconf20_600s",
"tech_v2_ar10_pconf20_1800s",
"tech_v2_br10_pconf20",
"tech_v2_br10_pconf20_15s",
"tech_v2_br10_pconf20_60s",
"tech_v2_br10_pconf20_600s",
"tech_v2_br10_pconf20_1800s",
"tech_v2_cr10_pconf20",
"tech_v2_cr10_pconf20_15s",
"tech_v2_cr10_pconf20_60s",
"tech_v2_cr10_pconf20_600s",
"tech_v2_cr10_pconf20_1800s",
"tech_v2_ddi10_pconf20",
"tech_v2_ddi10_pconf20_15s",
"tech_v2_ddi10_pconf20_60s",
"tech_v2_ddi10_pconf20_600s",
"tech_v2_ddi10_pconf20_1800s",
"tech_v2_adtm10_pconf20",
"tech_v2_adtm10_pconf20_15s",
"tech_v2_adtm10_pconf20_60s",
"tech_v2_adtm10_pconf20_600s",
"tech_v2_adtm10_pconf20_1800s",
"tech_v2_cmf10_pconf20",
"tech_v2_cmf10_pconf20_15s",
"tech_v2_cmf10_pconf20_60s",
"tech_v2_cmf10_pconf20_600s",
"tech_v2_cmf10_pconf20_1800s",
"tech_v2_acd10_pconf20",
"tech_v2_acd10_pconf20_15s",
"tech_v2_acd10_pconf20_60s",
"tech_v2_acd10_pconf20_600s",
"tech_v2_acd10_pconf20_1800s",
"tech_v2_cci5_pconf20",
"tech_v2_cci5_pconf20_15s",
"tech_v2_cci5_pconf20_60s",
"tech_v2_cci5_pconf20_600s",
"tech_v2_cci5_pconf20_1800s",
"tech_v2_cci60_pconf20",
"tech_v2_cci60_pconf20_15s",
"tech_v2_cci60_pconf20_60s",
"tech_v2_cci60_pconf20_600s",
"tech_v2_cci60_pconf20_1800s",
"tech_v2_tsi20&10_pconf20",
"tech_v2_tsi20&10_pconf20_15s",
"tech_v2_tsi20&10_pconf20_60s",
"tech_v2_tsi20&10_pconf20_600s",
"tech_v2_tsi20&10_pconf20_1800s",
"tech_v2_trix10&9_pconf20",
"tech_v2_trix10&9_pconf20_15s",
"tech_v2_trix10&9_pconf20_60s",
"tech_v2_trix10&9_pconf20_600s",
"tech_v2_trix10&9_pconf20_1800s",
"tech_v2_plrc10_pconf20",
"tech_v2_plrc10_pconf20_15s",
"tech_v2_plrc10_pconf20_60s",
"tech_v2_plrc10_pconf20_600s",
"tech_v2_plrc10_pconf20_1800s",
"tech_v2_macd12&26&9_pconf20",
"tech_v2_macd12&26&9_pconf20_15s",
"tech_v2_macd12&26&9_pconf20_60s",
"tech_v2_macd12&26&9_pconf20_600s",
"tech_v2_macd12&26&9_pconf20_1800s",
"tech_v2_vmacd12&26&9_pconf20",
"tech_v2_vmacd12&26&9_pconf20_15s",
"tech_v2_vmacd12&26&9_pconf20_60s",
"tech_v2_vmacd12&26&9_pconf20_600s",
"tech_v2_vmacd12&26&9_pconf20_1800s",
"tech_v2_fso10_pconf20",
"tech_v2_fso10_pconf20_15s",
"tech_v2_fso10_pconf20_60s",
"tech_v2_fso10_pconf20_600s",
"tech_v2_fso10_pconf20_1800s",
"tech_v2_sso10_pconf20",
"tech_v2_sso10_pconf20_15s",
"tech_v2_sso10_pconf20_60s",
"tech_v2_sso10_pconf20_600s",
"tech_v2_sso10_pconf20_1800s",
"tech_v2_dsso20_pconf20",
"tech_v2_dsso20_pconf20_15s",
"tech_v2_dsso20_pconf20_60s",
"tech_v2_dsso20_pconf20_600s",
"tech_v2_dsso20_pconf20_1800s",
"tech_v2_uo7&14&28_pconf20",
"tech_v2_uo7&14&28_pconf20_15s",
"tech_v2_uo7&14&28_pconf20_60s",
"tech_v2_uo7&14&28_pconf20_600s",
"tech_v2_uo7&14&28_pconf20_1800s",
"tech_v2_kst_pconf20",
"tech_v2_kst_pconf20_15s",
"tech_v2_kst_pconf20_60s",
"tech_v2_kst_pconf20_600s",
"tech_v2_kst_pconf20_1800s",
```