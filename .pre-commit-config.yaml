repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    # -   id: check-yaml
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
    -   id: sort-simple-yaml
    -   id: requirements-txt-fixer
    -   id: check-merge-conflict
-   repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.3.5
    hooks:
        # Run the linter.
    - id: ruff
      args: [ --fix ]
      files: ^kunpeng/.*\\.py$
        # Run the formatter.
    - id: ruff-format
      files: ^kunpeng/.*\\.py$


-   repo: local
    hooks:
      - id: ty-check
        name: ty-check
        language: python
        entry: ty check
        pass_filenames: false
        # args: [--python=.venv/]
        additional_dependencies: [ty]
        files: ^kunpeng/.*\\.py$
