"""验证安装"""


def test_import():
    try:
        import kunpeng_replay

        print(f"✓ 成功导入 kunpeng_replay v{kunpeng_replay.__version__}")

        from kunpeng_replay import ReplayEngine, ReplayConfig

        print("✓ 核心组件导入成功")

        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_cli():
    import subprocess

    try:
        result = subprocess.run(
            ["kunpeng-replay", "--help"], capture_output=True, text=True
        )
        if result.returncode == 0:
            print("✓ 命令行工具可用")
            return True
        else:
            print("✗ 命令行工具不可用")
            return False
    except FileNotFoundError:
        print("✗ 命令行工具未找到")
        return False


if __name__ == "__main__":
    print("=== Kunpeng Replay 安装验证 ===")
    test_import()
    test_cli()
