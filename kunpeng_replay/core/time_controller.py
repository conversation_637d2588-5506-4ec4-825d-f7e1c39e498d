"""简化的时间控制器"""

import threading
import time
from collections import deque
from typing import Callable, Deque, List, Optional

from kunpeng_replay.utils.logger import wylogger

from ..events.market_data_event import MarketDataEvent

# 全局调用序号计数器（与replay_engine共享）
_call_counter = 0


def get_next_call_id() -> int:
    """获取下一个调用序号"""
    global _call_counter
    _call_counter += 1
    return _call_counter


class TimeController:
    """简化的时间控制器（单例模式）

    只保留核心时间管理功能，移除复杂的统计和状态管理
    使用单例模式确保全局只有一个时间控制器实例
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        # 避免重复初始化
        if self._initialized:
            return

        call_id = get_next_call_id()
        self.logger = wylogger
        self.logger.info(f"[{call_id}] TimeController.__init__: 初始化时间控制器（单例）")

        self.current_time: Optional[int] = None
        self.system_time_offset: Optional[float] = None
        self.event_queue: Deque[MarketDataEvent] = deque()
        self.time_listeners: List[Callable] = []

        # 基本统计
        self.processed_events = 0
        self.start_time: Optional[float] = None

        # 时间同步配置
        self.enable_system_time_sync = True

        self._initialized = True

    @classmethod
    def get_instance(cls) -> "TimeController":
        """获取TimeController单例实例"""
        if cls._instance is None:
            cls()
        return cls._instance

    @classmethod
    def reset_instance(cls):
        """重置单例实例（主要用于测试）"""
        with cls._lock:
            cls._instance = None

    def set_event_queue(self, events: Deque[MarketDataEvent]):
        """设置事件队列并初始化时间同步"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] set_event_queue: 设置事件队列，共{len(events)}个事件")

        self.event_queue = events
        self.processed_events = 0

        if events:
            # 设置初始时间为第一个事件的localtime
            first_event = events[0]
            self.current_time = first_event.localtime

            # 计算系统时间偏移量 - 修复：让调整后的时间直接等于事件的历史时间
            if self.enable_system_time_sync:
                # system_time_offset = 0 意味着调整后的时间就是事件的localtime
                # 这样可以实现真正的时间回放功能
                self.system_time_offset = 0.0

            call_id = get_next_call_id()
            self.logger.info(f"[{call_id}] set_event_queue: 初始时间设置为{self.current_time}微秒")

            if self.enable_system_time_sync:
                historical_time = self.current_time / 1e6
                current_system_time = time.time()
                time_diff = current_system_time - historical_time
                call_id = get_next_call_id()
                self.logger.info(f"[{call_id}] set_event_queue: 事件历史时间与当前系统时间差: {time_diff:.2f}秒")

    def get_current_time_us(self) -> int:
        """获取当前模拟时间（微秒）"""
        return self.current_time or 0

    def get_current_time_ms(self) -> int:
        """获取当前模拟时间（毫秒）"""
        return (self.current_time or 0) // 1000

    def get_current_time_s(self) -> float:
        """获取当前模拟时间（秒）"""
        return (self.current_time or 0) / 1e6

    def get_system_time_s(self) -> float:
        """获取调整后的时间（秒）- 用于时间回放功能"""
        if self.system_time_offset is None:
            return time.time()
        # 修复：直接返回事件的历史时间，实现真正的时间回放
        return self.current_time / 1e6

    def advance_to_next_event(self) -> bool:
        """推进到下一个事件时间"""
        if not self.event_queue:
            return False

        next_event = self.event_queue[0]
        old_time = self.current_time
        self.current_time = next_event.localtime

        # 通知时间监听器
        for listener in self.time_listeners:
            try:
                listener(old_time, self.current_time)
            except Exception as e:
                self.logger.error(f"Time listener error: {e}")

        return True

    def get_current_event(self) -> Optional[MarketDataEvent]:
        """获取当前事件（不移除）"""
        return self.event_queue[0] if self.event_queue else None

    def mark_event_processed(self):
        """标记当前事件处理完成"""
        if self.event_queue:
            self.event_queue.popleft()
            self.processed_events += 1

            # 定期记录进度
            if self.processed_events % 1000 == 0:
                call_id = get_next_call_id()
                self.logger.info(f"[{call_id}] mark_event_processed: 已处理{self.processed_events}个事件")

    def register_time_listener(self, listener: Callable[[Optional[int], int], None]):
        """注册时间变化监听器"""
        self.time_listeners.append(listener)

    def get_remaining_events(self) -> int:
        """获取剩余事件数量"""
        return len(self.event_queue)

    def start_timing(self):
        """开始计时"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] start_timing: 开始计时")
        self.start_time = time.time()

    def set_time_sync_enabled(self, enabled: bool):
        """设置时间同步开关"""
        self.enable_system_time_sync = enabled

    def get_time_sync_status(self) -> dict:
        """获取时间同步状态"""
        return {
            "enabled": self.enable_system_time_sync,
            "current_simulated_time_us": self.current_time,
            "current_simulated_time_s": self.get_current_time_s(),
            "system_time_offset": self.system_time_offset,
            "corresponding_system_time_s": self.get_system_time_s() if self.system_time_offset else None,
        }
