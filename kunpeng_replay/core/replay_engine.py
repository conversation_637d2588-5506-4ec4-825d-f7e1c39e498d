"""简化的行情数据回放引擎

基于固定插槽设计的回放引擎，支持三个固定的处理阶段：
BaseTickProcessor → FeatureCalculator → FeatureDumper
"""

import asyncio
import time
from typing import Optional

from tqdm import tqdm

from kunpeng_replay.events.market_data_event import MarketDataEvent
from kunpeng_replay.merak_adapter import Tick

from ..config.settings import ReplayConfig
from ..core.time_controller import TimeController
from ..data_source.base_reader import MarketDataBaseReader
from ..data_types.standard_types import FeatureData, ProcessingStats
from ..processors.base_tick_feature_calculator import BaseFeatureCalculator
from ..processors.base_tick_processor import BaseTickProcessor
from ..processors.simple_processors import FeatureDumper

# 全局调用序号计数器
_call_counter = 0


def get_next_call_id() -> int:
    """获取下一个调用序号"""
    global _call_counter
    _call_counter += 1
    return _call_counter


class MarketDataReplayEngine:
    """简化的行情数据回放引擎

    基于固定插槽设计，支持三个固定的处理阶段：
    BaseTickProcessor → BaseFeatureCalculator → FeatureDumper
    """

    def __init__(self, config: ReplayConfig, logger):
        call_id = get_next_call_id()
        self.logger = logger
        self.logger.info(f"[{call_id}] MarketDataReplayEngine.__init__: 初始化回放引擎")

        self.config = config
        # 使用单例模式获取TimeController实例
        self.time_controller = TimeController.get_instance()
        self.data_reader: Optional[MarketDataBaseReader] = None

        # 三个固定的处理器插槽
        self.tick_processor: Optional[BaseTickProcessor] = None
        self.feature_calculator: Optional[BaseFeatureCalculator] = None
        self.feature_dumper: Optional[FeatureDumper] = None

        # 统计信息
        self.stats = ProcessingStats()
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None

    def set_data_reader(self, reader: MarketDataBaseReader):
        """设置数据读取器"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] set_data_reader: 设置数据读取器 {reader.__class__.__name__}")
        self.data_reader = reader

    def set_tick_processor(self, processor: BaseTickProcessor):
        """设置tick处理器

        Args:
            processor: tick处理器实例，负责原始行情 → 标准化tick数据
        """
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] set_tick_processor: 设置tick处理器 {processor.name}")
        self.tick_processor = processor

    def set_feature_calculator(self, calculator: BaseFeatureCalculator):
        """设置特征计算器

        Args:
            calculator: 特征计算器实例，负责tick数据 → 特征数据
        """
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] set_feature_calculator: 设置特征计算器 {calculator.name}")
        self.feature_calculator = calculator

    def set_feature_dumper(self, dumper: FeatureDumper):
        """设置特征存储器

        Args:
            dumper: 特征存储器实例，负责特征数据 → 持久化存储
        """
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] set_feature_dumper: 设置特征存储器 {dumper.name}")
        self.feature_dumper = dumper

    def _validate_setup(self):
        """验证引擎设置是否完整"""
        if self.data_reader is None:
            raise ValueError("Data reader not set. Call set_data_reader() first.")

        if self.tick_processor is None:
            raise ValueError("Tick processor not set. Call set_tick_processor() first.")

        if self.feature_calculator is None:
            raise ValueError("Feature calculator not set. Call set_feature_calculator() first.")

        if self.feature_dumper is None:
            raise ValueError("Feature dumper not set. Call set_feature_dumper() first.")

    async def run(self) -> ProcessingStats:
        """运行回放处理

        Returns:
            ProcessingStats: 处理统计信息
        """
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] run: 开始运行回放引擎")

        # 验证设置
        self._validate_setup()

        # 重置统计信息
        self.stats = ProcessingStats()
        self.stats.start_time = time.time()

        # 重置各processor的统计信息
        self.tick_processor.reset_stats()
        self.feature_calculator.reset_stats()
        self.feature_dumper.reset_stats()

        try:
            # 读取数据
            call_id = get_next_call_id()
            self.logger.info(f"[{call_id}] run: 读取事件数据")
            events = self.data_reader.read_events()
            self.stats.total_events = len(events)

            if self.stats.total_events == 0:
                self.logger.warning("No events to replay")
                return self.stats

            # 设置事件队列到时间控制器
            call_id = get_next_call_id()
            self.logger.info(f"[{call_id}] run: 设置事件队列，共{self.stats.total_events}个事件")
            from collections import deque

            self.time_controller.set_event_queue(deque(events))

            self.logger.info(f"[{call_id}] run: 开始处理{self.stats.total_events}个事件")

            # 处理事件
            if self.config.progress_bar:
                await self._run_with_progress()
            else:
                await self._run_simple()

            # 完成处理
            self.stats.end_time = time.time()
            self.stats.update_timing()

            call_id = get_next_call_id()
            self.logger.info(f"[{call_id}] run: 回放完成，处理了{self.stats.processed_events}个事件")
            return self.stats

        except Exception as e:
            self.logger.error(f"Replay failed: {e}")
            raise

    async def _run_simple(self):
        """简单的事件处理循环"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] _run_simple: 开始简单处理模式")

        self.time_controller.start_timing()

        while self.time_controller.get_remaining_events() > 0:
            # 推进到下一个事件时间
            call_id = get_next_call_id()
            if not self.time_controller.advance_to_next_event():
                self.logger.info(f"[{call_id}] _run_simple: 无法推进到下一个事件，结束处理")
                break

            # 获取当前事件
            event = self.time_controller.get_current_event()
            if event is None:
                break

            # 处理事件
            call_id = get_next_call_id()
            success = await self._process_event(event)

            if success:
                self.stats.processed_events += 1
            else:
                self.stats.failed_events += 1

            # 标记事件处理完成
            self.time_controller.mark_event_processed()

            # 定期让出控制权
            if self.stats.processed_events % 100 == 0:
                await asyncio.sleep(0.001)

    async def _run_with_progress(self):
        """带进度条的事件处理循环"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] _run_with_progress: 开始带进度条处理模式")

        pbar = tqdm(total=self.stats.total_events, desc="Processing events")

        try:
            self.time_controller.start_timing()

            while self.time_controller.get_remaining_events() > 0:
                # 推进到下一个事件时间
                if not self.time_controller.advance_to_next_event():
                    break

                # 获取当前事件
                event: Optional[MarketDataEvent] = self.time_controller.get_current_event()
                if event is None:
                    break

                # 处理事件
                success = await self._process_event(event)

                if success:
                    self.stats.processed_events += 1
                else:
                    self.stats.failed_events += 1

                # 更新进度条
                pbar.n = self.stats.processed_events
                pbar.refresh()

                # 标记事件处理完成
                self.time_controller.mark_event_processed()

                # 定期让出控制权
                if self.stats.processed_events % 100 == 0:
                    await asyncio.sleep(0.001)
        finally:
            pbar.close()

    async def _process_event(self, event: MarketDataEvent) -> bool:
        """处理单个事件，通过三个固定阶段

        Args:
            event: 原始行情事件

        Returns:
            bool: 是否处理成功
        """
        try:
            # 阶段1: Tick处理
            start_time = time.time()
            tick_data: Optional[Tick] = await self.tick_processor.process_tick(event)
            tick_time = time.time() - start_time

            self.tick_processor.total_processing_time += tick_time

            if tick_data is None:
                self.tick_processor.failed_count += 1
                self.stats.tick_processing_failures += 1
                return False
            self.logger.info(f" tick_data = {tick_data}")
            self.tick_processor.processed_count += 1

            # 阶段2: 特征计算
            start_time = time.time()
            features = self.feature_calculator.calculate_features(tick_data)
            feature_time = time.time() - start_time

            self.feature_calculator.total_processing_time += feature_time

            if features is None:
                self.feature_calculator.failed_count += 1
                self.stats.feature_calculation_failures += 1
                return False

            self.feature_calculator.processed_count += 1
            self.feature_calculator.total_features_calculated += len(features)

            # 阶段3: 特征存储
            start_time = time.time()
            # 从事件中获取ticker和timestamp信息，因为merak.Tick不包含这些字段
            feature_data = FeatureData(ticker=event.ticker, timestamp=event.localtime, features=features)
            dump_result = self.feature_dumper.dump_features(feature_data)
            dump_time = time.time() - start_time

            self.feature_dumper.total_processing_time += dump_time

            if not dump_result.success:
                self.feature_dumper.failed_count += 1
                self.stats.feature_dumping_failures += 1
                self.logger.warning(f"Feature dumping failed: {dump_result.error_message}")
                return False

            self.feature_dumper.processed_count += 1
            self.feature_dumper.total_records_saved += dump_result.records_saved

            return True

        except Exception as e:
            self.logger.error(f"Error processing event {event.get_event_id()}: {e}")
            return False

    def get_detailed_stats(self) -> dict:
        """获取详细的统计信息

        Returns:
            dict: 包含整体统计和各阶段统计的详细信息
        """
        return {
            "overall_stats": self.stats.to_dict(),
            "tick_processor_stats": self.tick_processor.get_stats() if self.tick_processor else {},
            "feature_calculator_stats": self.feature_calculator.get_stats() if self.feature_calculator else {},
            "feature_dumper_stats": self.feature_dumper.get_stats() if self.feature_dumper else {},
        }

    def print_summary(self):
        """打印处理摘要"""
        print("\n" + "=" * 60)
        print("Processing Summary")
        print("=" * 60)

        print(f"Total Events: {self.stats.total_events}")
        print(f"Processed: {self.stats.processed_events}")
        print(f"Failed: {self.stats.failed_events}")
        print(f"Success Rate: {self.stats.get_success_rate():.2%}")
        print(f"Processing Rate: {self.stats.processing_rate:.2f} events/sec")
        print(f"Average Time: {self.stats.avg_processing_time_ms:.2f} ms/event")

        print("\nStage Failures:")
        print(f"  Tick Processing: {self.stats.tick_processing_failures}")
        print(f"  Feature Calculation: {self.stats.feature_calculation_failures}")
        print(f"  Feature Dumping: {self.stats.feature_dumping_failures}")

        if self.tick_processor:
            tick_stats = self.tick_processor.get_stats()
            print(f"\nTick Processor ({tick_stats['name']}):")
            print(f"  Processed: {tick_stats['processed_count']}")
            print(f"  Success Rate: {tick_stats['success_rate']:.2%}")
            print(f"  Avg Time: {tick_stats['avg_processing_time_ms']:.2f} ms")

        if self.feature_calculator:
            feature_stats = self.feature_calculator.get_stats()
            print(f"\nFeature Calculator ({feature_stats['name']}):")
            print(f"  Processed: {feature_stats['processed_count']}")
            print(f"  Success Rate: {feature_stats['success_rate']:.2%}")
            print(f"  Total Features: {feature_stats['total_features_calculated']}")
            print(f"  Avg Features/Tick: {feature_stats['avg_features_per_tick']:.1f}")
            print(f"  Avg Time: {feature_stats['avg_processing_time_ms']:.2f} ms")

        if self.feature_dumper:
            dumper_stats = self.feature_dumper.get_stats()
            print(f"\nFeature Dumper ({dumper_stats['name']}):")
            print(f"  Processed: {dumper_stats['processed_count']}")
            print(f"  Success Rate: {dumper_stats['success_rate']:.2%}")
            print(f"  Records Saved: {dumper_stats['total_records_saved']}")
            print(f"  Avg Time: {dumper_stats['avg_processing_time_ms']:.2f} ms")

        print("=" * 60)

    def get_time_controller(self) -> TimeController:
        """获取时间控制器实例"""
        return self.time_controller

    def __del__(self):
        """析构函数，由于使用单例模式，不需要清理引用"""
        pass
