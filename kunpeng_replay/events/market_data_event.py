"""行情数据事件"""

import time
import uuid
from dataclasses import dataclass


@dataclass
class MarketDataEvent:
    """行情数据事件

    表示一个行情数据事件，包含股票代码、更新时间、本地时间和快照索引。
    参考kunpeng项目中的MarketDataEvent实现，确保ticker字段使用真实的股票代码数据。
    """

    ticker: str  # 真实股票代码，如 "000001.SZ", "600000.SH"
    updatetime: int  # 行情更新时间戳
    localtime: int  # 本地时间戳（微秒）
    snap_index: int  # 快照索引

    def __post_init__(self):
        """初始化后处理，生成事件ID"""
        self.event_id = str(uuid.uuid4())
        self.created_at = time.time()

    def to_message(self) -> str:
        """转换为MQ消息格式

        Returns:
            str: 格式化的消息字符串，格式为 "0,ticker,updatetime,localtime,snap_index"
        """
        return f"0,{self.ticker},{self.updatetime},{self.localtime},{self.snap_index}"

    def to_mq_message(self) -> str:
        """转换为消息队列消息格式（兼容kunpeng接口）

        Returns:
            str: 格式化的消息字符串
        """
        return self.to_message()

    def get_timestamp(self) -> int:
        """获取事件时间戳（微秒）"""
        return self.localtime

    def get_event_id(self) -> str:
        """获取事件ID"""
        return self.event_id

    def __str__(self) -> str:
        return f"MarketDataEvent(ticker={self.ticker}, updatetime={self.updatetime}, localtime={self.localtime})"

    def __repr__(self) -> str:
        return self.__str__()
