"""Tick批处理器模块 - 处理Tick数据并计算特征

1. 批处理模式：虽然tick数据是流式出现的，但为了简化处理逻辑，这里采用批处理方式
   - 每次调用process_tick_data时传入start_time和end_time，表示取这段时间内的tick数据
   - 这是一个有界区间的处理方式，适用于历史数据回测、离线分析和线上实时处理

"""

from typing import List, Optional

import bottleneck as bn
import numpy as np

from kunpeng_replay.market_data.market_data_fields import (
    ACCUMULATED_FIELD_MAPPING,
    FIELD_INDEX_MAPPING,
    MARKET_DATA_FIELDS,
)
from kunpeng_replay.ops.ndarray_ops import DiffAcc
from kunpeng_replay.processors.base_tick_processor import BaseTickProcessor
from kunpeng_replay.utils.timecut import timecuts_addauc
from kunpeng_replay.utils.typing import TickData  # numpy数组类型
from ..merak_adapter import Tick

from ..events.market_data_event import MarketDataEvent

SECONDS_IN_MILLISECOND = 1000
PRE_MARKET_START_TIME = 91500000  # 早盘集合竞价开始时间


def find_nearest_timecut_index(updatetime: int, timecuts: List[str]) -> int:
    """
    查找大于等于 updatetime 的最近 timecut

    参数:
        updatetime: 市场时间戳，格式为 HHMMSSmmm（小时分钟秒毫秒）
        timecuts: 包含所有 timecut 的 List[str]，格式为 HHMMSS（小时分钟秒）

    返回:
        大于等于 updatetime 的最近 timecut的index
    """
    # 将时间戳转换为字符串并补齐前导零
    timestamp_str = str(updatetime).zfill(9)  # 补齐到9位，例如 91500000 -> "091500000"

    # 截取前6位（HHMMSS），忽略毫秒部分
    formatted_time = timestamp_str[:6]  # 假设 TIMECUT_STRING_LENGTH = 6

    # 使用二分查找找到第一个大于等于 formatted_time 的索引
    timecut_index = int(np.searchsorted(timecuts, formatted_time))

    return timecut_index


class TickBatchProcessor(BaseTickProcessor):
    """Tick批处理器实现类（只负责读取和处理tick数据，不做特征计算）

    性能权衡：
    - 优势：简化特征计算逻辑，提高系统稳定性，便于调试和回测
    - 劣势：数据量随时间增长，处理耗时逐步增加，内存占用较大
    """

    def __init__(self, logger):
        """初始化Tick批处理器

        注意：构造时不设置 market_data_reader，需要后续通过 set_market_data_reader 方法设置
        """
        super().__init__()
        self.name = "TickBatchProcessor"
        self.logger = logger

        # 初始化BaseTickProcessor需要的统计信息
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0

        # 保持原有的统计信息字典（向后兼容）
        self.stats = {"processed": 0, "failed": 0, "total_time": 0.0}

    def process_tick(self, event: MarketDataEvent) -> Optional[Tick]:
        """实现BaseTickProcessor的抽象方法，将MarketDataEvent转换为merak.Tick

        这个方法实现了BaseTickProcessor的接口要求，将MarketDataEvent转换为merak.Tick
        原有的批处理逻辑移到了process_tick_batch方法中

        Args:
            event: 原始行情事件

        Returns:
            Tick: merak标准的tick数据，如果处理失败返回None
        """
        try:
            # 这里提供一个基本的转换，将MarketDataEvent转换为merak.Tick
            # 实际使用时需要根据数据源提供完整的63字段数据

            # 创建一个基本的Tick对象，使用默认值填充缺失字段
            tick_data = [
                str(event.updatetime),  # tick_ut
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,  # bp1-bp10
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,  # bv1-bv10
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,  # ap1-ap10
                0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,  # av1-av10
                0.0,  # tick_pre_close
                0.0,  # tick_op
                0.0,  # tick_hp
                0.0,  # tick_lp
                0.0,  # tick_vol
                0.0,  # tick_tvr
                0.0,  # bv_tot
                0.0,  # av_tot
                0.0,  # bvwp_tot
                0.0,  # avwp_tot
                0.0,  # up_limit_p
                0.0,  # down_limit_p
            ]

            return Tick(*tick_data)

        except Exception as e:
            self.logger.error(f"Error converting event to Tick: {e}")
            return None

    async def process_tick_batch(self, event: MarketDataEvent) -> Optional[Tick]:
        """处理事件 - 原始的批处理逻辑

        这个方法保留了原有的批处理逻辑，用于需要批处理功能的场景

        Args:
            event: 要处理的事件

        Returns:
            bool: 处理是否成功
        """
        self.logger.info(f"处理 event: {event}")

        try:
            # 处理行情数据事件
            timecut_index = find_nearest_timecut_index(updatetime=event.updatetime, timecuts=timecuts_addauc)
            timecut = timecuts_addauc[timecut_index]
            result = await self.process_zp_ticks(
                ticker=event.ticker, start_time=PRE_MARKET_START_TIME, end_time=event.updatetime, timecut=timecut
            )
            success = result is not None

            return success

        except Exception as e:
            self.logger.error(f"Error processing event {event.get_event_id()}: {e}")
            return False

    async def process_auc_ticks(
        self,
        ticker: str,
        start_time: int,
        end_time: int,
    ) -> Optional[np.ndarray]:
        """
        处理集合竞价阶段的tick数据。

        Args:
            ticker (str): 股票代码
            start_time (int): 开始时间
            end_time (int): 结束时间
            event_time (Optional[int]): 事件时间，用于过滤数据

        Returns:
            Optional[np.ndarray]: 处理后的tick数据，若无有效数据则返回None

        Raises:
            RuntimeError: 如果 market_data_reader 未设置
        """
        self._validate_market_data_reader()

        aligned_ticks = await self._get_aligned_ticks(
            ticker=ticker,
            start_time=start_time,
            end_time=end_time,
        )
        if aligned_ticks is None:
            return None
        # 1. 找出买一价和卖一价都为0的tick（无效tick）
        invalid_bidask_idx = np.where(
            np.all(
                aligned_ticks[[FIELD_INDEX_MAPPING["stk_tick"][x] for x in ["BidPrice1", "AskPrice1"]]] == 0.0,
                axis=0,
            )
        )[0]

        # 2. 找出累计成交额大于0的tick（有成交的tick，通常只保留最后一个）
        acc_turnover_idx = np.where(aligned_ticks[FIELD_INDEX_MAPPING["stk_tick"]["AccTurnover"]] > 0)[0]

        # 3. 删除无效tick和多余的成交tick，只保留最后一个成交tick
        remove_idx = np.hstack((invalid_bidask_idx, acc_turnover_idx[:-1]))
        aligned_ticks = np.delete(aligned_ticks, remove_idx, axis=1)
        windowed_data = aligned_ticks

        windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]] = DiffAcc(
            windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]]
        )
        bn.replace(windowed_data, 0.0, np.nan)
        return windowed_data

    async def process_zp_ticks(
        self,
        ticker: str,
        start_time: int,
        end_time: int,
        timecut: str,
    ) -> Optional[np.ndarray]:
        """
        处理早盘阶段的tick数据。

        Args:
            ticker (str): 股票代码
            start_time (int): 开始时间
            end_time (int): 结束时间
            timecut (str): 时间切片

        Returns:
            Optional[np.ndarray]: 处理后的tick数据，若无有效数据则返回None

        Raises:
            RuntimeError: 如果 market_data_reader 未设置
        """
        self._validate_market_data_reader()

        aligned_ticks = await self._get_aligned_ticks(
            ticker=ticker,
            start_time=start_time,
            end_time=end_time,
        )
        if aligned_ticks is None:
            return None

        start_idx = np.searchsorted(aligned_ticks[0], 93000) - 1
        end_idx = np.searchsorted(aligned_ticks[0], int(timecut), side="right")
        windowed_data = aligned_ticks[:, start_idx:end_idx]

        # 处理累计值（批处理优势：可以一次性处理所有累计字段）
        windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]] = DiffAcc(
            windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]]
        )
        bn.replace(windowed_data, 0.0, np.nan)

        self.logger.info(f"windowed_data shape = {windowed_data.shape}")
        return windowed_data

    async def process_pz_ticks(
        self,
        ticker: str,
        start_time: int,
        end_time: int,
        timecut: str,
    ) -> Optional[np.ndarray]:
        """
        处理盘中阶段的tick数据。

        Args:
            ticker (str): 股票代码
            start_time (int): 开始时间
            end_time (int): 结束时间
            timecut (str): 时间切片

        Returns:
            Optional[np.ndarray]: 处理后的tick数据，若无有效数据则返回None

        Raises:
            RuntimeError: 如果 market_data_reader 未设置
        """
        self._validate_market_data_reader()

        aligned_ticks = await self._get_aligned_ticks(
            ticker=ticker,
            start_time=start_time,
            end_time=end_time,
        )
        if aligned_ticks is None:
            return None

        start_idx = np.searchsorted(aligned_ticks[0], 93000) - 1
        end_idx = np.searchsorted(aligned_ticks[0], int(timecut), side="right")
        windowed_data = aligned_ticks[:, start_idx:end_idx]

        # 处理午间休市数据（删除11:30:00~13:00:00之间的tick）
        mid_st = np.searchsorted(windowed_data[0], 113000, side="right")
        mid_ed = np.searchsorted(windowed_data[0], 130000)
        mid_break_idx = np.arange(mid_st, mid_ed)
        windowed_data = np.delete(windowed_data, mid_break_idx, axis=1)

        # 处理累计值（批处理优势：可以一次性处理所有累计字段）
        windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]] = DiffAcc(
            windowed_data[ACCUMULATED_FIELD_MAPPING["stk_tick"]]
        )
        bn.replace(windowed_data, 0.0, np.nan)

        return windowed_data

    async def _get_aligned_ticks(
        self,
        ticker: str,
        start_time: int,
        end_time: int,
    ) -> Optional[np.ndarray]:
        """
        获取对齐后的tick数据。

        Args:
            ticker (str): 股票代码
            start_time (int): 开始时间
            end_time (int): 结束时间

        Returns:
            Optional[np.ndarray]: 对齐后的tick数据，若无有效数据则返回None
        """
        # 获取历史Tick数据（批处理：获取完整时间区间的数据）
        # 线上实时处理时，这里会获取从开盘到当前的所有tick数据
        raw_tick_data: Optional[TickData] = self.market_data_reader.query_tick_values(
            ticker=ticker, start_time=start_time, end_time=end_time
        )

        if raw_tick_data is None or raw_tick_data.size == 0:
            return None

        # 预处理tick数据
        processed_ticks = self._preprocess_tick_data(raw_tick_data)
        if processed_ticks is None:
            self.logger.error("processed_ticks is None")
            return None

        # 提取关键字段
        column_indices = [
            self.market_data_reader.get_tick_column_mapping()[col] for col in MARKET_DATA_FIELDS.stock_tick
        ]
        price_volume_data = processed_ticks[column_indices]

        # 数据校验
        if not self._has_valid_price_data(price_volume_data):
            # 这里一直是False
            self.logger.error("has_valid_price_data is False")
            return None

        # 时间戳对齐
        aligned_ticks = self._align_timestamps(price_volume_data)
        return aligned_ticks

    def _preprocess_tick_data(self, raw_tick_records: TickData) -> TickData:
        """预处理tick数据记录，进行数据清洗和格式标准化

        处理流程：
        1. 数据堆叠：将多个tick记录合并为二维数组
        2. 精度标准化：统一数值精度为3位小数
        3. 时间戳格式化：转换为标准HHMMSSmmm格式
        4. 数据过滤：移除时间戳无效的记录

        Args:
            raw_tick_records: 原始tick数据记录列表，每个元素是一个tick记录数组
                            格式: List[np.ndarray]，其中每个数组包含一个tick的95个字段
                            示例: [tick_record_1, tick_record_2, ..., tick_record_39]

        Returns:
            np.ndarray: 预处理后的tick数据矩阵
                    形状: (valid_tick_count, 95)，其中valid_tick_count <= 原始记录数
                    格式: 每行是一个tick记录，第0列是标准化的时间戳

        """
        # 确保输入是有效的二维数组

        # 统一数值精度为3位小数，确保数据一致性
        tick_data_matrix: TickData = np.round(raw_tick_records, 3)

        # 确保 np.round 返回的仍然是二维数组
        if not isinstance(tick_data_matrix, np.ndarray) or tick_data_matrix.ndim != 2:
            return None

        # 标准化时间戳格式：转换为HHMMSSmmm格式
        # 第0列是时间戳字段，格式为纳秒级时间戳
        timestamp_column = tick_data_matrix[0]
        standardized_timestamps = np.round(timestamp_column / SECONDS_IN_MILLISECOND) * SECONDS_IN_MILLISECOND
        tick_data_matrix[0] = standardized_timestamps

        # 过滤掉时间戳无效的记录（时间戳 <= 0）
        # 保留时间戳大于0的有效记录
        valid_tick_mask = tick_data_matrix[0] > 0
        cleaned_tick_data = tick_data_matrix[:, valid_tick_mask]

        return cleaned_tick_data

    def _has_valid_price_data(self, price_volume_data: np.ndarray) -> bool:
        """检查是否有有效的价量数据"""
        data_to_be_checked: np.ndarray = price_volume_data[
            [
                FIELD_INDEX_MAPPING["stk_tick"][x]
                for x in [
                    "BidPrice1",
                    "AskPrice1",
                    "TotalBidVol",
                    "TotalAskVol",
                ]
            ]
        ]
        # 把data_to_be_checked中的0.0全部换成nan, inplace替换
        bn.replace(data_to_be_checked, 0.0, np.nan)
        return not bn.allnan(data_to_be_checked)

    def _align_timestamps(self, price_volume_data: np.ndarray) -> np.ndarray:
        """
        时间戳对齐到最近的时间切片

        参数:
            price_volume_data: 过滤后的Tick数据（原地修改）
        返回:
            对齐后的Tick数据
        """
        # 假设时间戳在0列，格式为HHMMSSmmm整数
        timestamps = price_volume_data[0]

        # 转换为秒级时间戳（示例对齐到整秒）
        aligned_ts = np.floor(timestamps % 1e9 / 1e3)
        price_volume_data[0] = aligned_ts

        return price_volume_data
