"""简化的处理器接口定义

为研究员提供极简的三阶段处理器接口
"""

import logging
from abc import ABC, abstractmethod
from typing import Optional

from ..merak_adapter import Tick

from ..data_types.standard_types import FeatureData, ProcessingResult


class FeatureCalculator(ABC):
    """特征计算器基类

    负责根据tick数据计算各种技术特征
    研究员需要继承此类并实现calculate_features方法
    """

    def __init__(self, name: str = "FeatureCalculator"):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")

        # 统计信息
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
        self.total_features_calculated = 0

    @abstractmethod
    def calculate_features(self, tick: Tick) -> Optional[dict[str, float]]:
        """根据tick数据计算特征

        Args:
            tick: merak标准的tick数据（63个字段的完整数据结构）

        Returns:
            dict[str, float]: 特征名到特征值的映射，如果计算失败返回None

        Note:
            研究员需要实现此方法，包含以下逻辑：
            1. 维护历史数据窗口（如价格序列）
            2. 计算技术指标（如移动平均、波动率等）
            3. 生成特征向量

        Example:
            def calculate_features(self, tick: Tick) -> Optional[dict[str, float]]:
                # 维护价格窗口
                self.price_window.append(tick.bp1)  # 使用买一价

                # 计算特征
                features = {
                    'sma_5': self._calculate_sma(5),
                    'volatility': self._calculate_volatility(),
                    'momentum': self._calculate_momentum(),
                    'spread': tick.ap1 - tick.bp1,  # 买卖价差
                    'mid_price': (tick.bp1 + tick.ap1) / 2,  # 中间价
                }

                return features
        """
        pass

    def get_stats(self) -> dict:
        """获取处理统计信息"""
        success_rate = self.processed_count / max(self.processed_count + self.failed_count, 1)
        avg_time = self.total_processing_time / max(self.processed_count, 1)
        avg_features = self.total_features_calculated / max(self.processed_count, 1)

        return {
            "name": self.name,
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "success_rate": success_rate,
            "avg_processing_time_ms": avg_time * 1000,
            "total_features_calculated": self.total_features_calculated,
            "avg_features_per_tick": avg_features,
        }

    def reset_stats(self):
        """重置统计信息"""
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
        self.total_features_calculated = 0


class FeatureDumper(ABC):
    """特征存储器基类

    负责将计算好的特征数据持久化存储
    研究员需要继承此类并实现dump_features方法
    """

    def __init__(self, name: str = "FeatureDumper"):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")

        # 统计信息
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
        self.total_records_saved = 0

    @abstractmethod
    def dump_features(self, features: FeatureData) -> ProcessingResult:
        """存储特征数据

        Args:
            features: 包含特征的数据对象

        Returns:
            ProcessingResult: 处理结果，包含成功/失败状态和相关信息

        Note:
            研究员需要实现此方法，包含以下逻辑：
            1. 数据格式转换（如转为DataFrame、JSON等）
            2. 存储到目标位置（文件、数据库、消息队列等）
            3. 错误处理和重试逻辑

        Example:
            def dump_features(self, features: FeatureData) -> ProcessingResult:
                try:
                    # 转换为存储格式
                    record = {
                        'ticker': features.ticker,
                        'timestamp': features.timestamp,
                        **features.features  # 展开所有特征
                    }

                    # 存储到数据库
                    self.database.insert(record)

                    return ProcessingResult.success_result(
                        ticker=features.ticker,
                        timestamp=features.timestamp,
                        records_saved=1
                    )
                except Exception as e:
                    return ProcessingResult.failure_result(
                        ticker=features.ticker,
                        timestamp=features.timestamp,
                        error_message=str(e)
                    )
        """
        pass

    def get_stats(self) -> dict:
        """获取处理统计信息"""
        success_rate = self.processed_count / max(self.processed_count + self.failed_count, 1)
        avg_time = self.total_processing_time / max(self.processed_count, 1)

        return {
            "name": self.name,
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "success_rate": success_rate,
            "avg_processing_time_ms": avg_time * 1000,
            "total_records_saved": self.total_records_saved,
        }

    def reset_stats(self):
        """重置统计信息"""
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
        self.total_records_saved = 0
