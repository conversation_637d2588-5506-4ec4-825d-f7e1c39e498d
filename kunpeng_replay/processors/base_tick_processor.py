"""Tick处理基类模块"""

from abc import ABC, abstractmethod
from typing import Optional

from kunpeng_replay.data_source.base_reader import MarketDataBaseReader
from ..merak_adapter import Tick

from ..events.market_data_event import MarketDataEvent


class BaseTickProcessor(ABC):
    """Tick处理基类，定义了处理tick数据的接口

    这个基类专注于核心的Tick处理功能，只依赖行情读取器
    1. MarketDataReader: 用于读取行情数据
    """

    def __init__(self):
        """初始化Tick处理器

        注意：构造时不设置 market_data_reader，需要后续通过 set_market_data_reader 方法设置
        """
        self.market_data_reader: Optional[MarketDataBaseReader] = None
        # 获取类名作为name,如果是衍生类，就获取衍生类的名称，而不是基类
        self.name = self.__class__.__name__

    def set_market_data_reader(self, market_data_reader: MarketDataBaseReader) -> None:
        """设置行情数据读取器

        Args:
            market_data_reader: 行情数据读取器实例
        """
        self.market_data_reader = market_data_reader

    def _validate_market_data_reader(self) -> None:
        """验证 market_data_reader 是否已设置

        Raises:
            RuntimeError: 如果 market_data_reader 未设置
        """
        if self.market_data_reader is None:
            raise RuntimeError("MarketDataReader not set. Please call set_market_data_reader() first.")

    @abstractmethod
    def process_tick(self, event: MarketDataEvent) -> Optional[Tick]:
        """处理原始行情事件，转换为merak标准tick数据

        Args:
            event: 原始行情事件

        Returns:
            Tick: merak标准的tick数据（63个字段），如果处理失败返回None

        Note:
            研究员需要实现此方法，包含以下逻辑：
            1. 数据验证和清洗
            2. 格式转换和标准化（转换为63字段的完整tick数据）
            3. 异常数据过滤
        """
        pass

    def get_stats(self) -> dict:
        """获取处理统计信息"""
        success_rate = self.processed_count / max(self.processed_count + self.failed_count, 1)
        avg_time = self.total_processing_time / max(self.processed_count, 1)

        return {
            "name": self.name,
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "success_rate": success_rate,
            "avg_processing_time_ms": avg_time * 1000,
        }

    def reset_stats(self):
        """重置统计信息"""
        self.processed_count = 0
        self.failed_count = 0
        self.total_processing_time = 0.0
