"""标准数据类型定义

定义replay engine中各个处理阶段的标准数据类型
"""

from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class TickData:
    """标准化的tick数据

    这是BaseTickProcessor的输出类型，FeatureCalculator的输入类型
    """

    ticker: str  # 股票代码，如 "000001.SZ"
    timestamp: int  # 时间戳（微秒）
    price: float  # 最新价格
    volume: int  # 成交量
    bid_price: float  # 买一价
    ask_price: float  # 卖一价
    bid_volume: int  # 买一量
    ask_volume: int  # 卖一量
    turnover: float  # 成交额

    # 可选的扩展字段
    high_price: Optional[float] = None  # 最高价
    low_price: Optional[float] = None  # 最低价
    open_price: Optional[float] = None  # 开盘价
    prev_close: Optional[float] = None  # 昨收价

    def __post_init__(self):
        """数据验证"""
        if self.price <= 0:
            raise ValueError(f"Invalid price: {self.price}")
        if self.volume < 0:
            raise ValueError(f"Invalid volume: {self.volume}")
        if self.bid_price <= 0 or self.ask_price <= 0:
            raise ValueError(f"Invalid bid/ask price: {self.bid_price}/{self.ask_price}")

    def get_mid_price(self) -> float:
        """获取中间价"""
        return (self.bid_price + self.ask_price) / 2.0

    def get_spread(self) -> float:
        """获取买卖价差"""
        return self.ask_price - self.bid_price

    def get_spread_bps(self) -> float:
        """获取买卖价差（基点）"""
        mid_price = self.get_mid_price()
        if mid_price > 0:
            return (self.get_spread() / mid_price) * 10000
        return 0.0


@dataclass
class FeatureData:
    """特征数据

    这是FeatureCalculator的输出类型，FeatureDumper的输入类型
    """

    ticker: str  # 股票代码
    timestamp: int  # 时间戳（微秒）
    features: Dict[str, float]  # 特征名 -> 特征值的映射
    metadata: Optional[Dict[str, Any]] = None  # 元数据，如计算参数、窗口大小等

    def __post_init__(self):
        """数据验证"""
        if not self.features:
            raise ValueError("Features dictionary cannot be empty")

        # 检查特征值是否有效
        for name, value in self.features.items():
            if not isinstance(value, (int, float)):
                raise ValueError(f"Feature '{name}' must be numeric, got {type(value)}")
            if not (-1e10 < value < 1e10):  # 防止极端值
                raise ValueError(f"Feature '{name}' value {value} is out of reasonable range")

    def get_feature(self, name: str, default: float = 0.0) -> float:
        """安全获取特征值"""
        return self.features.get(name, default)

    def has_feature(self, name: str) -> bool:
        """检查是否包含指定特征"""
        return name in self.features

    def get_feature_count(self) -> int:
        """获取特征数量"""
        return len(self.features)


@dataclass
class ProcessingResult:
    """处理结果

    这是FeatureDumper的输出类型，用于表示数据持久化的结果
    """

    success: bool  # 是否成功
    ticker: str  # 股票代码
    timestamp: int  # 时间戳
    records_saved: int  # 保存的记录数
    processing_time_ms: float  # 处理耗时（毫秒）
    error_message: Optional[str] = None  # 错误信息（如果失败）
    metadata: Optional[Dict[str, Any]] = None  # 额外的元数据

    def __post_init__(self):
        """数据验证"""
        if not self.success and not self.error_message:
            raise ValueError("Failed result must have error_message")
        if self.records_saved < 0:
            raise ValueError(f"Invalid records_saved: {self.records_saved}")
        if self.processing_time_ms < 0:
            raise ValueError(f"Invalid processing_time_ms: {self.processing_time_ms}")

    @classmethod
    def success_result(
        cls,
        ticker: str,
        timestamp: int,
        records_saved: int = 1,
        processing_time_ms: float = 0.0,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> "ProcessingResult":
        """创建成功结果"""
        return cls(
            success=True,
            ticker=ticker,
            timestamp=timestamp,
            records_saved=records_saved,
            processing_time_ms=processing_time_ms,
            metadata=metadata,
        )

    @classmethod
    def failure_result(
        cls, ticker: str, timestamp: int, error_message: str, processing_time_ms: float = 0.0
    ) -> "ProcessingResult":
        """创建失败结果"""
        return cls(
            success=False,
            ticker=ticker,
            timestamp=timestamp,
            records_saved=0,
            processing_time_ms=processing_time_ms,
            error_message=error_message,
        )


@dataclass
class ProcessingStats:
    """整体处理统计信息"""

    total_events: int = 0  # 总事件数
    processed_events: int = 0  # 成功处理的事件数
    failed_events: int = 0  # 失败的事件数

    # 各阶段统计
    tick_processing_failures: int = 0  # tick处理失败数
    feature_calculation_failures: int = 0  # 特征计算失败数
    feature_dumping_failures: int = 0  # 特征存储失败数

    # 性能统计
    total_processing_time: float = 0.0  # 总处理时间（秒）
    avg_processing_time_ms: float = 0.0  # 平均处理时间（毫秒）
    processing_rate: float = 0.0  # 处理速率（events/sec）

    # 时间统计
    start_time: Optional[float] = None  # 开始时间
    end_time: Optional[float] = None  # 结束时间

    def update_timing(self):
        """更新时间相关统计"""
        if self.start_time and self.end_time:
            self.total_processing_time = self.end_time - self.start_time
            if self.total_processing_time > 0:
                self.processing_rate = self.processed_events / self.total_processing_time
            if self.processed_events > 0:
                self.avg_processing_time_ms = (self.total_processing_time * 1000) / self.processed_events

    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_events == 0:
            return 0.0
        return self.processed_events / self.total_events

    def get_failure_rate(self) -> float:
        """获取失败率"""
        return 1.0 - self.get_success_rate()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_events": self.total_events,
            "processed_events": self.processed_events,
            "failed_events": self.failed_events,
            "success_rate": self.get_success_rate(),
            "failure_rate": self.get_failure_rate(),
            "tick_processing_failures": self.tick_processing_failures,
            "feature_calculation_failures": self.feature_calculation_failures,
            "feature_dumping_failures": self.feature_dumping_failures,
            "total_processing_time": self.total_processing_time,
            "avg_processing_time_ms": self.avg_processing_time_ms,
            "processing_rate": self.processing_rate,
        }
