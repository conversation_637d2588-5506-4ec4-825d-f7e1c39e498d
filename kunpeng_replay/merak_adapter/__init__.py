"""
Merak适配器模块

提供对merak.define.tick.Tick的本地适配，避免直接依赖外部merak包
"""

# 重新导出merak的Tick定义
try:
    from merak.define.tick import Tick
except ImportError:
    # 如果merak包不可用，提供一个本地的Tick定义
    from dataclasses import dataclass
    
    @dataclass(frozen=True)
    class Tick:
        """
        Tick数据结构 - 本地适配版本
        """
        # === 时间信息 ===
        tick_ut: str
        
        # === 买盘价格（10档深度）===
        bp1: float
        bp2: float
        bp3: float
        bp4: float
        bp5: float
        bp6: float
        bp7: float
        bp8: float
        bp9: float
        bp10: float
        
        # === 买盘量（10档深度）===
        bv1: float
        bv2: float
        bv3: float
        bv4: float
        bv5: float
        bv6: float
        bv7: float
        bv8: float
        bv9: float
        bv10: float
        
        # === 卖盘价格（10档深度）===
        ap1: float
        ap2: float
        ap3: float
        ap4: float
        ap5: float
        ap6: float
        ap7: float
        ap8: float
        ap9: float
        ap10: float
        
        # === 卖盘量（10档深度）===
        av1: float
        av2: float
        av3: float
        av4: float
        av5: float
        av6: float
        av7: float
        av8: float
        av9: float
        av10: float
        
        # === 基础价格信息 ===
        tick_pre_close: float
        tick_op: float
        tick_hp: float
        tick_lp: float
        
        # === 成交信息（已差分处理）===
        tick_vol: float
        tick_tvr: float
        
        # === 市场总量信息 ===
        bv_tot: float
        av_tot: float
        bvwp_tot: float
        avwp_tot: float
        
        # === 价格限制 ===
        up_limit_p: float
        down_limit_p: float

__all__ = ["Tick"]
