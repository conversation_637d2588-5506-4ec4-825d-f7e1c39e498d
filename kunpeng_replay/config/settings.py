"""回放配置管理"""

from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, Optional


class ReplayMode(Enum):
    """回放模式"""

    CONTINUOUS = "continuous"  # 连续时间模式
    EVENT_DRIVEN = "event_driven"  # 事件驱动模式


@dataclass
class ReplayConfig:
    """回放配置类"""

    # 基础配置
    tradingday: str = "20250218"
    start_time: str = "09:23:00"
    end_time: str = "15:00:00"

    # 模式配置
    mode: ReplayMode = ReplayMode.CONTINUOUS
    speed: int = 1  # 回放速度倍数（仅连续模式有效）

    # 事件驱动配置
    event_processing_timeout: int = 10  # 事件处理超时时间(秒)
    max_events_per_batch: int = 1000  # 每批处理的最大事件数

    # 数据源配置
    data_source_config: Optional[Dict[str, Any]] = None

    # 输出配置
    enable_logging: bool = True
    log_level: str = "INFO"
    progress_bar: bool = True

    # 性能配置
    memory_limit_mb: int = 1024  # 内存限制

    def __post_init__(self):
        if self.data_source_config is None:
            self.data_source_config = {}

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "ReplayConfig":
        """从字典创建配置"""
        # 处理mode字段
        if "mode" in config_dict and isinstance(config_dict["mode"], str):
            config_dict["mode"] = ReplayMode(config_dict["mode"])

        return cls(**config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, ReplayMode):
                result[key] = value.value
            else:
                result[key] = value
        return result
