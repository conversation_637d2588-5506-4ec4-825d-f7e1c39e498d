import warnings

import bottleneck as bn
import numpy as np

warnings.filterwarnings("ignore")


"""Single Input Func"""


# def Shift(arr, shift=1, axis=-1):
#     """
#     数组平移，功能类似pandas.Dataframe.shift()
#     :param arr: ndarray
#     :param shift: int，平移单位
#     :param axis: int，指定轴
#     :return:
#     """
#     if shift == 0:
#         return arr
#     if np.abs(shift) >= arr.shape[axis]:
#         return np.full(arr.shape, np.nan)
#     shift_arr = np.roll(arr, shift, axis)
#     if shift > 0:
#         sl = [slice(None)] * arr.ndim
#         sl[axis] = range(shift)
#         shift_arr[tuple(sl)] = np.nan
#     else:
#         sl = [slice(None)] * arr.ndim
#         sl[axis] = range(shift, 0)
#         shift_arr[tuple(sl)] = np.nan
#     return shift_arr


def Shift(arr, shift=1, axis=-1):
    if shift == 0:
        return arr.copy()

    axis = axis % arr.ndim
    size = arr.shape[axis]
    if abs(shift) >= size:
        return np.full_like(arr, np.nan, dtype=np.float64)

    # 初始化全nan数组
    result = np.empty_like(arr, dtype=np.float64)
    result.fill(np.nan)

    # 计算有效数据范围
    src_slice = [slice(None)] * arr.ndim
    dest_slice = [slice(None)] * arr.ndim

    if shift > 0:
        src_slice[axis] = slice(None, -shift)
        dest_slice[axis] = slice(shift, None)
    else:
        src_slice[axis] = slice(-shift, None)
        dest_slice[axis] = slice(None, shift)

    # 拷贝有效数据
    np.copyto(result[tuple(dest_slice)], arr[tuple(src_slice)])
    return result


# Shift = OptimizedShift


def Ret(arr, shift=1, axis=-1):
    """
    收益率
    :param arr: ndarray
    :param shift: int，平移单位
    :param axis: int，指定轴
    :return:
    """
    return Div(arr, Shift(arr, shift, axis)) - 1


def LnRet(arr, shift=1, axis=-1):
    """
    对数收益率
    :param arr: ndarray
    :param shift: int，平移单位
    :param axis: int，指定轴
    :return:
    """
    return Log(Div(arr, Shift(arr, shift, axis)))


def Diff(arr, shift=1, axis=-1):
    """
    一阶差分，返回数组的第一个切片为nan值
    :param arr: ndarray
    :param shift: int，平移单位
    :param axis: int，指定轴
    :return:
    """
    return arr - Shift(arr, shift, axis)


def FillDiff(arr, fill_value=0.0, shift=1, axis=-1):
    """
    填充后一阶差分，返回数组的第一个切片为nan值
    :param arr: ndarray
    :param fill_value: float，填充值
    :param shift: int，平移单位
    :param axis: int，指定轴
    :return:
    """
    return arr - Shift(FillNanBy(arr, fill_value), shift, axis)


def DiffAcc(arr, shift=1, axis=-1):
    """
    一阶差分，返回数组的第一个切片为原值，可用于累计量转换为非累积量
    :param arr: ndarray
    :param shift: int，平移单位
    :param axis: int，指定轴
    :return:
    """
    return arr - FillNanBy(Shift(arr, shift, axis), 0.0)


def Sqrt(arr):
    return np.sqrt(Filter(arr >= 0, arr))


def Log(arr):
    return np.log(Filter(arr > 0, arr))


def Neg(arr):
    return -arr


def Inv(arr):
    return Div(1.0, arr)


def Abs(arr):
    return np.abs(arr)


def NotNan(arr):
    return ~np.isnan(arr)


def Stack(seq, axis=-1):
    """
    沿新增轴拼接数组序列
    :param seq: a sequence of ndarrays
    :param axis: int，新增轴
    :return:
    """
    return np.stack(seq, axis)


def NanArrayMean(seq):
    """
    带nan值的数组序列求均值，可用于计算买卖中间价：涨跌停时一方价格为nan，则返回有值的一方，其他情况下返回买卖均值
    :param seq: a sequence of ndarrays, e.g. [bidprice1, askprice1]
    :return: ndarray
    """
    return NanMean(Stack(seq, 0), 0, True)


def NanArraySum(seq):
    return NanSumN(Stack(seq, 0), 0, 1, True)


def Last(arr, axis=-1):
    """
    指定轴最后一个元素
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    sl = [slice(None)] * arr.ndim
    sl[axis] = -1
    return arr[tuple(sl)]


def NanFirst(arr, axis=-1):
    sl = [slice(None)] * arr.ndim
    sl[axis] = range(arr.shape[axis] - 1, -1, -1)
    arr = bn.push(arr[tuple(sl)], axis=axis)
    sl[axis] = -1
    return arr[tuple(sl)]


def NanFirstN(arr, axis=-1, min_n=None):
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanFirst(arr, axis)
    else:
        res = NanFirst(arr, axis)
        res[na_cond] = np.nan
        return res


def NanSum(arr, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求和
    :param arr: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    if high_precision:
        arr_max = NanMax(arr, axis)
        na_cond = arr_max == NanMin(arr, axis)
        if arr.ndim == 1:
            if na_cond:
                return arr_max * NanCount(arr, axis)
            else:
                return bn.nansum(arr, axis)
        else:
            res = bn.nansum(arr, axis)
            res[na_cond] = (arr_max * NanCount(arr, axis))[na_cond]
            return res
    else:
        return bn.nansum(arr, axis)


def NanSumN(arr, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求和，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanSum(arr, axis, high_precision)
    else:
        res = NanSum(arr, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanMean(arr, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求均值
    :param arr: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    if high_precision:
        arr_max = NanMax(arr, axis)
        na_cond = arr_max == NanMin(arr, axis)
        if arr.ndim == 1:
            if na_cond:
                return arr_max
            else:
                return bn.nanmean(arr, axis)
        else:
            res = bn.nanmean(arr, axis)
            res[na_cond] = arr_max[na_cond]
            return res
    else:
        return bn.nanmean(arr, axis)


def NanMeanN(arr, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求均值，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanMean(arr, axis, high_precision)
    else:
        res = NanMean(arr, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanMedian(arr, axis=-1):
    """
    带nan值的数组沿指定轴求中位数
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    return bn.nanmedian(arr, axis)


def NanMedianN(arr, axis=-1, min_n=None):
    """
    带nan值的数组沿指定轴求中位数，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanMedian(arr, axis)
    else:
        res = NanMedian(arr, axis)
        res[na_cond] = np.nan
        return res


def NanStd(arr, axis=-1, ddof=1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求标准差
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    return Sqrt(NanVar(arr, axis, ddof, avg, high_precision))


def NanStdN(arr, axis=-1, ddof=1, min_n=None, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求标准差，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param min_n: int，最少样本量
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanStd(arr, axis, ddof, avg, high_precision)
    else:
        res = NanStd(arr, axis, ddof, avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanStdCoef(arr, axis=-1, ddof=1, avg=None, high_precision=False):
    return Div(
        NanStd(arr, axis, ddof, avg, high_precision),
        Abs(NanMean(arr, axis, True)),
    )


def NanStdCoefN(arr, axis=-1, ddof=1, min_n=None, avg=None, high_precision=False):
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanStdCoef(arr, axis, ddof, avg, high_precision)
    else:
        res = NanStdCoef(arr, axis, ddof, avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanUpStd(arr, axis=-1, ddof=1, avg=None, fillby=0.0, high_precision=False):
    """
    带nan值的数组沿指定轴求上行标准差
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param fillby:
    :param high_precision: bool，精度处理
    :return:
    """
    if avg is None:
        avg = np.expand_dims(NanMean(arr, axis, high_precision), axis)
    x_davg = arr - avg
    x_davg[x_davg <= 0.0] = fillby
    res = NanMean(x_davg**2, axis, high_precision)
    if ddof > 0:
        n = NanCount(x_davg, axis)
        res = res * n / (n - ddof)
    return np.sqrt(res)


def NanUpStdN(arr, axis=-1, ddof=1, min_n=None, avg=None, fillby=0.0, high_precision=False):
    """
    带nan值的数组沿指定轴求上行标准差，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param min_n: int，最少样本量
    :param avg:
    :param fillby:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2

    if avg is None:
        avg = np.expand_dims(NanMean(arr, axis, high_precision), axis)
    x_davg = arr - avg
    x_davg[x_davg <= 0.0] = fillby
    n = NanCount(x_davg, axis)

    na_cond = n < min_n
    if arr.ndim == 1:
        if na_cond:
            res = np.nan
        else:
            res = NanMean(x_davg**2, axis, high_precision)
            if ddof > 0:
                res = res * n / (n - ddof)
    else:
        res = NanMean(x_davg**2, axis, high_precision)
        if ddof > 0:
            res = res * n / (n - ddof)
        res[na_cond] = np.nan
    return np.sqrt(res)


def NanDownStd(arr, axis=-1, ddof=1, avg=None, fillby=0.0, high_precision=False):
    """
    带nan值的数组沿指定轴求下行标准差
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param fillby:
    :param high_precision: bool，精度处理
    :return:
    """
    if avg is None:
        avg = np.expand_dims(NanMean(arr, axis, high_precision), axis)
    x_davg = arr - avg
    x_davg[x_davg >= 0.0] = fillby
    res = NanMean(x_davg**2, axis, high_precision)
    if ddof > 0:
        n = NanCount(x_davg, axis)
        res = res * n / (n - ddof)
    return np.sqrt(res)


def NanDownStdN(arr, axis=-1, ddof=1, min_n=None, avg=None, fillby=0.0, high_precision=False):
    """
    带nan值的数组沿指定轴求下行标准差，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param min_n: int，最少样本量
    :param avg:
    :param fillby:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2

    if avg is None:
        avg = np.expand_dims(NanMean(arr, axis, high_precision), axis)
    x_davg = arr - avg
    x_davg[x_davg >= 0.0] = fillby
    n = NanCount(x_davg, axis)

    na_cond = n < min_n
    if arr.ndim == 1:
        if na_cond:
            res = np.nan
        else:
            res = NanMean(x_davg**2, axis, high_precision)
            if ddof > 0:
                res = res * n / (n - ddof)
    else:
        res = NanMean(x_davg**2, axis, high_precision)
        if ddof > 0:
            res = res * n / (n - ddof)
        res[na_cond] = np.nan
    return np.sqrt(res)


def NanVar(arr, axis=-1, ddof=1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求方差
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if avg is None:
        if high_precision:
            na_cond = NanMax(arr, axis) == NanMin(arr, axis)
            if arr.ndim == 1:
                if na_cond:
                    return 0.0
                else:
                    return bn.nanvar(arr, axis, ddof)
            else:
                res = bn.nanvar(arr, axis, ddof)
                res[na_cond] = 0.0
                return res
        else:
            return bn.nanvar(arr, axis, ddof)
    else:
        x_davg = arr - avg
        res = NanMean(x_davg**2, axis, high_precision)
        if ddof > 0:
            n = NanCount(arr, axis)
            res = res * n / (n - ddof)
        return res


def NanVarN(arr, axis=-1, ddof=1, min_n=None, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求方差，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param min_n: int，最少样本量
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanVar(arr, axis, ddof, avg, high_precision)
    else:
        res = NanVar(arr, axis, ddof, avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanSkew(arr, axis=-1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求偏度
    :param arr: ndarray
    :param axis: int，指定轴
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    z = ZScoreMean(arr, axis, 1, avg, high_precision)
    count = NanCount(z, axis) * 1.0
    bias_adj = Div(count, (count - 1) * (count - 2))
    skew = bias_adj * NanSumN(z**3, axis, 1, high_precision)
    return skew


def NanSkewN(arr, axis=-1, min_n=None, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求偏度，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanSkew(arr, axis, avg, high_precision)
    else:
        res = NanSkew(arr, axis, avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanKurt(arr, axis=-1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求峰度
    :param arr: ndarray
    :param axis: int，指定轴
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    z = ZScoreMean(arr, axis, 1, avg, high_precision)
    count = NanCount(z, axis) * 1.0
    bias_adj = Div(count * (count + 1), (count - 1) * (count - 2) * (count - 3))
    sub = Div(3 * (count - 1) ** 2, (count - 2) * (count - 3))
    kurt = bias_adj * NanSumN(z**4, axis, 1, high_precision) - sub
    return kurt


def NanKurtN(arr, axis=-1, min_n=None, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴求峰度，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = NanCount(arr, axis) < min_n
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return NanKurt(arr, axis, avg, high_precision)
    else:
        res = NanKurt(arr, axis, avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanMin(arr, axis=-1):
    """
    带nan值的数组沿指定轴求最小值
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    return bn.nanmin(arr, axis)


def NanMax(arr, axis=-1):
    """
    带nan值的数组沿指定轴求最大值
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    return bn.nanmax(arr, axis)


def NanArgMin(arr, axis=-1):
    na_cond = bn.allnan(arr, axis)
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return bn.nanargmin(arr, axis)
    else:
        arr_c = np.where(np.expand_dims(na_cond, axis), 0, arr)
        res = bn.nanargmin(arr_c, axis).astype(np.float64)
        res[na_cond] = np.nan
    return res


def NanArgMax(arr, axis=-1):
    na_cond = bn.allnan(arr, axis)
    if arr.ndim == 1:
        if na_cond:
            return np.nan
        else:
            return bn.nanargmax(arr, axis)
    else:
        arr_c = np.where(np.expand_dims(na_cond, axis), 0, arr)
        res = bn.nanargmax(arr_c, axis).astype(np.float64)
        res[na_cond] = np.nan
    return res


def NanRank(arr, axis=-1):
    """
    带nan值的数组沿指定轴的排名，排名从1开始，值越小的排名越低，值相同的取均值
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    return bn.nanrankdata(arr, axis)


def NanPctRank(arr, axis=-1):
    return Div(
        NanRank(arr, axis),
        np.expand_dims(NanCount(arr, axis).astype(np.float64), axis=axis),
    )


def NanPctRankN(arr, axis=-1, min_n=None):
    if min_n is None:
        min_n = arr.shape[axis] // 2
    na_cond = np.expand_dims(NanCount(arr, axis), axis) < min_n
    res = np.where(na_cond, np.nan, NanPctRank(arr, axis))
    return res


def NanCount(arr, axis=-1):
    """
    带nan值的数组沿指定轴计算非nan值的数量
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    return np.sum(~np.isnan(arr), axis)


def NanWMean(x, w, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求加权均值
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    res = Div(NanSum(x * w, axis), NanSum(w, axis))

    if high_precision:
        arr_max = NanMax(x, axis)
        na_cond = arr_max == NanMin(x, axis)
        if x.ndim == 1:
            if na_cond:
                return arr_max
            else:
                return res
        else:
            res[na_cond] = arr_max[na_cond]
            return res
    else:
        return res


def NanWMeanN(x, w, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求加权均值，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWMean(x, w, axis, high_precision)
    else:
        res = NanWMean(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanWVar(x, w, axis=-1, high_precision=False):
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    v1 = NanSum(w, axis)
    m1 = Div(NanSum(x * w, axis), v1)
    res = Div(NanSumN((x - m1) ** 2 * w, axis, min_n=1), v1)

    if high_precision:
        na_cond = NanMax(x, axis) == NanMin(x, axis)
        if x.ndim == 1:
            if na_cond:
                return 0.0
            else:
                return res
        else:
            res[na_cond] = 0.0
            return res
    else:
        return res


def NanWVarN(x, w, axis=-1, min_n=None, high_precision=False):
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWVar(x, w, axis, high_precision)
    else:
        res = NanWVar(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanWStd(x, w, axis=-1, high_precision=False):
    return Sqrt(NanWVar(x, w, axis, high_precision))


def NanWStdN(x, w, axis=-1, min_n=None, high_precision=False):
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWStd(x, w, axis, high_precision)
    else:
        res = NanWStd(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanWSkew(x, w, axis=-1, high_precision=False):
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    v1 = NanSum(w, axis)
    m1 = Div(NanSum(x * w, axis), v1)
    m2 = Div(NanSumN((x - m1) ** 2 * w, axis, min_n=1), v1)
    res = Div(NanSumN(Div(x - m1, np.sqrt(m2)) ** 3 * w, axis, min_n=1), v1)

    if high_precision:
        na_cond = NanMax(x, axis) == NanMin(x, axis)
        if x.ndim == 1:
            if na_cond:
                return np.nan
            else:
                return res
        else:
            res[na_cond] = np.nan
            return res
    else:
        return res


def NanWSkewN(x, w, axis=-1, min_n=None, high_precision=False):
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWSkew(x, w, axis, high_precision)
    else:
        res = NanWSkew(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanWKurt(x, w, axis=-1, high_precision=False):
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    v1 = NanSum(w, axis)
    m1 = Div(NanSum(x * w, axis), v1)
    m2 = Div(NanSumN((x - m1) ** 2 * w, axis, min_n=1), v1)
    res = Div(NanSumN(Div(x - m1, np.sqrt(m2)) ** 4 * w, axis, min_n=1), v1) - 3

    if high_precision:
        na_cond = NanMax(x, axis) == NanMin(x, axis)
        if x.ndim == 1:
            if na_cond:
                return np.nan
            else:
                return res
        else:
            res[na_cond] = np.nan
            return res
    else:
        return res


def NanWKurtN(x, w, axis=-1, min_n=None, high_precision=False):
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWKurt(x, w, axis, high_precision)
    else:
        res = NanWKurt(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanEMean(x, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求指数加权均值
    :param x: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    n = x.shape[axis]
    a = 2 / (n + 1)
    w = a * (1 - a) ** np.arange(n - 1, -1, -1)
    return NanWMean(x, w, axis, high_precision)


def NanEMeanN(x, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求指数加权均值，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanEMean(x, axis, high_precision)
    else:
        res = NanEMean(x, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanWSum(x, w, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求加权和
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    return Div(NanSum(x * w, axis, high_precision), NanSum(w, axis))


def NanWSumN(x, w, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求加权和，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanWSum(x, w, axis, high_precision)
    else:
        res = NanWSum(x, w, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanESum(x, axis=-1, high_precision=False):
    """
    带nan值的数组沿指定轴求指数加权和
    :param x: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    n = x.shape[axis]
    a = 2 / (n + 1)
    w = a * (1 - a) ** np.arange(n - 1, -1, -1)
    return NanWSum(x, w, axis, high_precision)


def NanESumN(x, axis=-1, min_n=None, high_precision=False):
    """
    带nan值的数组沿指定轴求指数加权和，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanESum(x, axis, high_precision)
    else:
        res = NanESum(x, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanTStat(x, axis=-1, pop_avg=0, high_precision=False):
    x_avg = NanMean(x, axis, high_precision)
    x_se = Div(NanStd(x, axis, 1, None, True), Sqrt(NanCount(x, axis)))
    x_tstat = Div(x_avg - pop_avg, x_se)
    return x_tstat


def NanTStatN(x, axis=-1, min_n=None, pop_avg=0, high_precision=False):
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanTStat(x, axis, pop_avg, high_precision)
    else:
        res = NanTStat(x, axis, pop_avg, high_precision)
        res[na_cond] = np.nan
        return res


def NanTBeta(x, axis=-1, ignore_nan=True, high_precision=False):
    """
    带nan值的数组沿指定轴求时序回归系数
    :param x: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    sl = np.ones(len(x.shape), dtype=np.int32)
    sl[axis] = -1
    if ignore_nan:
        t = np.ones(x.shape[axis]).reshape(sl)
        t = t + x * 0
        t = np.nancumsum(t, axis)
    else:
        t = np.arange(x.shape[axis]).reshape(sl)
    return NanBeta(t, x, axis, high_precision)


def NanTBetaN(x, axis=-1, min_n=None, ignore_nan=True, high_precision=False):
    """
    带nan值的数组沿指定轴求时序回归系数，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanTBeta(x, axis, ignore_nan, high_precision)
    else:
        res = NanTBeta(x, axis, ignore_nan, high_precision)
        res[na_cond] = np.nan
        return res


def NanTBetaTStat(x, axis=-1, ignore_nan=True, high_precision=False):
    """
    带nan值的数组沿指定轴求时序回归系数t统计值
    :param x: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    sl = np.ones(len(x.shape), dtype=np.int32)
    sl[axis] = -1
    if ignore_nan:
        t = np.ones(x.shape[axis]).reshape(sl)
        t = t + x * 0
        t = np.nancumsum(t, axis)
    else:
        t = np.arange(x.shape[axis]).reshape(sl)
    return NanBetaTStat(t, x, axis, high_precision)


def NanTBetaTStatN(x, axis=-1, min_n=None, ignore_nan=True, high_precision=False):
    """
    带nan值的数组沿指定轴求时序回归系数t统计值，样本量少于min_n的切片替换为nan值
    :param arr: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanTBetaTStat(x, axis, ignore_nan, high_precision)
    else:
        res = NanTBetaTStat(x, axis, ignore_nan, high_precision)
        res[na_cond] = np.nan
        return res


def NanAbsDiffMean(x, fill_value=0, shift=1, axis=-1, high_precision=False):
    x_diff = NanMean(Abs(FillDiff(x, fill_value, shift, axis)), axis, high_precision)
    x_avg = Abs(NanMean(x, axis, True))
    return Div(x_diff, x_avg)


def MoveFirst(arr, n, axis=-1, min_n=None):
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)

    na_cond = MoveCount(arr, n, axis) < min_n
    sl = [slice(None)] * arr.ndim
    sl[axis] = range(arr.shape[axis] - 1, -1, -1)
    res = bn.push(Shift(bn.push(arr[tuple(sl)], axis=axis), -(n - 1), axis), axis=axis)[tuple(sl)]
    res[na_cond] = np.nan
    return res


def MoveSum(arr, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求和，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if high_precision:
        arr_max = MoveMax(arr, n, axis, min_n)
        na_cond = arr_max == MoveMin(arr, n, axis, min_n)
        res = bn.move_sum(arr, n, min_n, axis)
        res[na_cond] = (arr_max * MoveCount(arr, n, axis))[na_cond]
        return res
    else:
        return bn.move_sum(arr, n, min_n, axis)


def MoveMean(arr, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求均值，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if high_precision:
        arr_max = MoveMax(arr, n, axis, min_n)
        na_cond = arr_max == MoveMin(arr, n, axis, min_n)
        res = bn.move_mean(arr, n, min_n, axis)
        res[na_cond] = arr_max[na_cond]
        return res
    else:
        return bn.move_mean(arr, n, min_n, axis)


def MoveMedian(arr, n, axis=-1, min_n=None):
    """
    沿指定轴滚动求中位数，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    return bn.move_median(arr, n, min_n, axis)


def MoveStd(arr, n, axis=-1, ddof=1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求标准差，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if high_precision:
        na_cond = MoveMax(arr, n, axis, min_n) == MoveMin(arr, n, axis, min_n)
        res = bn.move_std(arr, n, min_n, axis, ddof)
        res[na_cond] = 0.0
        return res
    else:
        return bn.move_std(arr, n, min_n, axis, ddof)


def MoveUpStd(arr, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求上行标准差，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    up_arr = np.where(arr < 0, 0, arr)
    res = MoveMean(up_arr**2.0, n, axis, min_n, high_precision)
    return np.sqrt(res)


def MoveDownStd(arr, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求下行标准差，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    down_arr = np.where(arr > 0, 0, arr)
    res = MoveMean(down_arr**2.0, n, axis, min_n, high_precision)
    return np.sqrt(res)


def MoveVar(arr, n, axis=-1, ddof=1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求方差，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if high_precision:
        na_cond = MoveMax(arr, n, axis, min_n) == MoveMin(arr, n, axis, min_n)
        res = bn.move_var(arr, n, min_n, axis, ddof)
        res[na_cond] = 0.0
        return res
    else:
        return bn.move_var(arr, n, min_n, axis, ddof)


def MoveTStat(x, n, axis=-1, pop_avg=0, min_n=None, high_precision=False):
    x_avg = MoveMean(x, n, axis, min_n, high_precision)
    x_se = Div(MoveStd(x, n, axis, 1, min_n, True), Sqrt(MoveCount(x, n, axis)))
    x_tstat = Div(x_avg - pop_avg, x_se)
    return x_tstat


def MoveSkew(x, n, axis=-1, min_n=None, high_precision=False):
    n = min(n, x.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    k2 = MoveVar(x, n, axis, 1, min_n, True)
    cnt = MoveCount(x, n, axis) * 1.0
    x_mean = MoveMean(x, n, axis, min_n, high_precision)
    x2_mean = MoveMean(x**2, n, axis, min_n, high_precision)
    x3_mean = MoveMean(x**3, n, axis, min_n, high_precision)
    m3 = x3_mean - 3 * x2_mean * x_mean + 2 * x_mean**3
    bias_adj = Div(cnt**2, (cnt - 1) * (cnt - 2))
    skew = Div(bias_adj * m3, k2**1.5)
    return skew


def MoveKurt(x, n, axis=-1, min_n=None, high_precision=False):
    n = min(n, x.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    cnt = MoveCount(x, n, axis) * 1.0
    m2 = MoveVar(x, n, axis, 0, min_n, True)
    x_mean = MoveMean(x, n, axis, min_n, high_precision)
    x2_mean = MoveMean(x**2, n, axis, min_n, high_precision)
    x3_mean = MoveMean(x**3, n, axis, min_n, high_precision)
    x4_mean = MoveMean(x**4, n, axis, min_n, high_precision)
    m4 = x4_mean - 4 * x3_mean * x_mean + 6 * x2_mean * x_mean**2 - 3 * x_mean**4
    bias_adj = Div((cnt - 1) * (cnt + 1), (cnt - 2) * (cnt - 3))
    sub = Div(3 * (cnt - 1) ** 2, (cnt - 2) * (cnt - 3))
    kurt = bias_adj * Div(m4, m2**2) - sub
    return kurt


def MoveMax(arr, n, axis=-1, min_n=None):
    """
    沿指定轴滚动求最大值，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    return bn.move_max(arr, n, min_n, axis)


def MoveMin(arr, n, axis=-1, min_n=None):
    """
    沿指定轴滚动求最小值，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    return bn.move_min(arr, n, min_n, axis)


def MoveArgMax(arr, n, axis=-1, min_n=None):
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    return bn.move_argmax(arr, n, min_n, axis)


def MoveArgMin(arr, n, axis=-1, min_n=None):
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    return bn.move_argmin(arr, n, min_n, axis)


def MoveZScore(arr, n, axis=-1, ddof=1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求ZScore，可处理nan值，不可处理inf值
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, arr.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    move_mean = MoveMean(arr, n, axis, min_n, high_precision)
    move_std = MoveStd(arr, n, axis, ddof, min_n, True)
    return Div(arr - move_mean, move_std)


def MoveCount(arr, n, axis=-1):
    """
    沿指定轴滚动求非nan值数量
    :param arr: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :return:
    """
    return np.round(MoveSum((~np.isnan(arr)).astype(np.float64), n, axis, 1, False), 0)


def MoveWMean(x, w, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求加权均值
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, x.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    xw_sum = MoveSum(x * w, n, axis, min_n)
    w_sum = MoveSum(w, n, axis, min_n)
    res = Div(xw_sum, w_sum)

    if high_precision:
        arr_max = MoveMax(x, n, axis, min_n)
        na_cond = arr_max == MoveMin(x, n, axis, min_n)
        res[na_cond] = arr_max[na_cond]

    return res


def MoveEMean(x, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求指数加权均值
    :param x: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    a = 2 / (n + 1)
    w = a * (1 - a) ** np.arange(x.shape[axis] - 1, -1, -1)
    return MoveWMean(x, w, n, axis, min_n, high_precision)


def MoveWSum(x, w, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求加权和
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, x.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    xw_sum = MoveSum(x * w, n, axis, min_n, high_precision)
    w_sum = MoveSum(w, n, axis, min_n, high_precision)
    return Div(xw_sum, w_sum)


def MoveWVar(x, w, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求加权方差
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    n = min(n, x.shape[axis])
    min_n = max(n // 2, 1) if min_n is None else min(min_n, n)
    if x.shape != w.shape:
        sl = np.ones(len(x.shape), dtype=np.int32)
        sl[axis] = -1
        w = w.reshape(sl)
    w = w + x * 0
    v1 = MoveSum(w, n, axis, min_n, high_precision)
    m1 = Div(MoveSum(x * w, n, axis, min_n, high_precision), v1)
    m2 = MoveSum((x - m1) ** 2 * w, n, axis, min_n, high_precision)
    v2 = MoveSum(w**2, n, axis, min_n, high_precision)
    return m2 * Div(v1, v1**2 - v2)


def MoveWStd(x, w, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求加权标准差
    :param x: ndarray
    :param w: ndarray，权重，支持Broadcast
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    return np.sqrt(MoveWVar(x, w, n, axis, min_n, high_precision))


def MoveAbsDiffMean(x, n, shift=1, axis=-1, min_n=None, high_precision=False):
    x_diff = MoveMean(Abs(Diff(x, shift, axis)), n, axis, min_n, high_precision)
    x_avg = Abs(MoveMean(x, n, axis, min_n, True))
    return Div(x_diff, x_avg)


def MinMaxScaler(arr, axis=-1):
    """
    带nan值的数组沿指定轴作0-1标准化
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    arr_min = np.expand_dims(NanMin(arr, axis), axis)
    arr_max = np.expand_dims(NanMax(arr, axis), axis)
    return Div(arr - arr_min, arr_max - arr_min)


def MaxAbsScaler(arr, axis=-1):
    arr_abs_max = np.expand_dims(NanMax(Abs(arr), axis), axis)
    return Div(arr, arr_abs_max)


def TanhScaler(arr, axis=-1, alpha=None, bound=1):
    if alpha is None:
        alpha = Div(2, NanStdN(arr, axis, high_precision=True))
    return bound * Div(1 - np.exp(-alpha * arr), 1 + np.exp(-alpha * arr))


def ZScoreMean(x, axis=-1, ddof=1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴作ZScore标准化，减的是均值
    :param x: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    if avg is None:
        avg = np.expand_dims(NanMean(x, axis, high_precision), axis)
    std = np.expand_dims(NanStd(x, axis, ddof, avg, True), axis)
    z_score = Div(x - avg, std)
    return z_score


def ZScoreMedian(arr, axis=-1, ddof=1, avg=None, high_precision=False):
    """
    带nan值的数组沿指定轴作ZScore标准化，减的是中位数
    :param arr: ndarray
    :param axis: int，指定轴
    :param ddof: int，自由度
    :param avg:
    :param high_precision: bool，精度处理
    :return:
    """
    median = np.expand_dims(NanMedian(arr, axis), axis)
    std = np.expand_dims(NanStd(arr, axis, ddof, avg, True), axis)
    z_score = Div((arr - median), std)
    return z_score


def CutOutlierQ(arr, q=0.01, axis=0):
    """
    带nan值的数组沿指定轴去极端值
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    upper = np.nanquantile(arr, 1 - q, axis, keepdims=True)
    lower = np.nanquantile(arr, q, axis, keepdims=True)
    res = np.where(arr > upper, upper, arr)
    res = np.where(res < lower, lower, res)
    return res


def CutOutlierMAD(arr, k=7, axis=0, drop=False):
    """
    带nan值的数组沿指定轴去极端值
    :param arr: ndarray
    :param axis: int，指定轴
    :return:
    """
    median = bn.nanmedian(arr, axis)
    distance = bn.nanmedian(np.abs(arr - np.expand_dims(median, axis)), axis)
    upper = np.expand_dims(median + k * 1.4826 * distance, axis)
    lower = np.expand_dims(median - k * 1.4826 * distance, axis)
    if drop:
        res = np.where((arr > upper) | (arr < lower), np.nan, arr)
    else:
        res = np.where(arr > upper, upper, arr)
        res = np.where(res < lower, lower, res)
    return res


"""Double Input Func"""


def Add(x, y):
    return x + y


def Sub(x, y):
    return x - y


def Mul(x, y):
    return x * y


def Div(x, y):
    """
    除法，分母为0时返回nan值
    :param x: ndarray
    :param y: ndarray
    :return:
    """
    if hasattr(y, "__len__"):
        y = y.copy()
        return x / bn.replace(y, 0.0, np.nan)
    else:
        if y == 0.0:
            if hasattr(x, "__len__"):
                return x / np.array([np.nan])
            else:
                return np.nan
        else:
            return x / y


def Imbalance(x, y):
    return Div(x - y, Abs(x) + Abs(y))


def FMax(x, y):
    return np.fmax(x, y)


def FMin(x, y):
    return np.fmin(x, y)


def Larger(x, y):
    return x > y


def Smaller(x, y):
    return x < y


def Equal(x, y):
    return x == y


def LargerEqual(x, y):
    return x >= y


def SmallerEqual(x, y):
    return x <= y


def Filter(x, y):
    """
    根据条件x筛选y，不符合的赋值nan
    :param x: ndarray，判断条件，布尔型
    :param y: ndarray，被筛选的数组
    """
    return np.where(x, y, np.nan)


def FillNanBy(x, y):
    """
    填充x中的nan值为y
    :param x: ndarray，判断条件，布尔型
    :param y: ndarray or scalar
    """
    return np.where(np.isnan(x), y, x)


def NanCov(x, y, axis=-1, high_precision=False):
    """
    沿指定轴求x与y（带nan值）的两两协方差
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    count = NanCount(x, axis)
    bias_adj = count / (count - 1)
    x_avg = np.expand_dims(NanMean(x, axis, high_precision), axis)
    y_avg = np.expand_dims(NanMean(y, axis, high_precision), axis)
    cov = NanMean((x - x_avg) * (y - y_avg), axis, high_precision) * bias_adj
    return cov


def NanCovN(x, y, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴求x与y（带nan值）的两两协方差，样本量少于min_n的切片替换为nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanCov(x, y, axis, high_precision)
    else:
        res = NanCov(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanCorr(x, y, axis=-1, high_precision=False):
    """
    沿指定轴求x与y（带nan值）的两两相关系数
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    cov = NanMean(x * y, axis, high_precision) - NanMean(x, axis, high_precision) * NanMean(y, axis, high_precision)
    std_x = NanStd(x, axis, 0, None, True)
    std_y = NanStd(y, axis, 0, None, True)
    corr = Div(cov, std_x * std_y)
    if isinstance(corr, float):
        if Abs(corr) > 1:
            corr = np.nan
    else:
        corr[Abs(corr) > 1] = np.nan
    return corr


def NanCorrN(x, y, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴求x与y（带nan值）的两两相关系数，样本量少于min_n的切片替换为nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanCorr(x, y, axis, high_precision)
    else:
        res = NanCorr(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanCosSim(x, y, axis=-1, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    dot_prod = NanMean(x * y, axis, high_precision)
    mod_x = Sqrt(NanMean(x**2, axis, True))
    mod_y = Sqrt(NanMean(y**2, axis, True))
    cossim = Div(dot_prod, mod_x * mod_y)
    if isinstance(cossim, float):
        if cossim > 1:
            cossim = np.nan
    else:
        cossim[cossim > 1] = np.nan
    return cossim


def NanCosSimN(x, y, axis=-1, min_n=None, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanCosSim(x, y, axis, high_precision)
    else:
        res = NanCosSim(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanCohenD(x, y, axis=-1, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    x_cnt = NanCount(x, axis)
    y_cnt = NanCount(y, axis)
    x_avg = NanMean(x, axis, high_precision)
    y_avg = NanMean(y, axis, high_precision)
    x_var = NanVar(x, axis, 0, None, True)
    y_var = NanVar(y, axis, 0, None, True)
    d = Div(
        x_avg - y_avg,
        Sqrt(Div(x_var * x_cnt + y_var * y_cnt, x_cnt + y_cnt - 2)),
    )
    return d


def NanCohenDN(x, y, axis=-1, min_n=None, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanCohenD(x, y, axis, high_precision)
    else:
        res = NanCohenD(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanBeta(x, y, axis=-1, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的回归系数，可处理nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    x_avg = np.expand_dims(NanMean(x, axis, high_precision), axis)
    y_avg = np.expand_dims(NanMean(y, axis, high_precision), axis)
    cov = NanMean((x - x_avg) * (y - y_avg), axis, high_precision)
    var_x = NanVar(x, axis, 0, None, True)
    beta = Div(cov, var_x)
    return beta


def NanBetaN(x, y, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的回归系数，可处理nan值，样本量少于min_n的切片替换为nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanBeta(x, y, axis, high_precision)
    else:
        res = NanBeta(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def NanResi(x, y, axis=-1, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的残差，可处理nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    x_avg = NanMean(x, axis, high_precision)
    y_avg = NanMean(y, axis, high_precision)
    cov = NanMean(
        (x - np.expand_dims(x_avg, axis)) * (y - np.expand_dims(y_avg, axis)),
        axis,
        high_precision,
    )
    var_x = NanVar(x, axis, 0, None, True)
    beta = Div(cov, var_x)
    cons = y_avg - beta * x_avg
    resi = y - (np.expand_dims(cons, axis) + np.expand_dims(beta, axis) * x)
    return resi


def NanResiN(x, y, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的残差，可处理nan值，样本量少于min_n的切片替换为nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = np.expand_dims(NanCount(x, axis), axis) < min_n
    res = np.where(na_cond, np.nan, NanResi(x, y, axis, high_precision))
    return res


def NanBetaTStat(x, y, axis=-1, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的t统计值，可处理nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    x_avg = NanMean(x, axis, high_precision)
    y_avg = NanMean(y, axis, high_precision)
    cov = NanMean(
        (x - np.expand_dims(x_avg, axis)) * (y - np.expand_dims(y_avg, axis)),
        axis,
        high_precision,
    )
    var_x = NanVar(x, axis, 0, None, True)
    beta = Div(cov, var_x)
    cons = y_avg - beta * x_avg
    resi = y - (np.expand_dims(cons, axis) + np.expand_dims(beta, axis) * x)
    mse = NanMean(resi**2, axis, high_precision)
    se = np.sqrt(Div(mse, var_x * (NanCount(x, axis) - 2)))
    tstat = Div(beta, se)
    return tstat


def NanBetaTStatN(x, y, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴求一元线性方程（带截距项）的t统计值，可处理nan值，样本量少于min_n的切片替换为nan值
    :param x: ndarray
    :param y: ndarray
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    if min_n is None:
        min_n = x.shape[axis] // 2
    na_cond = NanCount(x, axis) < min_n
    if len(x.shape) == 1:
        if na_cond:
            return np.nan
        else:
            return NanBetaTStat(x, y, axis, high_precision)
    else:
        res = NanBetaTStat(x, y, axis, high_precision)
        res[na_cond] = np.nan
        return res


def MoveCov(x, y, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求x与y的两两协方差，可处理nan值，不可处理inf值
    :param x: ndarray
    :param y: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    count = MoveCount(x, n, axis)
    bias_adj = count / (count - 1)
    cov = (
        MoveMean(x * y, n, axis, min_n, high_precision)
        - MoveMean(x, n, axis, min_n, high_precision) * MoveMean(y, n, axis, min_n, high_precision)
    ) * bias_adj
    return cov


def MoveCorr(x, y, n, axis=-1, min_n=None, high_precision=False):
    """
    沿指定轴滚动求x与y的两两相关系数，可处理nan值，不可处理inf值
    :param x: ndarray
    :param y: ndarray
    :param n: int，滚动窗口
    :param axis: int，指定轴
    :param min_n: int，最少样本量
    :param high_precision: bool，精度处理
    :return:
    """
    x = x + y * 0
    y = y + x * 0
    cov = MoveMean(x * y, n, axis, min_n, high_precision) - MoveMean(x, n, axis, min_n, high_precision) * MoveMean(
        y, n, axis, min_n, high_precision
    )
    std_x = MoveStd(x, n, axis, 0, min_n, True)
    std_y = MoveStd(y, n, axis, 0, min_n, True)
    corr = Div(cov, std_x * std_y)
    corr[Abs(corr) > 1] = np.nan
    return corr


def MoveTBeta(x, n, axis=-1, min_n=None, ignore_nan=True, high_precision=False):
    sl = np.ones(len(x.shape), dtype=np.int32)
    sl[axis] = -1
    if ignore_nan:
        t = np.ones(x.shape[axis]).reshape(sl)
        t = t + x * 0
        t = np.nancumsum(t, axis)
    else:
        t = np.arange(x.shape[axis]).reshape(sl)
    return MoveBeta(t, x, n, axis, min_n, high_precision)


def MoveBeta(x, y, n, axis=-1, min_n=None, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    x_avg = MoveMean(x, n, axis, min_n, high_precision)
    y_avg = MoveMean(y, n, axis, min_n, high_precision)
    cov = MoveMean(x * y, n, axis, min_n, high_precision) - y_avg * x_avg
    var_x = MoveVar(x, n, axis, 0, min_n, True)
    beta = Div(cov, var_x)
    return beta


def MoveResi(x, y, n, axis=-1, min_n=None, high_precision=False):
    x = x + y * 0
    y = y + x * 0
    x_avg = MoveMean(x, n, axis, min_n, high_precision)
    y_avg = MoveMean(y, n, axis, min_n, high_precision)
    cov = MoveMean(x * y, n, axis, min_n, high_precision) - y_avg * x_avg
    var_x = MoveVar(x, n, axis, 0, min_n, True)
    beta = Div(cov, var_x)
    cons = y_avg - beta * x_avg
    resi = y - (cons + beta * x)
    return resi


# 1d input method
def pearson_corr(x, y, na_check=False, min_num=10):
    if na_check:
        na_cond = np.logical_and(~np.isnan(x), ~np.isnan(y))
        use_idx = np.where(na_cond)[0]
        n = len(use_idx)
        if n < min_num:
            return np.nan
        x = x[use_idx]
        y = y[use_idx]
    else:
        n = len(x)
        if n < min_num:
            return np.nan
    cov = np.mean(x * y) - np.mean(x) * np.mean(y)

    return Div(cov, np.std(x) * np.std(y))


def spearman_corr(x, y, na_check=False, min_num=10):
    if na_check:
        na_cond = np.logical_and(~np.isnan(x), ~np.isnan(y))
        use_idx = np.where(na_cond)[0]
        n = len(use_idx)
        if n < min_num:
            return np.nan
        x = x[use_idx]
        y = y[use_idx]
    else:
        n = len(x)
        if n < min_num:
            return np.nan
    x_rank = x.argsort().argsort()
    y_rank = y.argsort().argsort()
    diff = x_rank - y_rank
    return 1.0 - 6.0 * np.dot(diff, diff) / (n * (n * n - 1))


def CovMatrix(x, min_n=None):
    """
    协方差矩阵
    :param x: ndarray, n*p array, dim=2, n samples and p variable
    :param min_n: int，最少样本量
    :return: ndarray, p*p array, dim=2, corr matrix
    """
    x = x.copy()
    if min_n is None:
        min_n = x.shape[0] // 2
    na_cond = NanCount(x, 0) < min_n
    x[:, na_cond] = np.nan

    x_dmean = FillNanBy(x - NanMean(x, 0), 0.0)
    c = x_dmean.T.dot(x_dmean)
    bn.replace(c, 0.0, np.nan)
    return c


def CorrMatrix(x, min_n=None):
    """
    相关系数矩阵
    :param x: ndarray, n*p array, dim=2, n samples and p variable
    :param min_n: int，最少样本量
    :return: ndarray, p*p array, dim=2, corr matrix
    """
    x = x.copy()
    if min_n is None:
        min_n = x.shape[0] // 2
    na_cond = NanCount(x, 0) < min_n
    x[:, na_cond] = np.nan

    x_dmean = FillNanBy(x - NanMean(x, 0), 0.0)
    c = x_dmean.T.dot(x_dmean)
    std_x = np.sqrt(np.diag(c))
    c = Div(c, std_x[:, None])
    c = Div(c, std_x[None, :])
    return c


def MultiregBeta(x, y):
    """
    多元线性回归求回归系数，x与y不可含有nan值
    :param x: ndarray, n*p array, dim=2
    :param y: ndarray, n*1 array, dim=2
    :return: ndarray, betas
    """
    return np.linalg.solve(x.transpose().dot(x), x.transpose().dot(y))


def NanMultiregBeta(x, y, min_n=None):
    """
    多元线性回归求回归系数，剔除x或y中含有nan值的样本，若剔除后样本量少于min_n，则返回nan值
    :param x: ndarray, n*p array, dim=2
    :param y: ndarray, n*1 array, dim=2
    :param min_n: int，最少样本量
    :return: ndarray, betas
    """
    if min_n is None:
        min_n = x.shape[0] // 2
    u_cond = (NanCount(x, 1) == x.shape[1]) & (~np.isnan(y).flatten())
    if u_cond.sum() < min_n:
        return np.full((x.shape[1], 1), np.nan)
    else:
        return MultiregBeta(x[u_cond], y[u_cond])


def MultiregResi(x, y):
    """
    多元线性回归求残差，x与y不可含有nan值
    :param x: ndarray, n*p array, dim=2
    :param y: ndarray, n*1 array, dim=2
    :return: ndarray, residuals
    """
    b = np.linalg.solve(x.transpose().dot(x), x.transpose().dot(y))
    return y - x.dot(b)


def NanMultiregResi(x, y, min_n=None):
    """
    多元线性回归求回归残差，剔除x或y中含有nan值的样本，若剔除后样本量少于min_n，则返回nan值
    :param x: ndarray, n*p array, dim=2
    :param y: ndarray, n*1 array, dim=2
    :param min_n: int，最少样本量
    :return: ndarray, residuals
    """
    if min_n is None:
        min_n = x.shape[0] // 2
    u_cond = (NanCount(x, 1) == x.shape[1]) & (~np.isnan(y).flatten())
    res = np.full(y.shape, np.nan)
    if u_cond.sum() > min_n:
        res[u_cond] = MultiregResi(x[u_cond], y[u_cond])
    return res
