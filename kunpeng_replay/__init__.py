"""Kunpeng Replay - 高效的金融数据事件驱动回放引擎"""

from .config.settings import ReplayConfig, ReplayMode
from .core.replay_engine import MarketDataReplayEngine
from .data_source.base_reader import MarketDataBaseReader
from .data_types.standard_types import FeatureData, ProcessingResult, ProcessingStats
from .events.market_data_event import MarketDataEvent
from .processors.base_tick_feature_calculator import BaseFeatureCalculator
from .processors.simple_processors import FeatureDumper
from .version import __version__

# 导入merak的Tick数据结构
from .merak_adapter import Tick

__all__ = [
    "MarketDataReplayEngine",
    "ReplayConfig",
    "ReplayMode",
    "MarketDataEvent",
    "MarketDataBaseReader",
    "BaseFeatureCalculator",
    "FeatureDumper",
    "Tick",  # 使用merak的Tick替代原来的TickData
    "FeatureData",
    "ProcessingResult",
    "ProcessingStats",
    "__version__",
]
