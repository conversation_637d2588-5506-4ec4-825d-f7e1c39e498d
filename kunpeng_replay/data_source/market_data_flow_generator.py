"""行情数据流生成器

参考kunpeng项目中的MarketDataFlowGenerator实现，用于生成基于真实股票数据的MarketDataEvent流。
"""

import logging
import sys
import time
from collections import deque
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Deque, Dict, List, Optional

from tqdm import tqdm

from ..events.market_data_event import MarketDataEvent
from ..utils.typing import TickData
from .base_reader import MarketDataBaseReader

# 定义开盘时间切片用于过滤
OPEN_TIMECUT_LIST = [
    93000000,
    93003000,
    93006000,
    93009000,
    93012000,
    93015000,
    93018000,
    93021000,
    93024000,
    93027000,
    93030000,
    93033000,
    93036000,
    93039000,
    93042000,
    93045000,
    93048000,
    93051000,
    93054000,
    93057000,
    93100000,
    93106000,
    93112000,
    93118000,
    93124000,
    93130000,
    93136000,
    93142000,
    93148000,
    93154000,
    93200000,
    93215000,
    93230000,
    93245000,
    93300000,
    93315000,
    93330000,
    93345000,
    93400000,
    93415000,
    93430000,
    93445000,
    93500000,
    93600000,
]


class MarketDataFlowGenerator:
    """行情数据流生成器

    负责生成和发送行情数据流，支持数据到达率过滤和实时发送。
    严格遵循kunpeng项目中的MarketDataFlowGenerator实现。
    """

    def __init__(
        self,
        market_data_reader: MarketDataBaseReader,
        arrival_rate: float = 1.0,
        max_tickers: Optional[int] = None,
    ):
        """初始化行情数据流生成器

        Args:
            market_data_reader: 行情数据读取器（必需参数）
            arrival_rate: 数据到达率，默认为1.0（100%）
            max_tickers: 最大股票数量限制，None表示不限制
        """
        if market_data_reader is None:
            raise ValueError("market_data_reader parameter is required and cannot be None")

        self.logger = logging.getLogger(__name__)
        self.market_data_reader = market_data_reader
        self.arrival_rate = arrival_rate

        # 从market_data_reader获取tickers和index_map，与kunpeng项目保持一致
        all_tickers = market_data_reader.get_tick_tickers()

        # 应用股票数量限制
        if max_tickers is not None and len(all_tickers) > max_tickers:
            self.tickers: List[str] = all_tickers[:max_tickers]
            self.logger.info(f"Limited to {max_tickers} tickers from {len(all_tickers)} available")
        else:
            self.tickers: List[str] = all_tickers

        self.index_map = market_data_reader.get_tick_column_mapping()

        self.logger.info(f"Initialized MarketDataFlowGenerator with {len(self.tickers)} tickers")
        # self.logger.info(f"Index map: {self.index_map}")
        self.logger.info(f"Arrival rate: {self.arrival_rate}")

    def generate_flow(self) -> Deque[MarketDataEvent]:
        """生成行情数据流

        并行处理所有股票的数据，并按时间戳排序。
        如果设置了到达率，会对数据进行过滤。

        Returns:
            Deque[MarketDataEvent]: 行情数据流队列
        """
        flow = []
        start_time = time.time()

        # 使用线程池并行处理所有股票数据
        with ThreadPoolExecutor(max_workers=32) as executor:
            futures = []
            for ticker in self.tickers:
                future = executor.submit(self._query_and_process, ticker)
                futures.append(future)

            with tqdm(
                total=len(futures),
                desc="Processing market data",
                file=sys.stdout,
                disable=False,
            ) as pbar:
                for future in as_completed(futures):
                    flow.extend(future.result())
                    pbar.update(1)

        # 按时间戳排序
        flow.sort(key=lambda x: x.updatetime)
        end_time = time.time()
        self.logger.info(f"generate_flow | flow size: {len(flow)} | time cost: {end_time - start_time:.2f}s")
        # 如果到达率是100%，直接返回所有数据
        if self.arrival_rate >= 1.0:
            return deque(flow)

        # 按到达率过滤数据
        filtered_flow = self._filter_by_arrival_rate(flow)
        self.logger.info(f"generate_flow | filtered_flow size: {len(filtered_flow)}")

        return deque(filtered_flow)

    def _query_and_process(self, ticker: str) -> List[MarketDataEvent]:
        """查询并处理单个股票的数据

        Args:
            ticker: 股票代码

        Returns:
            List[MarketDataEvent]: 处理后的数据列表
        """
        market_events = []

        # 使用market_data_reader查询数据，与kunpeng项目保持一致
        tick_values: TickData = self.market_data_reader.query_tick_values(
            ticker=ticker,
            start_time=91500000,
            end_time=93600000,
        )

        if tick_values is None or len(tick_values) == 0:
            return market_events

        tick_values = tick_values.T
        # 变成了 (tick_num, cols)
        # 这里tick数据是全的，但是只使用了一部分组成MarketDataEvent的流
        for idx, row in enumerate(tick_values, start=1):
            market_data_event = MarketDataEvent(
                ticker=ticker,
                updatetime=int(row[self.index_map["UpdateTime"]]),
                localtime=int(row[self.index_map["LocalTime"]]),
                snap_index=idx,
            )
            market_events.append(market_data_event)

        return market_events

    def _filter_by_arrival_rate(self, flow: List[MarketDataEvent]) -> List[MarketDataEvent]:
        """按到达率过滤数据"""
        filtered_flow = []
        curr = 0
        same_updatetime_group = []

        for event in tqdm(flow, desc="Filtering by arrival rate", file=sys.stdout, disable=True):
            if event.updatetime < OPEN_TIMECUT_LIST[0] or curr + 1 == len(OPEN_TIMECUT_LIST):
                filtered_flow.append(event)
                continue

            if event.updatetime >= OPEN_TIMECUT_LIST[curr] and event.updatetime < OPEN_TIMECUT_LIST[curr + 1]:
                same_updatetime_group.append(event)
            else:
                same_updatetime_group.sort(key=lambda x: x.localtime)
                filtered_flow.extend(same_updatetime_group[: int(len(same_updatetime_group) * self.arrival_rate)])
                same_updatetime_group = [event]
                curr += 1

        return filtered_flow

    def get_tickers(self) -> List[str]:
        """获取股票代码列表"""
        return self.tickers.copy()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "tickers_count": len(self.tickers),
            "arrival_rate": self.arrival_rate,
            "market_data_reader_type": type(self.market_data_reader).__name__,
        }
