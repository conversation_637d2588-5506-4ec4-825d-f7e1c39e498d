from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple

import numpy as np

from kunpeng_replay.events.market_data_event import MarketDataEvent
from kunpeng_replay.utils.typing import TickData


class MarketDataBaseReader(ABC):
    """行情数据基类，定义了获取tick数据的接口"""

    def __init__(self, trading_day: str):
        """
        Args:
            trading_day: 交易日，格式为YYYYMMDD
        """
        self.trading_day = trading_day

    @abstractmethod
    def get_order_column_mapping(self) -> Dict[str, int]:
        """获取order数据的列名到索引的映射

        Returns:
            order数据的列名到索引的映射字典
        """
        pass

    @abstractmethod
    def get_tick_column_mapping(self) -> Dict[str, int]:
        """获取tick数据的列名到索引的映射

        Returns:
            tick数据的列名到索引的映射字典
        """
        pass

    @abstractmethod
    def get_tick_tickers(self) -> List[str]:
        """获取tick数据的证券代码列表

        Returns:
            tick数据的证券代码列表
        """
        pass

    @abstractmethod
    def get_trade_column_mapping(self) -> Dict[str, int]:
        """获取trade数据的列名到索引的映射

        Returns:
            trade数据的列名到索引的映射字典
        """
        pass

    @abstractmethod
    def query_ticker_updatetime(self, ticker: str) -> float:
        """查询指定股票的最新更新时间

        Args:
            ticker: 证券代码

        Returns:
            更新时间戳
        """
        pass

    @abstractmethod
    def query_ticker_sendtime(self, ticker: str) -> Tuple[float, float]:
        """查询指定股票的发送时间

        Args:
            ticker: 证券代码

        Returns:
            (发送时间秒数, 发送时间纳秒数)
        """
        pass

    @abstractmethod
    def query_ticker_streamid(self, ticker: str) -> Tuple[float, float]:
        """查询指定股票的流ID

        Args:
            ticker: 证券代码

        Returns:
            (流ID第一部分, 流ID第二部分)
        """
        pass

    @abstractmethod
    def query_tick_values(self, ticker: str, start_time: int, end_time: int) -> Optional[TickData]:
        """查询指定股票在指定时间范围内的tick数据

        Args:
            ticker: 证券代码
            start_time: 开始时间戳
            end_time: 结束时间戳

        Returns:
            tick数据数组，如果未找到则返回None
        """
        pass

    @abstractmethod
    def query_trade_values(self, ticker: str, start_time: int, end_time: int) -> Optional[np.ndarray]:
        """查询指定股票在指定时间范围内的交易数据

        Args:
            ticker: 证券代码
            start_time: 开始时间戳
            end_time: 结束时间戳

        Returns:
            交易数据数组，如果未找到则返回None
        """
        pass

    def read_events(self) -> List[MarketDataEvent]:
        """读取事件列表

        Returns:
            MarketDataEvent列表
        """
        # 默认实现，子类可以重写
        return []
