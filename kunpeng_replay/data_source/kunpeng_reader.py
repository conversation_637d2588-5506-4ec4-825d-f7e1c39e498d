from typing import Dict, List, Optional, Tuple

import numpy as np
from kunpengdb import pyshmdb as shmdb

from kunpeng_replay.utils.logger import wylogger
from kunpeng_replay.utils.typing import TickData

from .base_reader import MarketDataBaseReader


class MarketDataKunpengDBReader(MarketDataBaseReader):
    """基于共享内存的行情数据实现"""

    def __init__(self, tradingday: str, market_tick_path: str, market_trade_path: str):
        """
        Args:
            tradingday: 交易日，格式为YYYYMMDD
            market_tick_path: 行情tick数据文件的路径，如果为None则使用默认路径
            market_trade_path: 行情trade数据文件的路径，如果为None则使用默认路径
        """
        super().__init__(tradingday)

        # 使用默认路径或指定路径
        self.market_tick_path = market_tick_path
        self.market_trade_path = market_trade_path

        # 初始化列映射
        self._order_column_mapping = {
            "UpdateTime": 0,
            "OrderPrice": 1,
            "OrderQty": 2,
            "SeqNo": 3,
            "Direction": 4,
            "OrderType": 5,
        }
        self._tick_column_mapping = {}
        self._trade_column_mapping = {}

        # 初始化共享内存数据库
        self.market_tick_db = shmdb.MkShmDb(self.market_tick_path)
        self.tick_tickers, self.tick_columns = self.market_tick_db.describe_fast_3D_table("tick")
        self.tick_tickers_mapping = dict(zip(self.tick_tickers, range(len(self.tick_tickers))))
        self._tick_column_mapping = dict(zip(self.tick_columns, range(len(self.tick_columns))))

        self.market_trade_db = shmdb.MkShmDb(self.market_trade_path)
        self.trade_tickers = self.market_trade_db.show_fast_2D_tables()
        self.trade_columns = self.market_trade_db.describe_fast_2D_table(self.trade_tickers[0])
        self._trade_column_mapping = dict(zip(self.trade_columns, range(len(self.trade_columns))))

        wylogger.info("init market memory data")

    def get_order_column_mapping(self) -> Dict[str, int]:
        """获取order数据的列名到索引的映射

        Returns:
            order数据的列名到索引的映射字典
        """
        return self._order_column_mapping

    def get_tick_column_mapping(self) -> Dict[str, int]:
        """获取tick数据的列名到索引的映射

        Returns:
            tick数据的列名到索引的映射字典
        """
        return self._tick_column_mapping

    def get_tick_tickers(self) -> List[str]:
        """获取tick数据的证券代码列表

        Returns:
            tick数据的证券代码列表
        """
        return self.tick_tickers

    def get_trade_column_mapping(self) -> Dict[str, int]:
        """获取trade数据的列名到索引的映射

        Returns:
            trade数据的列名到索引的映射字典
        """
        return self._trade_column_mapping

    def query_ticker_updatetime(self, ticker: str) -> float:
        updatetime = self.market_tick_db.query_fast_3D_any_lastN_value("tick", ticker, ["UpdateTime"], 1)
        return float(updatetime[0])

    def query_ticker_sendtime(self, ticker: str) -> Tuple[float, float]:
        sendtime_sec, sendtime_nano = self.market_tick_db.query_fast_3D_any_lastN_value(
            "tick", ticker, ["SendTimeSec", "SendTimeNano"], 1
        )
        return float(sendtime_sec), float(sendtime_nano)

    def query_ticker_streamid(self, ticker: str) -> Tuple[float, float]:
        streamid_first, streamid_second = self.market_tick_db.query_fast_3D_any_lastN_value(
            "tick", ticker, ["StreamIdFirstPart", "StreamIdSecondPart"], 1
        )
        return float(streamid_first), float(streamid_second)

    def query_tick_values(self, ticker: str, start_time: int, end_time: int) -> Optional[TickData]:
        try:
            if ticker[0] == "C":
                ticker = ticker.split(".WI")[0]
            ticker_index = self.tick_tickers_mapping[ticker]
            result = self.market_tick_db.query_fast_3Dle_values("tick", start_time, end_time, ticker_index)
            if len(result[0]) < 1:
                return None
            return np.array(result)
        except Exception as e:
            wylogger.error(f"查询 {ticker}的tick 数据失败 [{start_time},{end_time}]: {e}")
            return None

    def query_trade_values(self, ticker: str, start_time: int, end_time: int) -> Optional[np.ndarray]:
        try:
            result = self.market_trade_db.query_fast_2Dle_values(ticker, start_time, end_time)
            return result
        except Exception as e:
            wylogger.error(f"Error querying trade values for {ticker}: {e}")
            return None


def get_market_data_kunpengdb_reader() -> MarketDataKunpengDBReader:
    # TODO: 回拨参数可配置
    tradingday = "20250512"
    mk_tick_path = "/data/back_test/data_server/output/20250512/mkdb"
    mk_trade_path = "/data/back_test/data_server/output/20250512/mkdb"
    wylogger.info(f"mk_tick_path {mk_tick_path}")
    wylogger.info(f"mk_trade_path {mk_trade_path}")

    kunpengdb_reader = MarketDataKunpengDBReader(
        tradingday=tradingday,
        market_tick_path=mk_tick_path,
        market_trade_path=mk_trade_path,
    )

    return kunpengdb_reader


market_data_kunpengdb_reader = get_market_data_kunpengdb_reader()
