"""市场数据字段定义模块

此模块定义了市场数据的类型、字段结构和映射关系，包括：
1. 市场数据类型（股票行情、指数行情、成交、委托等）
2. 各类数据的字段定义（价格、成交量、K线等）
3. 字段映射和转换逻辑
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List

import numpy as np


class MarketDataType(Enum):
    """市场数据类型"""

    STOCK_TICK = "stk_tick"  # 股票行情
    INDEX_TICK = "index_tick"  # 指数行情
    TRADE = "trade"  # 成交
    ORDER = "order"  # 委托


@dataclass
class MarketDataFields:
    """市场数据字段定义"""

    # 基础字段
    update_time: str = "UpdateTime"  # 更新时间
    pre_close: str = "PreClose"  # 前收盘价
    open_price: str = "OpenPrice"  # 开盘价
    high_price: str = "HighPrice"  # 最高价
    low_price: str = "LowPrice"  # 最低价
    last_price: str = "LastPrice"  # 最新价
    acc_volume: str = "AccVolume"  # 累计成交量
    acc_turnover: str = "AccTurnover"  # 累计成交额
    total_bid_vol: str = "TotalBidVol"  # 总买量
    total_ask_vol: str = "TotalAskVol"  # 总卖量
    bid_wavg_price: str = "BidWAvgPrice"  # 买价加权平均
    ask_wavg_price: str = "AskWAvgPrice"  # 卖价加权平均
    up_limit_price: str = "UpLimitPrice"  # 涨停价
    down_limit_price: str = "DownLimitPrice"  # 跌停价

    # 买卖档位字段生成器
    bid_prices: List[str] = field(default_factory=lambda: [f"BidPrice{i}" for i in range(1, 11)])  # 买价档位
    bid_vols: List[str] = field(default_factory=lambda: [f"BidVol{i}" for i in range(1, 11)])  # 买量档位
    ask_prices: List[str] = field(default_factory=lambda: [f"AskPrice{i}" for i in range(1, 11)])  # 卖价档位
    ask_vols: List[str] = field(default_factory=lambda: [f"AskVol{i}" for i in range(1, 11)])  # 卖量档位

    # 成交字段
    trade_price: str = "TradePrice"  # 成交价
    trade_qty: str = "TradeQty"  # 成交量
    buy_no: str = "BuyNo"  # 买方委托序号
    sell_no: str = "SellNo"  # 卖方委托序号
    exec_type: str = "ExecType"  # 执行类型

    # 委托字段
    order_price: str = "OrderPrice"  # 委托价
    order_qty: str = "OrderQty"  # 委托量
    seq_no: str = "SeqNo"  # 委托序号
    direction: str = "Direction"  # 委托方向
    order_type: str = "OrderType"  # 委托类型

    @property
    def stock_tick(self) -> List[str]:
        """股票行情字段列表"""
        return [
            self.update_time,
            *self.bid_prices,
            *self.bid_vols,
            *self.ask_prices,
            *self.ask_vols,
            self.pre_close,
            self.open_price,
            self.high_price,
            self.low_price,
            self.last_price,
            self.acc_volume,
            self.acc_turnover,
            self.total_bid_vol,
            self.total_ask_vol,
            self.bid_wavg_price,
            self.ask_wavg_price,
            self.up_limit_price,
            self.down_limit_price,
        ]

    @property
    def index_tick(self) -> List[str]:
        """指数行情字段列表"""
        return [
            self.update_time,
            self.pre_close,
            self.open_price,
            self.high_price,
            self.low_price,
            self.last_price,
            self.acc_volume,
            self.acc_turnover,
        ]

    @property
    def trade(self) -> List[str]:
        """成交字段列表"""
        return [
            self.update_time,
            self.trade_price,
            self.trade_qty,
            self.buy_no,
            self.sell_no,
            self.exec_type,
        ]

    @property
    def order(self) -> List[str]:
        """委托字段列表"""
        return [
            self.update_time,
            self.order_price,
            self.order_qty,
            self.seq_no,
            self.direction,
            self.order_type,
        ]

    def to_dict(self) -> Dict[str, List[str]]:
        """转换为字典格式

        Returns:
            Dict[str, List[str]]: 字段字典，key为数据类型，value为字段列表
        """
        return {
            MarketDataType.STOCK_TICK.value: self.stock_tick,
            MarketDataType.INDEX_TICK.value: self.index_tick,
            MarketDataType.TRADE.value: self.trade,
            MarketDataType.ORDER.value: self.order,
        }


@dataclass
class PriceFields:
    """价格相关字段定义"""

    last_price: str = "LastPrice"  # 最新价
    pre_close: str = "PreClose"  # 前收盘价
    open_price: str = "OpenPrice"  # 开盘价
    high_price: str = "HighPrice"  # 最高价
    low_price: str = "LowPrice"  # 最低价
    bid_prices: List[str] = field(default_factory=lambda: [f"BidPrice{i}" for i in range(1, 11)])  # 买价档位
    ask_prices: List[str] = field(default_factory=lambda: [f"AskPrice{i}" for i in range(1, 11)])  # 卖价档位
    bid_wavg_price: str = "BidWAvgPrice"  # 买价加权平均
    ask_wavg_price: str = "AskWAvgPrice"  # 卖价加权平均

    def to_list(self) -> List[str]:
        """转换为列表格式"""
        return [
            self.last_price,
            self.pre_close,
            self.open_price,
            self.high_price,
            self.low_price,
            *self.bid_prices,
            *self.ask_prices,
            self.bid_wavg_price,
            self.ask_wavg_price,
        ]


@dataclass
class TickAccumulatedFields:
    """Tick数据累计量相关字段定义"""

    acc_volume: str = "AccVolume"  # 累计成交量
    acc_turnover: str = "AccTurnover"  # 累计成交额
    buy_vol: str = "BuyVol"  # 买入成交量
    buy_count: str = "BuyCount"  # 买入成交笔数
    buy_turnover: str = "BuyTurnover"  # 买入成交额
    sell_vol: str = "SellVol"  # 卖出成交量
    sell_count: str = "SellCount"  # 卖出成交笔数
    sell_turnover: str = "SellTurnover"  # 卖出成交额
    num_trades_combine: str = "NumTradesCombine"  # 合并成交笔数
    num_trades_total: str = "NumTradesTotal"  # 总成交笔数

    def to_list(self) -> List[str]:
        """转换为列表格式"""
        return [
            self.acc_volume,
            self.acc_turnover,
            self.buy_vol,
            self.buy_count,
            self.buy_turnover,
            self.sell_vol,
            self.sell_count,
            self.sell_turnover,
            self.num_trades_combine,
            self.num_trades_total,
        ]


@dataclass
class KBarFields:
    """K线字段定义"""

    pre_close: str = "pre_close"  # 前收盘价
    close_price: str = "close"  # 收盘价
    open_price: str = "open"  # 开盘价
    high_price: str = "high"  # 最高价
    low_price: str = "low"  # 最低价
    volume: str = "volume"  # 成交量
    turnover: str = "amount"  # 成交额
    float_share: str = "float_shr"  # 流通股本
    adjust_factor: str = "adjust_factor"  # 复权因子

    def to_list(self) -> List[str]:
        """转换为列表格式"""
        return [
            self.pre_close,
            self.close_price,
            self.open_price,
            self.high_price,
            self.low_price,
            self.volume,
            self.turnover,
            self.float_share,
            self.adjust_factor,
        ]


@dataclass
class FieldIndexMapping:
    """字段索引映射定义"""

    field_index_map: Dict[str, Dict[str, int]] = field(
        default_factory=lambda: {
            key: dict(zip(fields, range(len(fields)))) for key, fields in MARKET_DATA_FIELDS.to_dict().items()
        }
    )


@dataclass
class AccumulatedFieldMapping:
    """累计字段条件映射定义"""

    accumulated_conditions: Dict[str, np.ndarray] = field(
        default_factory=lambda: {
            MarketDataType.STOCK_TICK.value: np.isin(MARKET_DATA_FIELDS.stock_tick, TickAccumulatedFields().to_list()),
            MarketDataType.INDEX_TICK.value: np.isin(MARKET_DATA_FIELDS.index_tick, TickAccumulatedFields().to_list()),
        }
    )


@dataclass
class FieldRenameMapping:
    """字段重命名映射定义"""

    tick_mapping: Dict[str, str] = field(
        default_factory=lambda: {
            "UpdateTime": "tick_ut",  # 更新时间
            "LocalTime": "tick_lt",  # 本地时间
            **{f"BidPrice{i}": f"bp{i}" for i in range(1, 11)},  # 买价档位
            **{f"BidVol{i}": f"bv{i}" for i in range(1, 11)},  # 买量档位
            **{f"AskPrice{i}": f"ap{i}" for i in range(1, 11)},  # 卖价档位
            **{f"AskVol{i}": f"av{i}" for i in range(1, 11)},  # 卖量档位
            "PreClose": "tick_pre_close",  # 前收盘价
            "OpenPrice": "tick_op",  # 开盘价
            "HighPrice": "tick_hp",  # 最高价
            "LowPrice": "tick_lp",  # 最低价
            "LastPrice": "tick_cp",  # 最新价
            "AccVolume": "tick_vol",  # 累计成交量
            "AccTurnover": "tick_tvr",  # 累计成交额
            "UpLimitPrice": "up_limit_p",  # 涨停价
            "DownLimitPrice": "down_limit_p",  # 跌停价
            "TotalBidVol": "bv_tot",  # 总买量
            "TotalAskVol": "av_tot",  # 总卖量
            "BidWAvgPrice": "bvwp_tot",  # 买价加权平均
            "AskWAvgPrice": "avwp_tot",  # 卖价加权平均
        }
    )

    trade_mapping: Dict[str, str] = field(
        default_factory=lambda: {
            "UpdateTime": "trade_ut",  # 更新时间
            "LocalTime": "trade_lt",  # 本地时间
            "TradePrice": "trade_p",  # 成交价
            "TradeQty": "trade_vol",  # 成交量
            "BuyNo": "buy_id",  # 买方委托序号
            "SellNo": "sell_id",  # 卖方委托序号
        }
    )

    order_mapping: Dict[str, str] = field(
        default_factory=lambda: {
            "UpdateTime": "order_ut",  # 更新时间
            "LocalTime": "order_lt",  # 本地时间
            "OrderPrice": "order_p",  # 委托价
            "OrderQty": "order_vol",  # 委托量
            "SeqNo": "order_id",  # 委托序号
            "Direction": "order_side",  # 委托方向
            "OrderType": "order_type",  # 委托类型
        }
    )

    @property
    def rename_mappings(self) -> Dict[str, Dict[str, str]]:
        """获取所有重命名映射"""
        return {
            "tick": self.tick_mapping,
            "trade": self.trade_mapping,
            "order": self.order_mapping,
        }


# 创建内部使用的dataclass实例
MARKET_DATA_FIELDS = MarketDataFields()
_PRICE_FIELDS_OBJ = PriceFields()
_TICK_ACCUMULATED_FIELDS_OBJ = TickAccumulatedFields()
_KBAR_FIELDS_OBJ = KBarFields()
_FIELD_INDEX_MAPPING_OBJ = FieldIndexMapping()
_ACCUMULATED_FIELD_MAPPING_OBJ = AccumulatedFieldMapping()
_FIELD_RENAME_MAPPING_OBJ = FieldRenameMapping()

# 直接导出list形式的变量
PRICE_FIELDS = _PRICE_FIELDS_OBJ.to_list()
TICK_ACCUMULATED_FIELDS = _TICK_ACCUMULATED_FIELDS_OBJ.to_list()
KBAR_FIELDS = _KBAR_FIELDS_OBJ.to_list()

# 导出映射对象
FIELD_INDEX_MAPPING = _FIELD_INDEX_MAPPING_OBJ.field_index_map
ACCUMULATED_FIELD_MAPPING = _ACCUMULATED_FIELD_MAPPING_OBJ.accumulated_conditions
FIELD_RENAME_MAPPING = _FIELD_RENAME_MAPPING_OBJ.rename_mappings
