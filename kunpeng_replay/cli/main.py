"""命令行工具 - 更新为固定插槽设计"""

import argparse
import asyncio
from typing import Dict, Optional

from kunpeng_replay.utils.logger import wylogger
from ..merak_adapter import Tick

from ..config.settings import ReplayConfig, ReplayMode
from ..core.replay_engine import MarketDataReplayEngine
from ..data_source.market_data_flow_generator import MarketDataFlowGenerator
from ..data_types.standard_types import FeatureData, ProcessingResult
from ..processors.base_tick_feature_calculator import BaseFeatureCalculator
from ..processors.simple_processors import FeatureDumper
from ..processors.tick_batch_processor import TickBatchProcessor
from ..version import __version__

# 全局调用序号计数器
_call_counter = 0


def get_next_call_id() -> int:
    """获取下一个调用序号"""
    global _call_counter
    _call_counter += 1
    return _call_counter


class EventFlowReader:
    """简单的事件流读取器，直接返回预生成的事件流"""

    def __init__(self, events_flow, logger):
        call_id = get_next_call_id()
        self.logger = logger
        self.logger.info(f"[{call_id}] EventFlowReader.__init__: 创建事件流读取器")
        self.events_flow = events_flow

    def read_events(self):
        """返回预生成的事件流"""
        call_id = get_next_call_id()
        self.logger.info(f"[{call_id}] EventFlowReader.read_events: 返回{len(self.events_flow)}个预生成事件")
        return self.events_flow


class CLIFeatureCalculator(BaseFeatureCalculator):
    """CLI使用的特征计算器，基本特征计算"""

    def __init__(self):
        super().__init__("CLIFeatureCalculator")

    def calculate_features(self, tick: Tick) -> Optional[Dict[str, float]]:
        """计算基本特征"""
        try:
            call_id = get_next_call_id()
            wylogger.info(f"[{call_id}] calculate feature ")

            # 使用merak Tick的字段计算基本特征
            features = {
                "f1": 0.1,
                "f2": 0.2,
                "spread": tick.ap1 - tick.bp1,  # 买卖价差
                "mid_price": (tick.bp1 + tick.ap1) / 2,  # 中间价
                "volume": tick.tick_vol,  # 成交量
                "turnover": tick.tick_tvr,  # 成交额
            }
            return features

        except Exception as e:
            wylogger.error(f"Error calculating features: {e}")
            return None


class CLIFeatureDumper(FeatureDumper):
    """CLI使用的特征存储器，输出到日志"""

    def __init__(self):
        super().__init__("CLIFeatureDumper")
        self.processed_count = 0

    def dump_features(self, features: FeatureData) -> ProcessingResult:
        """将特征输出到日志"""
        try:
            call_id = get_next_call_id()
            wylogger.info(f"[{call_id}] dump feature ")
            self.processed_count += 1

            # 每100个事件输出一次日志
            if self.processed_count % 100 == 0:
                self.logger.info(f"Processed {self.processed_count} features for {features.ticker}")

            return ProcessingResult.success_result(
                ticker=features.ticker, timestamp=features.timestamp, records_saved=1
            )
        except Exception as e:
            return ProcessingResult.failure_result(
                ticker=features.ticker, timestamp=features.timestamp, error_message=str(e)
            )


async def run_market_data_processing(
    tickers_count: int = 10,
    arrival_rate: float = 1.0,
):
    """运行行情数据处理

    Args:
        tickers_count: 使用的股票数量
        arrival_rate: 数据到达率
    """
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 开始行情数据处理")

    # 1. 初始化数据读取器
    try:
        call_id = get_next_call_id()
        wylogger.info(f"[{call_id}] run_market_data_processing: 初始化kunpeng数据读取器")

        from ..data_source.kunpeng_reader import market_data_kunpengdb_reader

        # 获取股票代码列表
        call_id = get_next_call_id()
        all_tickers = market_data_kunpengdb_reader.get_tick_tickers()
        selected_tickers = all_tickers[:tickers_count]
        wylogger.info(f"[{call_id}] run_market_data_processing: 选择{len(selected_tickers)}个股票代码")

    except Exception as e:
        wylogger.error(f"Error initializing market data reader: {e}")
        return

    # 2. 生成行情数据流
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 创建数据流生成器")

    generator = MarketDataFlowGenerator(
        market_data_reader=market_data_kunpengdb_reader, arrival_rate=arrival_rate, max_tickers=tickers_count
    )

    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 生成行情数据流")
    market_data_flow = generator.generate_flow()
    wylogger.info(f"Generated {len(market_data_flow)} market data events")

    # 3. 创建事件流读取器
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 创建事件流读取器")
    event_reader = EventFlowReader(events_flow=market_data_flow, logger=wylogger)

    # 4. 创建回放引擎配置
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 创建回放引擎配置")
    config = ReplayConfig(mode=ReplayMode.EVENT_DRIVEN, progress_bar=True, max_events_per_batch=100)

    # 5. 创建回放引擎
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 创建回放引擎")
    engine = MarketDataReplayEngine(config=config, logger=wylogger)
    engine.set_data_reader(event_reader)

    # 6. 设置三个处理器
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 设置三阶段处理器")

    # 设置tick处理器
    tick_processor = TickBatchProcessor(logger=wylogger)
    tick_processor.set_market_data_reader(market_data_kunpengdb_reader)
    engine.set_tick_processor(tick_processor)

    # 设置特征计算器
    feature_calculator = CLIFeatureCalculator()
    engine.set_feature_calculator(feature_calculator)

    # 设置特征存储器
    feature_dumper = CLIFeatureDumper()
    engine.set_feature_dumper(feature_dumper)

    # 7. 运行回放
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] run_market_data_processing: 开始运行回放引擎")
    stats = await engine.run()

    # 8. 输出结果
    print("\n=== Market Data Processing Results ===")
    print(f"Total events: {stats.total_events}")
    print(f"Processed events: {stats.processed_events}")
    print(f"Failed events: {stats.failed_events}")
    print(f"Processing rate: {stats.processing_rate:.2f} events/s")
    print(f"Success rate: {stats.get_success_rate():.2%}")
    print(f"Average processing time: {stats.avg_processing_time_ms:.2f} ms/event")

    # 9. 显示各阶段统计
    print("\nStage Failures:")
    print(f"  Tick Processing: {stats.tick_processing_failures}")
    print(f"  Feature Calculation: {stats.feature_calculation_failures}")
    print(f"  Feature Dumping: {stats.feature_dumping_failures}")

    # 10. 显示详细统计
    detailed_stats = engine.get_detailed_stats()
    if "tick_processor_stats" in detailed_stats:
        tick_stats = detailed_stats["tick_processor_stats"]
        print("\nTick Processor:")
        print(f"  Processed: {tick_stats.get('processed_count', 0)}")
        print(f"  Success Rate: {tick_stats.get('success_rate', 0):.2%}")

    if "feature_calculator_stats" in detailed_stats:
        feature_stats = detailed_stats["feature_calculator_stats"]
        print("\nFeature Calculator:")
        print(f"  Processed: {feature_stats.get('processed_count', 0)}")
        print(f"  Total Features: {feature_stats.get('total_features_calculated', 0)}")

    if "feature_dumper_stats" in detailed_stats:
        dumper_stats = detailed_stats["feature_dumper_stats"]
        print("\nFeature Dumper:")
        print(f"  Processed: {dumper_stats.get('processed_count', 0)}")
        print(f"  Records Saved: {dumper_stats.get('total_records_saved', 0)}")

    return stats


def main():
    """主函数"""
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] main: 启动命令行工具")

    parser = argparse.ArgumentParser(
        description="Kunpeng Replay Tool - Market Data Event Processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # 基本使用
  python -m kunpeng_replay.cli.main --tickers 10

  # 调整数据到达率
  python -m kunpeng_replay.cli.main --tickers 5 --arrival-rate 0.5
        """,
    )
    parser.add_argument("--version", action="version", version=f"kunpeng-replay {__version__}")
    parser.add_argument(
        "--tickers",
        "-t",
        type=int,
        default=10,
        help="Number of tickers to use",
    )
    parser.add_argument(
        "--arrival-rate",
        type=float,
        default=1.0,
        help="Data arrival rate (0.0-1.0)",
    )

    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] main: 解析命令行参数")
    args = parser.parse_args()

    # 运行市场数据处理
    call_id = get_next_call_id()
    wylogger.info(f"[{call_id}] main: 启动异步事件循环")
    asyncio.run(
        run_market_data_processing(
            tickers_count=args.tickers,
            arrival_rate=args.arrival_rate,
        )
    )


if __name__ == "__main__":
    main()
