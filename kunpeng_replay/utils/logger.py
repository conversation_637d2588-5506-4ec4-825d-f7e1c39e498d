import sys
import traceback
from datetime import datetime

from loguru import logger


def get_adjusted_time() -> datetime:
    """获取调整后的时间，如果TimeController可用且启用时间同步则使用调整时间，否则使用系统时间"""
    try:
        # 动态导入避免循环导入
        from ..core.time_controller import TimeController

        # 获取TimeController单例实例
        time_controller = TimeController.get_instance()

        if (
            time_controller is not None
            and hasattr(time_controller, "enable_system_time_sync")
            and time_controller.enable_system_time_sync
            and hasattr(time_controller, "get_system_time_s")
        ):
            try:
                # 使用TimeController调整后的时间
                adjusted_time_s = time_controller.get_system_time_s()
                return datetime.fromtimestamp(adjusted_time_s)
            except Exception:
                # 如果获取调整时间失败，回退到系统时间
                pass
    except ImportError:
        # 如果无法导入TimeController，使用系统时间
        pass

    # 使用系统真实时间
    return datetime.now()


# 创建一个自定义的Handler类，处理stdout和stderr的输出
class StreamToLogger:
    def __init__(self, level):
        self.level = level

    # def write(self, message):
    #     if message.strip():  # 避免记录空消息
    #         self.level(message)

    def write(self, buf):
        for line in buf.rstrip().splitlines():
            self.level(line.rstrip())

    def flush(self):
        pass  # 可以留空


def add_custom_time(record):
    """添加自定义时间到record的extra字段"""
    try:
        # 获取调整后的时间
        adjusted_time = get_adjusted_time()
        custom_time = adjusted_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 保留3位毫秒
        record["extra"]["custom_time"] = custom_time
        return True
    except Exception:
        # 如果获取调整时间失败，使用系统时间
        system_time = datetime.now()
        custom_time = system_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        record["extra"]["custom_time"] = custom_time
        return True


def _get_logger_format_str() -> str:
    format_str = "<green>{extra[custom_time]}</green>"
    format_str += " <red>{process}</red>"
    format_str += " <level>{level: <6}</level>"
    format_str += " <yellow>{file}:{function}:{line}</yellow>"
    format_str += "$<level>{message}</level>"
    return format_str


def setup_module_logger(module_name: str, log_level: str = "INFO"):
    format_str = _get_logger_format_str()
    logger.remove()
    logger.add(
        sink=sys.stdout,
        enqueue=True,  # 支持异步存储
        backtrace=True,  # 回溯
        diagnose=True,  # 诊断
        # rotation="500MB",  # 最大保存大小
        # retention="10 days",  # 保存期限10天
        format=format_str,
        level=log_level,
        filter=add_custom_time,  # 使用自定义时间过滤器
    )
    logger.add(
        sink=f"./{module_name}.log",
        enqueue=True,  # 支持异步存储
        backtrace=True,  # 回溯
        diagnose=True,  # 诊断
        rotation="500MB",  # 最大保存大小
        retention="10 days",  # 保存期限10天
        format=format_str,
        level=log_level,
        filter=add_custom_time,  # 使用自定义时间过滤器
    )
    # if module_name == "wy_qlib_server_online":
    #     sys.stdout = StreamToLogger(wylogger.info)
    #     sys.stderr = StreamToLogger(wylogger.error)
    print(f"logger module_name={module_name}")

    return logger.bind(module=module_name)


# 主日志，与现状保持一致
wylogger = setup_module_logger(module_name="kunpeng_replay")


def loguru_excepthook(*exc_info):
    exc_type, exc_value, exc_traceback = exc_info
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    text = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    wylogger.error(f"Uncaught exception: {text}")


sys.excepthook = loguru_excepthook
