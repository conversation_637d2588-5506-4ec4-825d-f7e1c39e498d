from typing import List

import numpy as np
import pandas as pd

timecuts_093000to093500: List[str] = [
    "093000",
    "093003",
    "093006",
    "093009",
    "093012",
    "093015",
    "093018",
    "093021",
    "093024",
    "093027",
    "093030",
    "093033",
    "093036",
    "093039",
    "093042",
    "093045",
    "093048",
    "093051",
    "093054",
    "093057",
    "093100",
    "093106",
    "093112",
    "093118",
    "093124",
    "093130",
    "093136",
    "093142",
    "093148",
    "093154",
    "093200",
    "093215",
    "093230",
    "093245",
    "093300",
    "093315",
    "093330",
    "093345",
    "093400",
    "093415",
    "093430",
    "093445",
    "093500",
]


timecuts_093600to102900: List[str] = list(
    pd.date_range(start="09:36:00", end="10:29:00", freq="60s").strftime("%H%M%S").values
)
timecuts_103000to112900: List[str] = list(
    pd.date_range(start="10:30:00", end="11:29:00", freq="60s").strftime("%H%M%S").values
)
timecuts_103000to112500_300s: List[str] = list(
    pd.date_range(start="10:30:00", end="11:25:00", freq="300s").strftime("%H%M%S").values
)
timecuts_130500to144500_300s: List[str] = list(
    pd.date_range(start="13:05:00", end="14:45:00", freq="300s").strftime("%H%M%S").values
)


timecuts_130500to143000: List[str] = list(
    pd.date_range(start="13:05:00", end="14:30:00", freq="60s").strftime("%H%M%S").values
)

# timecuts_143500to144500_300s: List[str] = list(
#     pd.date_range(start="14:35:00", end="14:45:00", freq="300s")
#     .strftime("%H%M%S")
#     .values
# )

timecuts_143030to145630: List[str] = list(
    pd.date_range(start="14:30:30", end="14:56:30", freq="30s").strftime("%H%M%S").values
)


ontimer_timecuts: List[str] = (
    timecuts_093600to102900 + timecuts_103000to112900 + timecuts_130500to143000 + timecuts_143030to145630
)

timecuts_all: List[str] = timecuts_093000to093500 + ontimer_timecuts


zp_timecuts = [timecut for timecut in timecuts_all if timecut <= "093500"]
# 去掉093000
low_arrival_rate_timecuts = zp_timecuts[1:]
# low_arrival_rate_timecuts = zp_timecuts
pz_timecuts = [x for x in timecuts_all if x > "093500"]
idx_timecuts = dict(zip(timecuts_all, range(len(timecuts_all))))


time_after_auc: str = "092600"
timecuts_addauc: List[str] = [time_after_auc] + timecuts_all

# !直接切到093042会导致093042不出分，这里暂时多加一个截面
tick_timecuts = [timecut for timecut in zp_timecuts if timecut <= "093045"]


def get_timecut_t_type(timecut: str):
    if "093000" <= timecut <= "093500":
        return "zp"
    elif "093600" <= timecut <= "103000":
        return "pz1"
    elif "103100" <= timecut <= "112900":
        return "pz2"
    elif "130500" <= timecut <= "145630":
        return "pz3"


def get_timecut(updatetime):
    """
    获取时间戳对应的截面
    Args:
        updatetime: 时间戳,整型或者字符串类型,例如 93003000或者103003000
    Returns:
        截面字符串,例如'093003'或者'103003'
    """
    updatetime_norm = str(updatetime).zfill(9)[:6]
    timecut_idx = np.searchsorted(timecuts_all, updatetime_norm)
    if timecut_idx >= len(timecuts_all):
        timecut_idx = len(timecuts_all) - 1
    return timecuts_all[timecut_idx]


def get_timecut_range(timecut: str) -> str:
    """将具体的时间点映射到对应的时间区间

    将具体的时间点（如"093000"）映射到其所属的时间区间（如"093000to093042"）。
    特殊处理开盘前（092500）和收盘后（150000）的时间点。

    Args:
        timecut: 时间点，格式为"HHMMSS"，如"093000"

    Returns:
        str: 时间区间，格式为"HHMMSStoHHMMSS"或具体时间点

    Raises:
        ValueError: 当输入的时间点不在任何预定义的时间区间内时抛出
    """
    # 特殊处理开盘前和收盘后的时间点
    if timecut in ["092500", "150000"]:
        return timecut

    # 将时间点转换为整数进行比较
    time_int = int(timecut)

    # 定义时间区间映射
    if time_int <= 93042:
        return "093000to093042"
    elif time_int <= 93154:
        return "093045to093154"
    elif time_int <= 93500:
        return "093200to093500"
    elif time_int <= 100000:
        return "093600to100000"
    elif time_int <= 103000:
        return "100100to103000"
    elif time_int <= 112500:
        return "103500to112500"
    elif time_int <= 144500:
        return "130000to143000"
    else:
        raise ValueError(
            f"Invalid timecut: {timecut}. "
            "Timecut must be within one of the predefined time ranges: "
            "092500, 093000-093042, 093045-093154, 093200-093500, "
            "093600-100000, 100100-103000, 103500-112500, "
            "130000-143000, or 150000"
        )
