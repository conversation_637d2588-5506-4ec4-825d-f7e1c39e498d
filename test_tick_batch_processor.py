#!/usr/bin/env python3
"""
测试TickBatchProcessor的功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, '/code/kunpeng_replay')

def test_tick_batch_processor():
    """测试TickBatchProcessor的基本功能"""
    print("测试TickBatchProcessor...")
    
    try:
        from kunpeng_replay.processors.tick_batch_processor import TickBatchProcessor
        from kunpeng_replay.events.market_data_event import MarketDataEvent
        from kunpeng_replay.merak_adapter import Tick
        
        # 创建一个简单的logger
        class SimpleLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
        
        logger = SimpleLogger()
        
        # 创建TickBatchProcessor实例
        processor = TickBatchProcessor(logger)
        print(f"✓ 成功创建TickBatchProcessor: {processor.name}")
        
        # 测试统计信息
        stats = processor.get_stats()
        print(f"✓ 获取统计信息: {stats}")
        
        # 创建一个测试事件
        event = MarketDataEvent(
            ticker="000001.SZ",
            updatetime=93000000,  # 09:30:00
            localtime=1640995800000000,  # 微秒时间戳
            data={}
        )
        
        # 测试process_tick方法（BaseTickProcessor的抽象方法实现）
        tick = processor.process_tick(event)
        if tick is not None:
            print(f"✓ process_tick成功: {tick.tick_ut}")
            print(f"  买一价: {tick.bp1}, 卖一价: {tick.ap1}")
        else:
            print("✗ process_tick返回None")
        
        print("✓ TickBatchProcessor基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inheritance():
    """测试继承关系"""
    print("\n测试继承关系...")
    
    try:
        from kunpeng_replay.processors.tick_batch_processor import TickBatchProcessor
        from kunpeng_replay.processors.base_tick_processor import BaseTickProcessor
        
        class SimpleLogger:
            def info(self, msg): pass
            def error(self, msg): pass
        
        processor = TickBatchProcessor(SimpleLogger())
        
        # 检查是否正确继承
        if isinstance(processor, BaseTickProcessor):
            print("✓ TickBatchProcessor正确继承了BaseTickProcessor")
        else:
            print("✗ 继承关系不正确")
            return False
        
        # 检查是否有必要的方法
        required_methods = ['process_tick', 'get_stats', 'reset_stats']
        for method in required_methods:
            if hasattr(processor, method):
                print(f"✓ 有方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        # 检查是否有批处理方法
        if hasattr(processor, 'process_tick_batch'):
            print("✓ 保留了原有的process_tick_batch方法")
        else:
            print("✗ 缺少process_tick_batch方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 继承测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=== TickBatchProcessor测试 ===")
    
    tests = [
        test_tick_batch_processor,
        test_inheritance,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！TickBatchProcessor功能正常")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
