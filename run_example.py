#!/usr/bin/env python3
"""
Kunpeng Replay 运行示例

这个脚本展示了如何使用清理后的架构来运行基于行情数据的时间模拟系统。

运行方式:
1. 直接运行此脚本: python run_example.py
2. 使用命令行工具: python -m kunpeng_replay.cli.main --tickers 5
3. 如果已安装: kunpeng-replay --tickers 5
"""

import asyncio
import logging
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('kunpeng_replay.log')
        ]
    )

async def run_with_test_data():
    """使用测试数据运行示例"""
    print("=" * 80)
    print("Kunpeng Replay - 基于行情数据的时间模拟系统")
    print("=" * 80)
    
    try:
        # 运行我们之前创建的测试示例
        from kunpeng_replay.test_run import main as test_main
        await test_main()
        
    except Exception as e:
        print(f"运行测试示例时出错: {e}")
        import traceback
        traceback.print_exc()

def run_cli_tool():
    """运行命令行工具"""
    print("\n" + "=" * 80)
    print("尝试运行命令行工具")
    print("=" * 80)
    
    try:
        from kunpeng_replay.cli.main import main as cli_main
        
        # 模拟命令行参数
        import sys
        original_argv = sys.argv.copy()
        sys.argv = ['kunpeng-replay', '--tickers', '3', '--arrival-rate', '1.0']
        
        print("运行命令: kunpeng-replay --tickers 3 --arrival-rate 1.0")
        print("注意: 如果出现kunpeng数据源相关错误，这是正常的")
        print("-" * 40)
        
        try:
            cli_main()
        except SystemExit:
            # argparse会调用sys.exit，这是正常的
            pass
        finally:
            # 恢复原始命令行参数
            sys.argv = original_argv
            
    except Exception as e:
        print(f"运行CLI工具时出错: {e}")
        print("这可能是因为缺少kunpeng数据源依赖，这是正常的")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 80)
    print("使用方法和示例")
    print("=" * 80)
    
    print("""
1. 直接使用Python模块运行:
   python -m kunpeng_replay.cli.main --tickers 10 --arrival-rate 1.0

2. 如果已安装包，可以直接使用命令:
   kunpeng-replay --tickers 10 --arrival-rate 1.0

3. 在代码中使用:
   from kunpeng_replay.core.replay_engine import MarketDataReplayEngine
   from kunpeng_replay.config.settings import ReplayConfig, ReplayMode
   
   config = ReplayConfig(mode=ReplayMode.EVENT_DRIVEN, progress_bar=True)
   engine = MarketDataReplayEngine(config)
   engine.set_data_reader(your_data_reader)
   engine.set_processor(your_processor)
   engine.enable_time_sync(True)
   stats = await engine.run()

4. 参数说明:
   --tickers, -t     : 使用的股票数量 (默认: 10)
   --arrival-rate    : 数据到达率 0.0-1.0 (默认: 1.0)
   --version         : 显示版本信息
   --help, -h        : 显示帮助信息

5. 核心特性:
   ✅ 基于行情数据的时间模拟
   ✅ 按localtime字段推进时间
   ✅ 系统时间同步
   ✅ 事件驱动处理
   ✅ 进度监控和统计
   ✅ 可扩展的处理器架构
""")

def check_installation():
    """检查安装状态"""
    print("=" * 80)
    print("检查安装状态")
    print("=" * 80)
    
    try:
        import kunpeng_replay
        print(f"✅ kunpeng_replay 已导入，版本: {kunpeng_replay.version.__version__}")
        
        from kunpeng_replay.core.replay_engine import MarketDataReplayEngine
        print("✅ MarketDataReplayEngine 可用")
        
        from kunpeng_replay.core.time_controller import TimeController
        print("✅ TimeController 可用")
        
        from kunpeng_replay.config.settings import ReplayConfig
        print("✅ ReplayConfig 可用")
        
        print("\n✅ 所有核心组件都已正确安装和配置！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保项目已正确安装")
        return False

async def main():
    """主函数"""
    setup_logging()
    
    print("Kunpeng Replay 运行示例")
    print("作者: AI Assistant")
    print("功能: 展示基于行情数据的时间模拟系统")
    
    # 检查安装
    if not check_installation():
        return
    
    # 运行测试数据示例
    await run_with_test_data()
    
    # 尝试运行CLI工具
    run_cli_tool()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 80)
    print("示例运行完成！")
    print("=" * 80)
    print("\n现在你可以:")
    print("1. 运行 python kunpeng_replay/test_run.py 查看详细的功能演示")
    print("2. 运行 python kunpeng_replay/test_cli.py 测试CLI工具")
    print("3. 使用 python -m kunpeng_replay.cli.main --help 查看CLI帮助")
    print("4. 根据你的需求修改和扩展代码")

if __name__ == "__main__":
    asyncio.run(main())
