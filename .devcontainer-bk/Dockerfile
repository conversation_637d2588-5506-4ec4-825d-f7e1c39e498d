# 使用NVIDIA Ubuntu 22.04作为基础镜像
FROM nvidia/cuda:12.2.2-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive

# 备份并配置apt清华源
RUN cp /etc/apt/sources.list /etc/apt/sources.list.backup 2>/dev/null || true && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ jammy-security main restricted universe multiverse" >> /etc/apt/sources.list && \
    rm -rf /var/lib/apt/lists/*

# 更新包列表并安装基础工具
RUN apt-get update --fix-missing && \
    apt-get install -y --no-install-recommends \
    wget \
    curl \
    git \
    vim \
    sudo \
    zsh \
    build-essential \
    cmake \
    gdb \
    valgrind \
    pkg-config \
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    liblzma-dev \
    ca-certificates \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 单独安装clang工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    clang \
    clangd \
    clang-format \
    clang-tidy \
    && rm -rf /var/lib/apt/lists/*

# 安装Miniconda
RUN wget --quiet https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p /opt/miniconda && \
    rm /tmp/miniconda.sh && \
    /opt/miniconda/bin/conda clean -afy

# 配置conda清华源
RUN /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/free/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main/ && \
    /opt/miniconda/bin/conda config --add channels https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud/conda-forge/ && \
    /opt/miniconda/bin/conda config --set show_channel_urls yes

# 配置pip全局清华源
RUN mkdir -p /etc/pip && \
    echo "[global]" > /etc/pip/pip.conf && \
    echo "index-url = https://pypi.tuna.tsinghua.edu.cn/simple" >> /etc/pip/pip.conf && \
    echo "trusted-host = pypi.tuna.tsinghua.edu.cn" >> /etc/pip/pip.conf

# 将conda添加到PATH
ENV PATH="/opt/miniconda/bin:$PATH"

# 创建设置脚本
COPY setup.sh /tmp/setup.sh
RUN chmod +x /tmp/setup.sh

# 设置工作目录
WORKDIR /workspaces
