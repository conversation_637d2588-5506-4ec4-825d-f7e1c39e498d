{
  "name": "Merak Development Environment",
  "image": "harbor.wycluster.com/kunpeng/prod_2025:latest",
  // "build": {
  //   "dockerfile": "Dockerfile"
  // },

  // 容器运行时配置
  "runArgs": [
    "--network=host",
    "--gpus=all"
  ],
  
  // 挂载目录
  "mounts": [
    "source=/scratch,target=/scratch,type=bind",
    "source=/wydata,target=/wydata,type=bind"
  ],
  
  // 容器环境变量
  "containerEnv": {
    "SHELL": "/usr/bin/zsh"
  },
  
  // 端口转发
  "forwardPorts": [],

  "workspaceFolder": "/workspace/merak",
  "workspaceMount": "source=${localWorkspaceFolder},target=/workspace/merak,type=bind",

  // 创建后运行的命令
  "postCreateCommand": "zsh /workspace/merak/.devcontainer/setup.sh",

  "privileged": true,
  
  // 用户
  "updateRemoteUser": true,
  "remoteUser": "${localEnv:USER}",
  
  // Features - 管理用户和zsh
  "features": {
    "ghcr.io/devcontainers/features/common-utils:2": {
      "installZsh": true,
      "configureZshAsDefaultShell": true,
      "installOhMyZsh": true,
      "upgradePackages": true,
      "username": "${localEnv:USER}",
      "userUid": "automatic",
      "userGid": "automatic"
    }
  },
  
  
  // VS Code设置
  "customizations": {
    "vscode": {
      "extensions": [
        "ms-python.python",
        "ms-python.vscode-pylance",
        "ms-toolsai.jupyter",
        "ms-toolsai.jupyter-keymap",
        "ms-toolsai.jupyter-renderers",
        "ms-toolsai.vscode-jupyter-cell-tags",
        "ms-toolsai.vscode-jupyter-slideshow",
        "ms-python.pylint",
        "ms-python.black-formatter",
        "ms-vscode.cpptools",
        "ms-vscode.cpptools-extension-pack",
        "ms-vscode.cmake-tools",
        "twxs.cmake",
        "ms-vscode.cpptools-themes",
        "llvm-vs-code-extensions.vscode-clangd",
        "christian-kohler.path-intellisense",
        "davidanson.vscode-markdownlint",
        "eamodio.gitlens",
        "mhutchie.git-graph",
        "redhat.vscode-yaml",
        "tamasfe.even-better-toml"
      ],
      "settings": {
        "terminal.integrated.defaultProfile.linux": "zsh",
        "clangd.path": "/usr/bin/clangd",
        "clangd.arguments": [
          "--header-insertion=iwyu",
          "--completion-style=detailed",
          "--function-arg-placeholders",
          "--fallback-style=google"
        ],
        "C_Cpp.intelliSenseEngine": "Disabled",
        "http.proxy": "http://proxy-mlinfra.wycluster.com:1080"
      }
    }
  }
} 