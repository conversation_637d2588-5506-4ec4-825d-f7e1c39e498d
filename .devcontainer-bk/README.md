# Merak Dev Container 开发环境

这是一个为C++和Python开发优化的VS Code Dev Container配置。

## 主要特性

### 🐳 环境配置
- **基础镜像**: NVIDIA Ubuntu 22.04 (支持CUDA 12.2)
- **GPU支持**: 自动挂载所有GPU设备
- **挂载目录**: `/scratch` (NFS) 和 `/wydata` (本地数据)
- **用户配置**: 自动匹配宿主机用户UID/GID

### 🔧 开发工具
- **C++**: GCC/G++、CMake、GDB、clangd语言服务器、clang-format
- **Python**: Miniconda、Python 3.10+、常用科学计算库
- **Shell**: Zsh + Oh My Zsh + 自动补全插件

### 🌐 网络配置
- **代理**: 预配置清华大学代理服务器
- **镜像源**: APT、Conda、pip均使用清华源

## 使用方法

1. **启动容器**
   ```bash
   # 在VS Code中
   Ctrl+Shift+P → "Dev Containers: Reopen in Container"
   ```

2. **代理控制**
   ```bash
   setproxy      # 启用代理
   unsetproxy    # 禁用代理
   ```

3. **Python环境**
   ```bash
   conda --version        # 查看conda版本
   conda install numpy    # 安装包
   pip install package    # 使用pip安装
   ```

## C++开发配置

- **clangd**: 提供智能补全、错误检查、代码导航
- **编译数据库**: 建议为CMake项目生成`compile_commands.json`
  ```bash
  cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON .
  ```

## 自定义配置

- `devcontainer.json` - 主配置文件
- `Dockerfile` - 容器镜像配置  
- `setup.sh` - 初始化脚本

---

**注意**: 此配置针对内网环境优化，包含特定的代理和镜像源设置。 


```bash
pip config set global.index-url http://mlinfra:<EMAIL>/repository/pypi/simple
pip config set global.extra-index-url http://mlinfra:<EMAIL>/repository/wyproxy/simple
pip config set global.trusted-host nexus.wycluster.com
```