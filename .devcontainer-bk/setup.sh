#!/usr/bin/zsh

# 设置代理（如果需要）
export HTTP_PROXY="http://proxy-mlinfra.wycluster.com:1080"
export HTTPS_PROXY="http://proxy-mlinfra.wycluster.com:1080"

# 获取当前用户名（features已创建用户）
CURRENT_USER=$(whoami)
USER_HOME="/home/<USER>"

echo "正在配置开发环境..."
echo "当前用户: $CURRENT_USER"

# 为当前用户配置conda环境
sudo chown -R $CURRENT_USER:$CURRENT_USER /opt/miniconda

# 初始化conda for 当前用户（继承Dockerfile中的全局conda配置）
/opt/miniconda/bin/conda init zsh
/opt/miniconda/bin/conda init bash

# 安装Python常用包 - 使用清华源，不需要代理
/opt/miniconda/bin/conda install -y numpy pandas matplotlib jupyter ipython

# 安装zsh插件
echo "正在安装zsh插件..."

# 安装zsh-autosuggestions - 使用Dockerfile中配置的全局git代理
if [ ! -d "$USER_HOME/.oh-my-zsh/custom/plugins/zsh-autosuggestions" ]; then
    git clone https://github.com/zsh-users/zsh-autosuggestions $USER_HOME/.oh-my-zsh/custom/plugins/zsh-autosuggestions
fi

# 安装zsh-syntax-highlighting - 使用Dockerfile中配置的全局git代理
if [ ! -d "$USER_HOME/.oh-my-zsh/custom/plugins/zsh-syntax-highlighting" ]; then
    git clone https://github.com/zsh-users/zsh-syntax-highlighting.git $USER_HOME/.oh-my-zsh/custom/plugins/zsh-syntax-highlighting
fi

# 配置.zshrc
if [ -f "$USER_HOME/.zshrc" ]; then
    # 备份原文件
    cp $USER_HOME/.zshrc $USER_HOME/.zshrc.backup
    
    # 添加插件到.zshrc
    sed -i 's/plugins=(git)/plugins=(git z zsh-autosuggestions zsh-syntax-highlighting)/' $USER_HOME/.zshrc
    
    # 添加zsh-autosuggestions快捷键配置
    echo "" >> $USER_HOME/.zshrc
    echo "# zsh-autosuggestions快捷键配置" >> $USER_HOME/.zshrc
    echo "bindkey '^I^I' autosuggest-accept" >> $USER_HOME/.zshrc
    
    # 添加代理配置
    echo "" >> $USER_HOME/.zshrc
    echo "# 代理配置" >> $USER_HOME/.zshrc
    echo "alias setproxy='export HTTP_PROXY=http://proxy-mlinfra.wycluster.com:1080 && export HTTPS_PROXY=http://proxy-mlinfra.wycluster.com:1080'" >> $USER_HOME/.zshrc
    echo "alias unsetproxy='unset HTTP_PROXY && unset HTTPS_PROXY'" >> $USER_HOME/.zshrc
    
    # 添加conda配置
    echo "" >> $USER_HOME/.zshrc
    echo "# Conda配置" >> $USER_HOME/.zshrc
    echo "export PATH=\"/opt/miniconda/bin:\$PATH\"" >> $USER_HOME/.zshrc
    
    # 添加常用别名
    echo "" >> $USER_HOME/.zshrc
    echo "# 常用别名" >> $USER_HOME/.zshrc
    echo "alias ll='ls -alF'" >> $USER_HOME/.zshrc
    echo "alias la='ls -A'" >> $USER_HOME/.zshrc
    echo "alias l='ls -CF'" >> $USER_HOME/.zshrc
    echo "alias ..='cd ..'" >> $USER_HOME/.zshrc
    echo "alias ...='cd ../..'" >> $USER_HOME/.zshrc
    echo "alias grep='grep --color=auto'" >> $USER_HOME/.zshrc
fi


# 配置clangd
echo "正在配置clangd..."

# 创建clangd全局配置文件
mkdir -p $USER_HOME/.config/clangd
tee $USER_HOME/.config/clangd/config.yaml > /dev/null <<EOF
CompileFlags:
  Add: 
    - -std=c++17
    - -Wall
    - -Wextra
Diagnostics:
  ClangTidy:
    Add: 
      - readability-*
      - performance-*
      - modernize-*
    Remove:
      - modernize-use-trailing-return-type
Index:
  Background: Build
EOF

# 验证clangd安装
echo "clangd版本信息："
clangd --version

echo "环境配置完成！"

# 清理代理变量
unset HTTP_PROXY
unset HTTPS_PROXY 