[tool.ruff]
# Set line length to 120 characters
line-length = 120

# Enable auto-fixing for certain rule categories
fix = true

# Select the rules to check
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]

# Ignore specific rules if needed
ignore = [
    "E501",  # Line too long (we set line-length instead)
]

[tool.ruff.format]
# Use double quotes for strings
quote-style = "double"

# Use 4 spaces for indentation
indent-style = "space"

# Skip magic trailing comma
skip-magic-trailing-comma = false

# Respect existing line endings
line-ending = "auto"

[tool.ruff.isort]
# Known first-party modules
known-first-party = ["merak"]
