[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "kunpeng-replay"
dynamic = ["version"]
description = "Event-driven replay engine for financial data"
authors = [
    {name = "Kunpeng Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
]

dependencies = [
    "tqdm>=4.60.0",
    "pydantic>=1.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-asyncio>=0.18.0",
    "black>=21.0.0",
    "isort>=5.0.0",
    "mypy>=0.900",
]

[project.scripts]
kunpeng-replay = "kunpeng_replay.cli.main:main"

[tool.setuptools.dynamic]
version = {attr = "kunpeng_replay.version.__version__"}

[tool.setuptools.packages.find]
where = ["."]
include = ["kunpeng_replay*"]

[tool.black]
line-length = 100
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 100
