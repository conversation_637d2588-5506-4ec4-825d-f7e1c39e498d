#!/usr/bin/env python3
"""
测试TickBatchProcessor的Tick转换功能
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.insert(0, '/code/kunpeng_replay')

def test_tick_conversion():
    """测试批处理结果到Tick对象的转换"""
    print("测试Tick转换功能...")
    
    try:
        from kunpeng_replay.processors.tick_batch_processor import TickBatchProcessor
        from kunpeng_replay.events.market_data_event import MarketDataEvent
        from kunpeng_replay.merak_adapter import Tick
        
        # 创建一个简单的logger
        class SimpleLogger:
            def info(self, msg): print(f"INFO: {msg}")
            def error(self, msg): print(f"ERROR: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
        
        logger = SimpleLogger()
        
        # 创建TickBatchProcessor实例
        processor = TickBatchProcessor(logger)
        print(f"✓ 成功创建TickBatchProcessor: {processor.name}")
        
        # 创建一个测试事件
        event = MarketDataEvent(
            ticker="000001.SZ",
            updatetime=93000000,  # 09:30:00
            localtime=1640995800000000,  # 微秒时间戳
            data={}
        )
        
        # 测试默认Tick创建
        default_tick = processor._create_default_tick(event)
        print(f"✓ 成功创建默认Tick: {default_tick.tick_ut}")
        print(f"  买一价: {default_tick.bp1}, 卖一价: {default_tick.ap1}")
        
        # 测试批处理结果转换（模拟数据）
        # 创建一个模拟的批处理结果
        mock_batch_result = np.array([
            [93000000, 10.0, 9.99, 0, 0, 0, 0, 0, 0, 0, 0,  # 时间戳和买盘价格
             100, 200, 0, 0, 0, 0, 0, 0, 0, 0,              # 买盘量
             10.01, 10.02, 0, 0, 0, 0, 0, 0, 0, 0,          # 卖盘价格
             150, 250, 0, 0, 0, 0, 0, 0, 0, 0,              # 卖盘量
             9.95, 10.0, 10.05, 9.98, 1000, 10000,          # 基础价格信息
             5500, 5500, 9.95, 10.05, 10.95, 8.96]         # 其他信息
        ])
        
        # 模拟一个简单的market_data_reader
        class MockMarketDataReader:
            def get_tick_column_mapping(self):
                return {
                    "UpdateTime": 0,
                    "BidPrice1": 1, "BidPrice2": 2,
                    "BidVol1": 11, "BidVol2": 12,
                    "AskPrice1": 21, "AskPrice2": 22,
                    "AskVol1": 31, "AskVol2": 32,
                    "PreClose": 41, "OpenPrice": 42, "HighPrice": 43, "LowPrice": 44,
                    "AccVolume": 45, "AccTurnover": 46,
                    "TotalBidVol": 47, "TotalAskVol": 48,
                    "BidWAvgPrice": 49, "AskWAvgPrice": 50,
                    "UpLimitPrice": 51, "DownLimitPrice": 52
                }
        
        processor.market_data_reader = MockMarketDataReader()
        
        # 测试转换
        converted_tick = processor._convert_batch_result_to_tick(mock_batch_result, event)
        if converted_tick:
            print(f"✓ 成功转换批处理结果到Tick对象")
            print(f"  时间戳: {converted_tick.tick_ut}")
            print(f"  买一价: {converted_tick.bp1}, 买二价: {converted_tick.bp2}")
            print(f"  买一量: {converted_tick.bv1}, 买二量: {converted_tick.bv2}")
            print(f"  卖一价: {converted_tick.ap1}, 卖二价: {converted_tick.ap2}")
            print(f"  卖一量: {converted_tick.av1}, 卖二量: {converted_tick.av2}")
            print(f"  昨收价: {converted_tick.tick_pre_close}")
            print(f"  成交量: {converted_tick.tick_vol}, 成交额: {converted_tick.tick_tvr}")
        else:
            print("✗ 转换失败")
            return False
        
        print("✓ Tick转换功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_process_tick_methods():
    """测试两个process_tick方法"""
    print("\n测试process_tick方法...")
    
    try:
        from kunpeng_replay.processors.tick_batch_processor import TickBatchProcessor
        from kunpeng_replay.events.market_data_event import MarketDataEvent
        
        class SimpleLogger:
            def info(self, msg): pass
            def error(self, msg): print(f"ERROR: {msg}")
            def warning(self, msg): print(f"WARNING: {msg}")
        
        processor = TickBatchProcessor(SimpleLogger())
        
        event = MarketDataEvent(
            ticker="000001.SZ",
            updatetime=93000000,
            localtime=1640995800000000,
            data={}
        )
        
        # 测试简单的process_tick方法（BaseTickProcessor接口）
        tick = processor.process_tick(event)
        if tick:
            print(f"✓ process_tick成功: {tick.tick_ut}")
        else:
            print("✗ process_tick失败")
            return False
        
        # 注意：process_tick_batch需要market_data_reader，这里只测试接口
        print("✓ process_tick方法测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("=== TickBatchProcessor Tick转换测试 ===")
    
    tests = [
        test_tick_conversion,
        test_process_tick_methods,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！Tick转换功能正常")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
