import datetime
import sys
import time

import numpy as np
import pandas as pd
from datahub_api import get_ashare_snap
from loguru import logger

from merak.base_features.kbar.tech_v2 import *
from merak.base_features.tick.tick_ba_base_v1 import TickBABaseV1
from merak.base_features.tick.tick_ba_lvn_pct import TickBALvnPct
from merak.base_features.tick.tick_mp_base_v1 import TickMPBaseV1
from merak.base_features.tick.tick_mp_base_v2 import TickMPBaseV2
from merak.base_features.tick.tick_voi import TickVOI
from merak.base_features.tick.tick_vol_pct import TickVolPct
from merak.constants import DATES, TICKERS
from merak.kbar.common import WINDOW_SIZE
from merak.kbar.kbar_day import KBarDay
from merak.settings import TRADING_DAY
from merak.tick_engine import TickEngine

sys.path.append("/scratch/wy_prod/alpha/feat/so")
from feature_deri_api_v102 import feature_deri, get_res_features

print(TRADING_DAY, len(TICKERS), len(DATES))

timecuts = list(pd.date_range(start="09:30:00", end="09:31:00", freq="3s").strftime("%H%M%S").values)
print(len(timecuts), timecuts)

ticker = "600036.SH"
ticker_id = TICKERS.index(ticker)
date_idx = DATES.index(TRADING_DAY)
print(f"{TRADING_DAY=} {ticker=} {ticker_id=} {date_idx=}")

all_base_feat_names = np.load("/scratch/data/stock_files/factors_eod/hf_features/prod/093003/base_feat_names.npy")
print(f"{len(all_base_feat_names)=}")


intra_zp_kw = {
    "st_date": TRADING_DAY,
    "ed_date": TRADING_DAY,
    "cuts": timecuts,
    "features": {
        "intra": all_base_feat_names,
    },
    "version": "prod",
    "cores": 20,
}


n_dict, v_dict = feature_deri(**intra_zp_kw)
feature_deri_output = v_dict["intra"][:, 0, ticker_id, :]
feature_names = n_dict["intra"]
feature_deri_df = pd.DataFrame(feature_deri_output, columns=feature_names)


def convert_timestamp(timestamp):
    timestamp_seconds = timestamp / 1_000_000_000
    dt = datetime.datetime.fromtimestamp(timestamp_seconds, tz=datetime.timezone(datetime.timedelta(hours=8)))
    formatted_time = dt.strftime("%H%M%S")
    return formatted_time


df = get_ashare_snap(date=TRADING_DAY, inst=ticker, return_type="df")
df["UpdateTime"] = df["UpdateTime"].apply(convert_timestamp)
start_idx = np.searchsorted(df["UpdateTime"].values, "093000") - 1
start_time = df["UpdateTime"].values[start_idx]
df = df[(df["UpdateTime"] >= start_time) & (df["UpdateTime"] <= "093100")]


column_names = [
    "UpdateTime",
    "BidPrice1",
    "BidPrice2",
    "BidPrice3",
    "BidPrice4",
    "BidPrice5",
    "BidPrice6",
    "BidPrice7",
    "BidPrice8",
    "BidPrice9",
    "BidPrice10",
    "BidVol1",
    "BidVol2",
    "BidVol3",
    "BidVol4",
    "BidVol5",
    "BidVol6",
    "BidVol7",
    "BidVol8",
    "BidVol9",
    "BidVol10",
    "AskPrice1",
    "AskPrice2",
    "AskPrice3",
    "AskPrice4",
    "AskPrice5",
    "AskPrice6",
    "AskPrice7",
    "AskPrice8",
    "AskPrice9",
    "AskPrice10",
    "AskVol1",
    "AskVol2",
    "AskVol3",
    "AskVol4",
    "AskVol5",
    "AskVol6",
    "AskVol7",
    "AskVol8",
    "AskVol9",
    "AskVol10",
    "PreClose",
    "OpenPrice",
    "HighPrice",
    "LowPrice",
    "AccVolume",
    "AccTurnover",
    "TotalBidVol",
    "TotalAskVol",
    "BidWAvgPrice",
    "AskWAvgPrice",
    "UpLimitPrice",
    "DownLimitPrice",
]

df = df[column_names]


engine = TickEngine()
engine.add_tick_stream(TickBABaseV1(ticker_id=ticker_id, trading_day=TRADING_DAY))
engine.add_tick_stream(TickMPBaseV1(ticker_id=ticker_id, trading_day=TRADING_DAY))
engine.add_tick_stream(TickVolPct(ticker_id=ticker_id, trading_day=TRADING_DAY))
engine.add_tick_stream(TickBALvnPct(ticker_id=ticker_id, trading_day=TRADING_DAY))
engine.add_tick_stream(TickMPBaseV2(ticker_id=ticker_id, trading_day=TRADING_DAY))

# kbar_calculator = KBarDay(ticker_id=ticker_id, trading_day=TRADING_DAY)
# kbar_calculator.register(tech_v2_bias)
# kbar_calculator.register(tech_v2_aroon)
# kbar_calculator.register(tech_v2_crsi)
# kbar_calculator.register(tech_v2_dbcd)
# kbar_calculator.register(tech_v2_dhilo)
# kbar_calculator.register(tech_v2_stdamb10)
# kbar_calculator.register(tech_v2_pvi_nvi)
# kbar_calculator.register(tech_v2_rvi10)
# kbar_calculator.register(tech_v2_si)
# kbar_calculator.register(tech_v2_srmi)
# kbar_calculator.register(tech_v2_tr)
# kbar_calculator.register(tech_v2_tvrshp)
# kbar_calculator.register(tech_v2_rvi10)
# kbar_calculator.register(tech_v2_cko)

# engine.add_tick_stream(kbar_calculator)

times = []
streaming_features = []
for i, row in enumerate(df.itertuples(index=False)):
    start_time = time.time_ns()
    features = engine.process_tick(list(row))
    end_time = time.time_ns()
    times.append((end_time - start_time) / 1000)
    streaming_features.append(features)
    # break

avg, std = np.mean(times), np.std(times)
logger.debug(f"Streaming engine time: {avg:.2f}us ± {std:.2f}us")

streaming_df = pd.DataFrame(streaming_features)

tar_df = feature_deri_df[streaming_df.columns]

rel_err = np.abs(tar_df - streaming_df) / tar_df


max_rel_err = rel_err.max()
threshold = 1e-6
high_error_columns = max_rel_err[max_rel_err >= threshold]

for col in high_error_columns.index:
    print(f"{col}: {high_error_columns[col]:.2e}")


nan_mismatch_columns = []
for col in tar_df.columns:
    tar_nan = tar_df[col].isna()
    streaming_nan = streaming_df[col].isna()

    # 检查 NaN 位置是否完全一致
    if not tar_nan.equals(streaming_nan):
        nan_mismatch_columns.append(col)
        tar_nan_count = tar_nan.sum()
        streaming_nan_count = streaming_nan.sum()

        diff_positions = tar_nan != streaming_nan
        if diff_positions.any():
            diff_indices = diff_positions[diff_positions].index.tolist()

total_problematic = set(high_error_columns.index) | set(nan_mismatch_columns)
print(f"{total_problematic=}")
