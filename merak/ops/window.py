import math
from collections import deque


class SlidingWindowMetrics:
    def __init__(self, window_size: int):
        if window_size <= 0:
            raise ValueError("window size must be greater than 0")

        self.window_size = window_size
        self.window = deque(maxlen=window_size)
        self.sum = 0.0
        self.valid_count = 0  # 跟踪非 NaN 值的数量

    def on_tick(self, value: float):
        # 如果第一个输入是NaN，返回NaN
        if len(self.window) == 0 and math.isnan(value):
            self.window.append(value)
            return float("nan"), float("nan"), float("nan")

        if len(self.window) == self.window_size:
            old_value = self.window[0]
            # Handle NaN values
            if not math.isnan(old_value):
                self.sum -= old_value
                self.valid_count -= 1

        self.window.append(value)
        # Handle NaN values
        if not math.isnan(value):
            self.sum += value
            self.valid_count += 1

        window_len = len(self.window)
        if window_len == 0 or self.valid_count == 0:
            return 0, 0, 0

        # 计算均值 - 使用有效值数量作为分母
        avg = self.sum / self.valid_count
        # 计算PMR (最新值 - 最早值)
        pmr = self.window[-1] - self.window[0] if window_len >= 2 else 0
        # 计算NAVG (最新值 - 均值)
        navg = self.window[-1] - avg
        return avg, pmr, navg


class SlidingWindowSum:
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.window = deque(maxlen=window_size)
        self.sum = 0.0

    def on_tick(self, value: float):
        if len(self.window) == self.window_size:
            old_value = self.window[0]
            # Handle NaN values
            if not math.isnan(old_value):
                self.sum -= old_value

        self.window.append(value)
        # Handle NaN values
        if not math.isnan(value):
            self.sum += value

        return self.sum


class SlidingWindowMean:
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.window = deque(maxlen=window_size)
        self.sum = 0.0

    def on_tick(self, value: float):
        if len(self.window) == self.window_size:
            old_value = self.window[0]
            # Handle NaN values
            if not math.isnan(old_value):
                self.sum -= old_value

        self.window.append(value)
        # Handle NaN values
        if not math.isnan(value):
            self.sum += value

        return self.sum / len(self.window)


class SlidingWindowReturnMetrics:
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.ret_window = deque(maxlen=window_size)

        self.up_ret_sum = 0.0
        self.down_ret_sum = 0.0
        self.up_ret_count = 0
        self.down_ret_count = 0

    def on_tick(self, ret_value: float):
        # 如果第一个输入是NaN，返回NaN
        if len(self.ret_window) == 0 and math.isnan(ret_value):
            self.ret_window.append(ret_value)
            return float("nan"), float("nan"), float("nan"), float("nan"), float("nan")

        if len(self.ret_window) == self.window_size:
            old_ret = self.ret_window[0]

            # Remove old values (handle NaN)
            if not math.isnan(old_ret):
                if old_ret > 0:
                    self.up_ret_sum -= old_ret
                    self.up_ret_count -= 1
                elif old_ret < 0:
                    self.down_ret_sum -= old_ret
                    self.down_ret_count -= 1

        # Add new values
        self.ret_window.append(ret_value)

        # Handle NaN values
        if not math.isnan(ret_value):
            if ret_value > 0:
                self.up_ret_sum += ret_value
                self.up_ret_count += 1
            elif ret_value < 0:
                self.down_ret_sum += ret_value
                self.down_ret_count += 1

        # Calculate metrics
        mp_up_ret = self.up_ret_sum
        mp_down_ret = self.down_ret_sum

        # Calculate per-tick averages
        mp_up_ret_ptk = mp_up_ret / self.up_ret_count if self.up_ret_count > 0 else 0.0
        mp_down_ret_ptk = mp_down_ret / self.down_ret_count if self.down_ret_count > 0 else 0.0

        # Combined up/down return per tick
        mp_ud_ret_ptk = mp_up_ret_ptk + mp_down_ret_ptk

        return mp_up_ret, mp_down_ret, mp_up_ret_ptk, mp_down_ret_ptk, mp_ud_ret_ptk


class SlidingWindowTrend:
    def __init__(self, window_size: int):
        self.window_size = window_size
        self.window = deque(maxlen=window_size)

        self.sum = 0.0
        self.abs_sum = 0.0

    def on_tick(self, value: float):
        # 如果第一个输入是NaN，返回NaN
        if len(self.window) == 0 and math.isnan(value):
            self.window.append(value)
            return float("nan")

        if len(self.window) == self.window_size:
            old_value = self.window[0]

            # Remove old values (handle NaN)
            if not math.isnan(old_value):
                self.sum -= old_value
                self.abs_sum -= abs(old_value)

        # Add new values
        self.window.append(value)

        # Handle NaN values
        if not math.isnan(value):
            self.sum += value
            self.abs_sum += abs(value)

        # Calculate trend indicator
        trend = self.sum / self.abs_sum if self.abs_sum > 0 else 0.0

        return trend


class SlidingWindowStdCustom:
    def __init__(self, window_size: int, min_count: int = 5, ddof: int = 1, mode: int = 0):
        # 基本参数校验
        if window_size <= 0:
            raise ValueError("window_size must be positive")
        if min_count < ddof + 1:
            raise ValueError("min_count must be ≥ ddof + 1")

        self.window_size = window_size
        self.min_count = min_count
        self.ddof = ddof
        self.mode = mode  # 0: 不转换，1: <= 0 -> 0，2: >= 0 -> 0

        self.window = deque()
        self.sum_squared = 0.0
        self.valid_count = 0  # 跟踪非 NaN 值的数量

    def on_tick(self, value: float) -> float:
        # 弹出旧值（若窗口已满）
        if len(self.window) == self.window_size:
            old = self.window.popleft()
            if not math.isnan(old):
                # 应用转换（如果启用 fillby_zero）
                if self.mode == 1:
                    x = max(old, 0.0)
                elif self.mode == 2:
                    x = min(old, 0.0)
                else:
                    x = old
                self.sum_squared -= x * x
                self.valid_count -= 1

        # 追加新值
        self.window.append(value)
        if not math.isnan(value):
            # 应用转换（如果启用 fillby_zero）
            if self.mode == 1:
                x = max(value, 0.0)
            elif self.mode == 2:
                x = min(value, 0.0)
            else:
                x = value
            self.sum_squared += x * x
            self.valid_count += 1

        # 检查是否有足够的有效值进行计算
        if self.valid_count < self.min_count or self.valid_count <= self.ddof:
            return float("nan")

        variance = self.sum_squared / (self.valid_count - self.ddof)

        # 浮点误差保护：理论上 var ≥ 0
        if variance < 0:
            variance = 0.0

        return math.sqrt(variance)


class SlidingWindowCorr:
    def __init__(self, window_size: int, min_count: int = 5):
        self.window_size = window_size
        self.min_count = min_count
        self.x_window = deque(maxlen=window_size)
        self.y_window = deque(maxlen=window_size)

        self.sum_x = 0.0
        self.sum_y = 0.0
        self.sum_xy = 0.0
        self.sum_x2 = 0.0
        self.sum_y2 = 0.0

    def on_tick(self, x: float, y: float):
        if len(self.x_window) == self.window_size:
            old_x = self.x_window[0]
            old_y = self.y_window[0]

            # Remove old values (handle NaN)
            if not math.isnan(old_x) and not math.isnan(old_y):
                self.sum_x -= old_x
                self.sum_y -= old_y
                self.sum_xy -= old_x * old_y
                self.sum_x2 -= old_x * old_x
                self.sum_y2 -= old_y * old_y

        # Add new values
        self.x_window.append(x)
        self.y_window.append(y)

        # Handle NaN values
        if not math.isnan(x) and not math.isnan(y):
            self.sum_x += x
            self.sum_y += y
            self.sum_xy += x * y
            self.sum_x2 += x * x
            self.sum_y2 += y * y

        # Calculate correlation
        window_count = len(self.x_window)
        if window_count < self.min_count:
            return float("nan")

        mean_x = self.sum_x / window_count
        mean_y = self.sum_y / window_count

        # Calculate covariance: E[XY] - E[X]E[Y]
        covariance = self.sum_xy / window_count - mean_x * mean_y

        # Calculate standard deviations
        var_x = self.sum_x2 / window_count - mean_x * mean_x
        var_y = self.sum_y2 / window_count - mean_y * mean_y

        # Check variance > 0 to handle floating-point precision issues
        # Theoretically variance should always be >= 0, but due to accumulation of
        # rounding errors in floating-point arithmetic, it might become slightly negative
        if var_x <= 0 or var_y <= 0:
            return 0.0

        std_x = var_x**0.5
        std_y = var_y**0.5

        # Calculate correlation: cov(X,Y) / (std(X) * std(Y))
        correlation = covariance / (std_x * std_y)

        return correlation


class SlidingWindowMinMax:
    """滑动窗口最大值、最小值和首值计算器"""

    def __init__(self, window_size: int):
        self.window_size = window_size
        self.window = deque(maxlen=window_size)

    def on_tick(self, value: float):
        self.window.append(value)

        if len(self.window) == 0:
            return float("nan"), float("nan"), float("nan")

        # 过滤掉NaN值进行计算
        valid_values = [v for v in self.window if not math.isnan(v)]

        if len(valid_values) == 0:
            return float("nan"), float("nan"), float("nan")

        # 计算最大值、最小值和首值
        max_val = max(valid_values)
        min_val = min(valid_values)
        first_val = valid_values[0] if valid_values else float("nan")

        return max_val, min_val, first_val


class SlidingWindowSkewKurt:
    """滑动窗口偏度和峰度计算器（均值固定为 0），时间复杂度 O(1)"""

    def __init__(self, window_size: int, min_count: int = 5, ddof: int = 1):
        # 基本参数校验
        if window_size <= 0:
            raise ValueError("window_size must be positive")
        if min_count < 4:  # 偏度和峰度至少需要4个点
            raise ValueError("min_count must be ≥ 4 for skew and kurt calculation")

        self.window_size = window_size
        self.min_count = min_count
        self.ddof = ddof

        self.window = deque()
        self.sum_squared = 0.0  # 用于计算方差的平方和
        self.sum_cubed = 0.0  # 三阶累积和
        self.sum_fourth = 0.0  # 四阶累积和
        self.valid_count = 0  # 跟踪非 NaN 值的数量

    def on_tick(self, value: float) -> "tuple[float, float]":
        # 弹出旧值（若窗口已满）
        if len(self.window) == self.window_size:
            old = self.window.popleft()
            if not math.isnan(old):
                # 移除旧值的贡献（基于均值为 0）
                _squared = old * old
                self.sum_squared -= _squared
                self.sum_cubed -= _squared * old
                self.sum_fourth -= _squared * _squared
                self.valid_count -= 1

        # 追加新值
        self.window.append(value)
        if not math.isnan(value):
            # 添加新值的贡献（基于均值为 0）
            _squared = value * value
            self.sum_squared += _squared
            self.sum_cubed += _squared * value
            self.sum_fourth += _squared * _squared
            self.valid_count += 1

        # 检查是否有足够的有效值进行计算
        if self.valid_count < self.min_count:
            return float("nan"), float("nan")

        # 计算标准差（基于均值为 0）
        variance = self.sum_squared / (self.valid_count - self.ddof)

        # 浮点误差保护：理论上 var ≥ 0
        if variance <= 0:
            return 0.0, 0.0

        std = math.sqrt(variance)

        # 直接根据累积量计算 Z-score 的三次方和四次方之和
        # sum_zp = Σ (x_i / std)^p = Σ x_i^p / std^p
        sum_z3 = self.sum_cubed / (std**3)
        sum_z4 = self.sum_fourth / (std**4)

        count = float(self.valid_count)

        # 计算偏度（带偏差调整，仿照 NanSkew）
        if count >= 3:
            bias_adj_skew = count / ((count - 1) * (count - 2))
            skew = bias_adj_skew * sum_z3
        else:
            skew = float("nan")

        # 计算峰度（带偏差调整，仿照 NanKurt）
        if count >= 4:
            bias_adj_kurt = (count * (count + 1)) / ((count - 1) * (count - 2) * (count - 3))
            sub = (3 * (count - 1) ** 2) / ((count - 2) * (count - 3))
            kurt = bias_adj_kurt * sum_z4 - sub
        else:
            kurt = float("nan")

        return skew, kurt


class SlidingWindowReturnMetricsV2:
    """V2版本的返回指标计算器，针对 tick_mp_base_v2 的特定逻辑"""

    def __init__(self, window_size: int):
        self.window_size = window_size
        self.ret_window = deque(maxlen=window_size)

        self.up_ret_sum = 0.0
        self.down_ret_sum = 0.0
        self.up_ret_count = 0
        self.down_ret_count = 0

    def on_tick(self, ret_value: float):
        # 如果第一个输入是NaN，返回NaN
        if len(self.ret_window) == 0 and math.isnan(ret_value):
            self.ret_window.append(ret_value)
            return float("nan"), float("nan")

        if len(self.ret_window) == self.window_size:
            old_ret = self.ret_window[0]

            # Remove old values (handle NaN)
            if not math.isnan(old_ret):
                if old_ret > 0:
                    self.up_ret_sum -= old_ret
                    self.up_ret_count -= 1
                elif old_ret < 0:
                    self.down_ret_sum -= old_ret
                    self.down_ret_count -= 1

        # Add new values
        self.ret_window.append(ret_value)

        # Handle NaN values
        if not math.isnan(ret_value):
            if ret_value > 0:
                self.up_ret_sum += ret_value
                self.up_ret_count += 1
            elif ret_value < 0:
                self.down_ret_sum += ret_value
                self.down_ret_count += 1

        # 计算 V2 需要的指标
        mp_ret_ud = self.up_ret_sum - self.down_ret_sum
        mp_ret_upcnt_pct = self.up_ret_count / len(self.ret_window) if len(self.ret_window) > 0 else 0.0

        return mp_ret_ud, mp_ret_upcnt_pct
