import math
from abc import abstractmethod
from collections import deque


class BaseWindowOp:
    def __init__(self, window_size: int):
        self.window_size = window_size

    @abstractmethod
    def update(self, value: float) -> float:
        raise NotImplementedError

    @abstractmethod
    def write_back(self):
        raise NotImplementedError


class MoveSum(BaseWindowOp):
    def __init__(self, window_size: int):
        super().__init__(window_size)

        self.window = deque(maxlen=self.window_size)
        self.sum = 0.0
        self._prev_value = None

    def update(self, value: float) -> float:
        temp_sum = self.sum
        if len(self.window) == self.window_size:
            old_value = self.window[0]
            if not math.isnan(old_value):
                temp_sum -= old_value

        if not math.isnan(value):
            temp_sum += value

        self._prev_value = value
        return temp_sum

    def write_back(self):
        if self._prev_value is None:
            return

        if len(self.window) == self.window_size:
            old_value = self.window[0]
            if not math.isnan(old_value):
                self.sum -= old_value

        self.window.append(self._prev_value)
        if not math.isnan(self._prev_value):
            self.sum += self._prev_value


class MoveMean(BaseWindowOp):
    def __init__(self, window_size: int):
        super().__init__(window_size)

        self.window = deque(maxlen=self.window_size)
        self.sum = 0.0
        self.count = 0  # 有效值的数量
        self._prev_value = None

    def update(self, value: float) -> float:
        """更新窗口并计算平均值。

        Args:
            value: 新的值

        Returns:
            当前窗口的平均值，如果没有有效值则返回 NaN
        """
        temp_sum = self.sum
        temp_count = self.count

        # 如果窗口满了，减去即将移出的值
        if len(self.window) == self.window_size:
            old_value = self.window[0]
            if not math.isnan(old_value):
                temp_sum -= old_value
                temp_count -= 1

        # 加上新的值
        if not math.isnan(value):
            temp_sum += value
            temp_count += 1

        self._prev_value = value

        # 返回平均值
        if temp_count == 0:
            return math.nan
        else:
            return temp_sum / temp_count

    def write_back(self):
        """将临时计算的结果写回内部状态。"""
        if self._prev_value is None:
            return

        # 如果窗口满了，减去即将移出的值
        if len(self.window) == self.window_size:
            old_value = self.window[0]
            if not math.isnan(old_value):
                self.sum -= old_value
                self.count -= 1

        # 添加新值到窗口
        self.window.append(self._prev_value)

        # 如果新值不是 NaN，更新 sum 和 count
        if not math.isnan(self._prev_value):
            self.sum += self._prev_value
            self.count += 1


class MoveStd(BaseWindowOp):
    def __init__(self, window_size: int, ddof: int = 1, min_n = None):
        """
        Args:
            window_size (int): The number of elements in the moving window.
            ddof (int): Means Delta Degrees of Freedom. The divisor used in calculations is N - ddof, where N 
                represents the number of elements. By default ddof is 1.
            min_count (int, None): If the number of non-NaN values in a window is less than min_count, then a value 
                of NaN is assigned to the window. By default min_count is None, which is equivalent to setting 
                min_count equal to max(window_size // 2, 1).
        """

    def update(self, value: float) -> float:
        pass


    def write_back(self):
        pass
