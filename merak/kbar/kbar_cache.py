from __future__ import annotations

from merak.define import KBar, Tick


class KBarCache:
    def __init__(self):
        self.open: float | None = None
        self.close: float | None = None
        self.high: float | None = None
        self.low: float | None = None
        self.volumn: float = 0.0
        self.amount: float = 0.0
        self.float_shr: float | None = None
        self.adjust_factor: float | None = None
        self.timestamps: list[str] = []

    def update(self, tick: Tick, is_intra: bool = True) -> KBar:
        mid_price = round((tick.bp1 + tick.ap1) / 2, 3)
        if is_intra:  # intra day kbar
            if self.open is None:
                # open is the mid price of the first tick of the current kbar
                self.open = mid_price
                self.high = mid_price
                self.low = mid_price
            else:
                self.high = max(self.high, mid_price)  # type: ignore
                self.low = min(self.low, mid_price)  # type: ignore
        else:  # day bar
            self.open = tick.tick_op
            self.high = tick.tick_hp
            self.low = tick.tick_lp

        self.close = mid_price
        self.volumn += tick.tick_vol
        self.amount += tick.tick_tvr
        self.timestamps.append(tick.tick_ut)

        return self.to_kbar()

    def set_float_shr_adjust_factor(self, float_shr: float, adjust_factor: float):
        self.float_shr = float_shr
        self.adjust_factor = adjust_factor

    def reset(self):
        self.open = None
        self.close = None
        self.high = None
        self.low = None
        self.volumn = 0.0
        self.amount = 0.0
        # 不需要reset float_shr & adjust_factor，因为他们在第一个tick时被设置后，全天保持不变
        self.timestamps = []

    def to_kbar(self) -> KBar:
        assert self.open is not None, "open must be set before calling to_kbar"
        assert self.close is not None, "close must be set before calling to_kbar"
        assert self.high is not None, "high must be set before calling to_kbar"
        assert self.low is not None, "low must be set before calling to_kbar"
        assert self.float_shr is not None, "float_shr must be set before calling to_kbar"
        assert self.adjust_factor is not None, "adjust_factor must be set before calling to_kbar"

        return KBar(
            open=self.open,
            close=self.close,
            high=self.high,
            low=self.low,
            volume=self.volumn,
            amount=self.amount,
            float_shr=self.float_shr,
            adjust_factor=self.adjust_factor,
            timestamps=self.timestamps,
        )
