from __future__ import annotations

from collections import deque

from merak.define import KBar, KBarStream
from merak.kbar.window import MoveSum


class DemeTechIndicator(KBarStream):
    def __init__(self, window_size: int = 10):
        super().__init__()

        self.typ_window = deque(maxlen=window_size)
        self.hlap_window = deque(maxlen=window_size)
        self.float_tr_window = deque(maxlen=window_size)

    def on_kbar(self, kbar: KBar) -> dict[str, float]:
        typ = (kbar.high + kbar.low + kbar.close) / 3
        self.typ_window.append(typ)
        hlap = round((kbar.high + kbar.low) / 2, 3)
        self.hlap_window.append(hlap)
        float_tr = kbar.volume / kbar.float_shr

        return {}

    def write_back(self):
        pass
