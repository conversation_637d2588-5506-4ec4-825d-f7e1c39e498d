from __future__ import annotations

import copy
import dataclasses
from collections import deque

import numpy as np
import wy_tdates

from merak.data.eod import get_eod_data
from merak.data.load_kbar import get_kbar
from merak.define import KBAR_CONFIGS, KBar, KBarStream, Tick, TickStream
from merak.kbar.kbar_cache import KBarCache


class KBarEngine(TickStream):
    def __init__(self, ticker_idx: int, trading_day: str, freq: str, window_size: int):
        assert freq in KBAR_CONFIGS, f"freq {freq} not in {KBAR_CONFIGS.keys()}"
        self.ticker_idx = ticker_idx
        self.trading_day = trading_day
        self.freq = freq
        self.window_size = window_size

        self.suffix_map = {
            "day": "",
            "15s": "_15s",
            "60s": "_60s",
            "600s": "_600s",
            "1800s": "_1800s",
        }

        self.labels = KBAR_CONFIGS[freq].labels
        self.thresholds = KBAR_CONFIGS[freq].thresholds
        self.thresholds_idx = 0

        self.kbars: deque[KBar] = deque(maxlen=window_size)
        self.kbar_cache = KBarCache()

        self.kbar_streams: list[KBarStream] = []

        self.preload = {}
        _prev_day = str(wy_tdates.prev_trading_day(self.trading_day))
        self.preload["stk_float_shr"] = get_eod_data(
            field="stk_float_shr",
            ticker_index=self.ticker_idx,
            tradingday=_prev_day,
            ndays=1,
        )
        self.preload["stk_adjust_factor"] = get_eod_data(
            field="stk_adjust_factor",
            ticker_index=self.ticker_idx,
            tradingday=_prev_day,
            ndays=1,
        )
        self.preload["stk_close"] = get_eod_data(
            field="stk_close",
            ticker_index=self.ticker_idx,
            tradingday=_prev_day,
            ndays=1,
            forward_fill=True,
        )
        self.is_first_tick = True
        self.pre_tick: Tick | None = None

    def write_back_streams(self):
        for kbar_stream in self.kbar_streams:
            kbar_stream.write_back()

    def on_tick(self, tick: Tick) -> dict[str, float]:
        if self.is_first_tick:
            mul = self.preload["stk_close"] / tick.tick_pre_close
            float_shr = mul * self.preload["stk_float_shr"] * 10000  # 万股
            adj_f_cur = mul * self.preload["stk_adjust_factor"]
            self.kbar_cache.set_float_shr_adjust_factor(
                float_shr=float_shr,
                adjust_factor=1.0,  # 历史的复权因子会按今天的调整，所以今天始终是1
            )
            # adjust histroy kbar's adjust_factor
            for kbar in self.kbars:
                kbar.adjust_factor /= adj_f_cur
                self._trigger_deri_streams(kbar)
                self.write_back_streams()

            self.is_first_tick = False

        # check if the current tick is the start of a new kbar
        if self.thresholds_idx < len(self.thresholds) and tick.tick_ut > self.thresholds[self.thresholds_idx]:
            # the current tick is the start of a new kbar, we need to extract the old kbar from the stream and persist it
            self.close_kbar(tick_ut=tick.tick_ut)

        self.pre_tick = copy.deepcopy(tick)

        # update the current kbar
        self.kbar_cache.update(tick, is_intra=self.freq != "day")
        current_kbar = self.kbar_cache.to_kbar()

        # trigger the derived streams
        return self._trigger_deri_streams(current_kbar)

    def register(self, kbar_stream: KBarStream):
        self.kbar_streams.append(kbar_stream)

    def _trigger_deri_streams(self, kbar: KBar):
        res = {}
        for kbar_stream in self.kbar_streams:
            values = kbar_stream.on_kbar(kbar)
            for k, v in values.items():
                res[f"{k}{self.suffix_map[self.freq]}"] = v
        return res

    def close_kbar(self, tick_ut: str | None = None):
        completed_kbar = self.kbar_cache.to_kbar()
        self.write_back_streams()
        self.kbar_cache.reset()
        if tick_ut is not None:
            # 有可能少了多个tick导致需要跳过多个kbar
            while self.thresholds_idx < len(self.thresholds) and tick_ut > self.thresholds[self.thresholds_idx]:
                completed_kbar.label = self.labels[self.thresholds_idx]
                self.kbars.append(copy.deepcopy(completed_kbar))
                self.thresholds_idx += 1
                # 如果当前kbar时间范围内没有收到任何tick时，用当前tick构造一个kbar
                assert self.pre_tick is not None, "Did not receive any tick before this kbar"
                mid_price = round((self.pre_tick.bp1 + self.pre_tick.ap1) / 2, 3)
                completed_kbar.open = mid_price
                completed_kbar.close = mid_price
                completed_kbar.high = mid_price
                completed_kbar.low = mid_price
                completed_kbar.volume = 0.0
                completed_kbar.amount = 0.0
                completed_kbar.timestamps = [self.pre_tick.tick_ut]
        else:  # 只用于最后收盘关闭最后一个kbar
            completed_kbar.label = self.labels[self.thresholds_idx]
            self.kbars.append(completed_kbar)
            self.thresholds_idx += 1

    def init(self):
        kbars = get_kbar(self.ticker_idx, self.freq, self.window_size)
        for i, kbar in enumerate(kbars):
            self.kbars.append(kbar)

    def get_kbars(self) -> np.ndarray:
        """将kbars转换为numpy数组

        Returns:
            np.ndarray: 形状为(num_kbar, 8)的数组，列顺序为[open, close, high, low, volume, amount, float_shr, adjust_factor]
        """
        print(f"{len(self.kbars)=} {self.thresholds_idx=}")
        if len(self.kbars) == 0:
            return np.empty((0, 8))

        # 将每个KBar对象转换为数组行
        arr = np.array([dataclasses.astuple(kbar)[:8] for kbar in self.kbars])
        return arr
