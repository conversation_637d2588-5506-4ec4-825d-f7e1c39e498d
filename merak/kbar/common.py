from __future__ import annotations

from dataclasses import dataclass

import numpy as np
from numpy.typing import NDArray

WINDOW_SIZE = 270  # window size for all kbars


@dataclass
class BasicKBarData:
    pre_close: NDArray[np.float64]  # 前收盘价, size: (window_size-1,)
    open: NDArray[np.float64]  # 开盘价, size: (window_size-1,)
    close: NDArray[np.float64]  # 收盘价, size: (window_size-1,)
    high: NDArray[np.float64]  # 最高价, size: (window_size-1,)
    low: NDArray[np.float64]  # 最低价, size: (window_size-1,)
    volume: NDArray[np.float64]  # 成交量, size: (window_size-1,)
    amount: NDArray[np.float64]  # 成交额, size: (window_size-1,)
    float_shr: NDArray[np.float64]  # 流通股本, size: (window_size-1,)
    adjust_factor: NDArray[np.float64]  # 复权因子, size: (window_size-1,)
