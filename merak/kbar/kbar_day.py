from __future__ import annotations

from functools import partial
from typing import Callable

import numpy as np
import wy_tdates

import merak.ops.ndarray as npop
from merak.data.eod import get_eod_data
from merak.define import Tick, TickStream
from merak.kbar.common import BasicKBarData


class KBarDay(TickStream):
    def __init__(self, ticker_id: int, trading_day: str, window_size: int = 125):
        super().__init__()
        self.ticker_id = ticker_id
        self.trading_day = trading_day
        self.window_size = window_size

        _data = self._load_basic_kbar_data(ticker_id, trading_day, window_size)
        self.tech: dict[str, np.ndarray] = {}
        self.tech["pre_close"] = np.append(_data.pre_close, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["open"] = np.append(_data.open, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["close"] = np.append(_data.close, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["high"] = np.append(_data.high, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["low"] = np.append(_data.low, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["volume"] = np.append(_data.volume, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["amount"] = np.append(_data.amount, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["float_shr"] = np.append(_data.float_shr, np.nan)  # ndarray of shape (window_size,), type float64
        self.tech["adjust_factor_org"] = np.append(
            _data.adjust_factor, np.nan
        )  # ndarray of shape (window_size,), type float64

        self.preload: dict[str, float] = {}
        prev_day = str(wy_tdates.prev_trading_day(trading_day))

        self.preload["stk_close"] = get_eod_data(  # type: ignore
            field="stk_close",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
            forward_fill=True,
        )
        self.preload["stk_float_shr"] = get_eod_data(  # type: ignore
            field="stk_float_shr",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )
        self.preload["stk_adjust_factor"] = get_eod_data(  # type: ignore
            field="stk_adjust_factor",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.cache = {
            "mul": None,
        }

        self._tick_vol_sum = 0.0
        self._tick_amount_sum = 0.0

        self.funcs = {}

    def register(self, func: Callable[[dict[str, np.ndarray]], dict[str, np.ndarray]]):
        self.funcs[func.__name__] = func

    def _load_basic_kbar_data(self, ticker_idx: int, trading_day: str, window_size: int) -> BasicKBarData:
        _get_eod_data = partial(get_eod_data, ticker_index=ticker_idx, tradingday=trading_day, ndays=window_size)

        pre_close = _get_eod_data(field="stk_pre_close")  # (window_size-1,)
        open = _get_eod_data(field="stk_open")  # (window_size-1,)
        close = _get_eod_data(field="stk_close")  # (window_size-1,)
        high = _get_eod_data(field="stk_high")  # (window_size-1,)
        low = _get_eod_data(field="stk_low")  # (window_size-1,)
        # 单位: 百股（一手)
        volume = 100 * _get_eod_data(field="stk_volume")  # (window_size-1,)
        # 单位：千元
        amount = 1000 * _get_eod_data(field="stk_amount")  # (window_size-1,)
        # 单位：万股
        float_shr = 10000 * _get_eod_data(field="stk_float_shr")  # (window_size-1,)
        adjust_factor_org = _get_eod_data(field="stk_adjust_factor")  # (window_size-1,)

        return BasicKBarData(
            pre_close=pre_close,
            open=open,
            close=close,
            high=high,
            low=low,
            volume=volume,  # type: ignore
            amount=amount,  # type: ignore
            float_shr=float_shr,  # type: ignore
            adjust_factor=adjust_factor_org,
        )

    def on_tick(self, tick: Tick):
        if self.cache["mul"] is None:
            self.cache["mul"] = self.preload["stk_close"] / tick.tick_pre_close  # type: ignore
            self.cache["float_shr_cur"] = self.cache["mul"] * self.preload["stk_float_shr"] * 10000  # type: ignore # 万股
            self.cache["adj_f_cur"] = self.cache["mul"] * self.preload["stk_adjust_factor"]  # type: ignore
        mp = round((tick.bp1 + tick.ap1) / 2, 3)

        self._tick_vol_sum += tick.tick_vol
        self._tick_amount_sum += tick.tick_tvr

        self.tech["pre_close"][-1] = tick.tick_pre_close
        self.tech["open"][-1] = tick.tick_op
        self.tech["close"][-1] = mp
        self.tech["high"][-1] = tick.tick_hp
        self.tech["low"][-1] = tick.tick_lp
        self.tech["volume"][-1] = self._tick_vol_sum
        self.tech["amount"][-1] = self._tick_amount_sum
        self.tech["float_shr"][-1] = self.cache["float_shr_cur"]  # type: ignore
        self.tech["adjust_factor_org"][-1] = self.cache["adj_f_cur"]  # type: ignore

        self._compute_derived_indicators()

        res = {}
        for func in self.funcs.values():
            res.update(func(self.tech))
        # for k, v in res.items():
        #     print(f"{k:20}: {v}")
        #     # res[k] = v[-1]
        return res

    def _compute_derived_indicators(self):
        self.tech["typ"] = npop.NanArrayMean([self.tech["close"], self.tech["high"], self.tech["low"]])
        self.tech["hlap"] = np.round(npop.NanArrayMean([self.tech["high"], self.tech["low"]]), 3)
        self.tech["float_tr"] = npop.Div(self.tech["volume"], self.tech["float_shr"])  # type: ignore
        self.tech["adjust_factor"] = npop.Div(self.tech["adjust_factor_org"], self.tech["adjust_factor_org"][-1])  # type: ignore
        self.tech["adj_op"] = self.tech["open"] * self.tech["adjust_factor"]
        self.tech["adj_cp"] = self.tech["close"] * self.tech["adjust_factor"]
        self.tech["adj_hp"] = self.tech["high"] * self.tech["adjust_factor"]
        self.tech["adj_lp"] = self.tech["low"] * self.tech["adjust_factor"]
        self.tech["adj_typ"] = self.tech["typ"] * self.tech["adjust_factor"]
        self.tech["adj_hlap"] = self.tech["hlap"] * self.tech["adjust_factor"]
        self.tech["adj_vol"] = npop.Div(self.tech["volume"], self.tech["adjust_factor"])  # type: ignore
        self.tech["adj_cp_diff"] = np.round(
            (self.tech["close"] - self.tech["pre_close"]) * self.tech["adjust_factor"], 7
        )
        self.tech["vol_cond"] = npop.NotNan(self.tech["volume"])
        self.tech["adj_cp_up"] = np.maximum(self.tech["adj_cp_diff"], 0)
        self.tech["adj_cp_down"] = np.minimum(self.tech["adj_cp_diff"], 0)
        self.tech["adj_cp_ret"] = npop.Log(npop.Div(self.tech["close"], self.tech["pre_close"]))
        self.tech["adj_op_diff"] = np.round(npop.Filter(self.tech["vol_cond"], npop.Diff(self.tech["adj_op"], 1, 0)), 7)
        self.tech["adj_hp_diff"] = np.round(npop.Filter(self.tech["vol_cond"], npop.Diff(self.tech["adj_hp"], 1, 0)), 7)
        self.tech["adj_lp_diff"] = np.round(npop.Filter(self.tech["vol_cond"], npop.Diff(self.tech["adj_lp"], 1, 0)), 7)
        self.tech["adj_typ_diff"] = np.round(
            npop.Filter(self.tech["vol_cond"], npop.Diff(self.tech["adj_typ"], 1, 0)), 7
        )
        self.tech["adj_hlap_diff"] = np.round(
            npop.Filter(self.tech["vol_cond"], npop.Diff(self.tech["adj_hlap"], 1, 0)), 7
        )
        _range = npop.Filter(self.tech["vol_cond"], self.tech["high"] - self.tech["low"])
        self.tech["range"] = np.round(_range, 7)
        self.tech["ad_w"] = npop.Div(2 * self.tech["close"] - self.tech["low"] - self.tech["high"], self.tech["range"])  # type: ignore
        self.tech["wvad_w"] = npop.Div(self.tech["close"] - self.tech["open"], self.tech["range"])  # type: ignore
        _accumulation = npop.Filter(
            self.tech["vol_cond"], self.tech["close"] - npop.FMin(self.tech["low"], self.tech["pre_close"])
        )
        self.tech["accumulation"] = np.round(_accumulation, 7)
        _distribution = npop.Filter(
            self.tech["vol_cond"], self.tech["close"] - npop.FMax(self.tech["high"], self.tech["pre_close"])
        )
        self.tech["distribution"] = np.round(_distribution, 7)
        self.tech["true_range"] = np.round(self.tech["accumulation"] - self.tech["distribution"], 7)
