from __future__ import annotations

import bottleneck as bn
import datahub_api as dh
import numpy as np

from merak.constants import DATES


# 日内特征计算所需EOD数据
def _gen_eod_data_dict_for_intra_feature() -> dict[str, np.ndarray]:
    eod_data_dict_for_intra_feature: dict[str, np.ndarray] = {
        "stk_float_shr": bn.push(
            dh.get_ashare_daily_bar(name="FLOAT_A_SHR_TODAY", last_n="full_history"),
            axis=1,
        ),
    }

    # if_trade = wydatacube.read_eod_stock('trade_status') == 1
    if_trade = dh.get_ashare_daily_bar(name="trade_status", last_n="full_history") == 1
    for field in [
        "pre_close",
        "adjust_factor",
        "open",
        "high",
        "low",
        "close",
        "volume",
        "amount",
    ]:
        # arr = wydatacube.read_eod_stock(field)
        if field == "pre_close":  # fuck datahub
            arr = dh.get_ashare_daily_bar(name="preclose", last_n="full_history")
        elif field == "adjust_factor":  # fuck again
            arr = dh.get_ashare_daily_bar(name="adj_factor", last_n="full_history")
        else:
            arr = dh.get_ashare_daily_bar(name=field, last_n="full_history")

        if field in ["pre_close", "adjust_factor"]:
            arr = bn.push(arr, axis=1)
        elif field in ["open", "high", "low"]:
            arr = np.where(if_trade, arr, eod_data_dict_for_intra_feature["stk_pre_close"])
        elif field in ["close", "volume", "amount"]:
            arr = np.where(if_trade, arr, np.nan)
        eod_data_dict_for_intra_feature[f"stk_{field}"] = arr  # type: ignore

    return eod_data_dict_for_intra_feature


eod_data_dict_for_intra_feature = _gen_eod_data_dict_for_intra_feature()


def get_eod_data(
    field: str,
    ticker_index: int,
    tradingday: str,
    ndays: int,
    forward_fill: bool = False,
) -> np.ndarray:
    """
    获取指定股票的eod（日线）数据，支持全量、区间、单天。

    参数:
        field (str): 字段名。
        ticker_index (int): 股票在eod数据中的索引。
        tradingday (str, optional): 交易日
        ndays (int): 取多少天（如取区间时用）。
        forward_fill (bool): 是否前向填充。

    返回:
        np.ndarray: 对应eod数据，若为单天则为标量。
    """
    eod_field_matrix = eod_data_dict_for_intra_feature[field]
    if eod_field_matrix.shape[0] > ticker_index:
        ticker_eod_array = bn.push(eod_field_matrix[ticker_index]) if forward_fill else eod_field_matrix[ticker_index]
    else:
        ticker_eod_array = np.full(eod_field_matrix.shape[1], np.nan)

    dateid = DATES.index(tradingday)
    if ndays == 1:
        # 单天
        return ticker_eod_array[dateid]
    else:
        # 区间
        return ticker_eod_array[dateid - ndays + 1 : dateid]
