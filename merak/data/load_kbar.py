from __future__ import annotations

from pathlib import Path

import h5py
import numpy as np
import wy_tdates

from merak.constants import DATES, TICKERS
from merak.data.eod import eod_data_dict_for_intra_feature
from merak.define import KBAR_CONFIGS, KBar
from merak.settings import MAX_KBAR_WINDOW, TRADING_DAY

__all__ = ["get_kbar", "KBAR_CACHE"]

KBAR_BASE = Path("/scratch/data/stock_files/factors_eod/hf_features/prod/resample_data")

KBAR_CACHE: dict[str, np.ndarray] = {}

# load minute kbar
for freq, kbar_config in KBAR_CONFIGS.items():
    if freq != "day":
        freq = int(freq.rstrip("s"))
        dates: list[int] = wy_tdates.prev_trading_day(TRADING_DAY, days=kbar_config.days, return_range=True)  # type: ignore
        dates = dates[:-1]  # remove today # type: ignore=
        date_ids = [DATES.index(str(date)) for date in dates]

        num_date = len(dates)
        num_ticker = len(TICKERS)
        num_kbar = len(kbar_config.thresholds)
        num_field = 8  # open, close, high, low, volume, amount, float_shr, adjust_facotr
        res = np.empty((num_ticker, num_date, num_kbar, num_field))

        for i, date in enumerate(dates):
            path = KBAR_BASE / f"{freq}" / f"{date}.h5"
            assert path.exists(), f"File {path} does not exist"
            with h5py.File(path, "r") as f:
                for j, key in enumerate(["mp_st", "mp_ed", "mp_max", "mp_min", "vol_sum", "tvr_sum"]):
                    data = np.array(f[key])  # num_ticker, num_kbar
                    min_num_ticker = min(num_ticker, data.shape[0])
                    res[:min_num_ticker, i, :, j] = data[:min_num_ticker, :]

        # load float_shr and adjust_factor
        for i, key in enumerate(["stk_float_shr", "stk_adjust_factor"]):
            data = eod_data_dict_for_intra_feature[key]  # num_ticker, num_date
            min_num_ticker = min(num_ticker, data.shape[0])
            data = data[:min_num_ticker, date_ids]  # num_ticker, num_date
            # 日内的stk_float_shr和stk_adjust_factor对于每个kbar都是一样的
            # num_ticker, num_date, 1 -> num_ticker, num_date, num_kbar
            data = data[:, :, None].repeat(num_kbar, axis=-1)
            res[:min_num_ticker, :, :, 6 + i] = data

        if freq < 3 * 60:  # 最后三分钟 集合竞价, 删除这部分kbar
            res = np.delete(res, range(-(3 * 60 // freq), -1), axis=2)

        res = res.reshape(num_ticker, -1, num_field)  # num_ticker, total_num_kbar, num_field

        KBAR_CACHE[f"{freq}s"] = res
    else:
        # load day bar
        res = np.empty((len(TICKERS), MAX_KBAR_WINDOW, 8))
        dates: list[int] = wy_tdates.prev_trading_day(TRADING_DAY, days=MAX_KBAR_WINDOW, return_range=True)  # type: ignore
        dates = dates[:-1]  # remove today # type: ignore
        date_ids = [DATES.index(str(date)) for date in dates]
        for i, key in enumerate(
            [
                "stk_open",
                "stk_close",
                "stk_high",
                "stk_low",
                "stk_volume",
                "stk_amount",
                "stk_float_shr",
                "stk_adjust_factor",
            ]
        ):
            data = eod_data_dict_for_intra_feature[key]  # num_ticker, num_date
            data = data[:, date_ids]  # num_ticker, num_date
            if key == "stk_volume":
                data *= 100  # 100 股
            elif key == "stk_amount":
                data *= 1000  # 1000 元
            elif key == "stk_float_shr":
                data *= 10000  # 10000 股
            min_num_ticker = min(len(TICKERS), data.shape[0])
            res[:min_num_ticker, :, i] = data[:min_num_ticker, :]

        KBAR_CACHE["day"] = res


def get_kbar(ticker_idx: int, freq: str, num_kbars: int) -> list[KBar]:
    ticker_kbars = KBAR_CACHE[freq][ticker_idx]  # total_num_kbar, num_field
    kbars_arr = ticker_kbars[-num_kbars:]
    res = []
    for i in range(num_kbars):
        kbar = KBar(*kbars_arr[i], timestamps=["history"], label="histroy")
        res.append(kbar)
    return res
