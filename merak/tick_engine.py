from __future__ import annotations

from typing import Any

from merak.define import FIELD_MAPPING, Tick, TickStream


class TickEngine:
    def __init__(self):
        self.tick_streams: list[TickStream] = []
        self.last_acc_volume: float | None = None
        self.last_acc_turnover: float | None = None

    def add_tick_stream(self, tick_stream: TickStream):
        """添加已实例化的计算器"""
        self.tick_streams.append(tick_stream)

    def process_tick(self, raw_data: list[Any]) -> dict[str, Any]:
        """处理单个tick数据，返回所有特征的字典"""
        # 创建原始数据字典
        original_field_names = list(FIELD_MAPPING.keys())
        raw_dict = dict(zip(original_field_names, raw_data))

        # 对AccVolume和AccTurnover做差分
        current_acc_volume = float(raw_dict["AccVolume"])
        current_acc_turnover = float(raw_dict["AccTurnover"])

        if self.last_acc_volume is not None and self.last_acc_turnover is not None:
            tick_volume = current_acc_volume - self.last_acc_volume
            tick_turnover = current_acc_turnover - self.last_acc_turnover
        else:
            tick_volume = current_acc_volume
            tick_turnover = current_acc_turnover

        # 更新历史值
        self.last_acc_volume = current_acc_volume
        self.last_acc_turnover = current_acc_turnover

        # 替换差分后的值
        raw_dict["AccVolume"] = tick_volume
        raw_dict["AccTurnover"] = tick_turnover

        # 数据清洗和格式化
        for key, value in raw_dict.items():
            if key == "UpdateTime":
                continue
            numeric_value = float(value)
            if key in ["BidWAvgPrice", "AskWAvgPrice"]:
                raw_dict[key] = round(numeric_value, 3)
            else:
                raw_dict[key] = round(numeric_value, 2)

        # 按映射顺序重新排列数据并创建NamedTuple
        processed_data = [raw_dict[field] for field in original_field_names]
        tick_data = Tick(*processed_data)

        # 调用所有注册的计算器
        features = {}
        for tick_stream in self.tick_streams:
            features.update(tick_stream.on_tick(tick_data))

        return features

    def reset_state(self):
        """重置引擎状态（用于新的数据流开始）"""
        self.last_acc_volume = None
        self.last_acc_turnover = None
