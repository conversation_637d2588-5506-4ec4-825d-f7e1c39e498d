from __future__ import annotations

from merak.define import KBar, KBarStream
from merak.kbar.window import MoveMean


class BOP10(KBarStream):
    def __init__(self):
        super().__init__()
        self.move_mean_wvad_w_10 = MoveMean(window_size=10)

    def on_kbar(self, kbar: KBar) -> dict[str, float]:
        if kbar.volume == 0:
            wvad_w = float("nan")
        else:
            range = kbar.high - kbar.low
            if range == 0:
                wvad_w = float("nan")
            else:
                wvad_w = (kbar.close - kbar.open) / range

        bop10 = self.move_mean_wvad_w_10.update(wvad_w)

        return {
            "tech_v2_bop10": bop10,
        }

    def write_back(self):
        self.move_mean_wvad_w_10.write_back()
