from __future__ import annotations

import math

import wy_tdates

from merak.data.eod import get_eod_data
from merak.define import Tick, TickStream
from merak.ops.window import (
    SlidingWindowCorr,
    SlidingWindowMean,
    SlidingWindowMetrics,
    SlidingWindowMinMax,
    SlidingWindowReturnMetricsV2,
    SlidingWindowSkewKurt,
    SlidingWindowStdCustom,
    SlidingWindowSum,
    SlidingWindowTrend,
)


class TickMPBaseV2(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        super().__init__()
        self.ticker_id = ticker_id

        self.preload = {}
        prev_day = str(wy_tdates.prev_trading_day(trading_day))

        self.preload["stk_close"] = get_eod_data(
            field="stk_close",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
            forward_fill=True,
        )

        self.preload["stk_high"] = get_eod_data(
            field="stk_high",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.preload["stk_low"] = get_eod_data(
            field="stk_low",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.cache = {
            "mul": None,
            "adj_pre_hp": None,
            "adj_pre_lp": None,
            "pre_mp": None,
        }

        # 基础窗口算子
        window_size = 400
        self.mp_mean = SlidingWindowMean(window_size=window_size)
        self.mp_minmax = SlidingWindowMinMax(window_size=window_size)
        self.mp_ed2mp_st_metrics = SlidingWindowMetrics(window_size=window_size)
        self.mp_range_metrics = SlidingWindowMetrics(window_size=window_size)
        self.mp_arpp_metrics = SlidingWindowMetrics(window_size=window_size)
        self.mp_ret_metrics_v2 = SlidingWindowReturnMetricsV2(window_size=window_size)
        self.mp_diff_trend = SlidingWindowTrend(window_size=window_size)
        self.pvt = SlidingWindowSum(window_size=window_size)
        self.mp_vol_corr = SlidingWindowCorr(window_size=window_size)

        # 新增的流式算子
        self.mp_ret_std = SlidingWindowStdCustom(window_size=window_size, min_count=5)
        self.mp_ret_upstd = SlidingWindowStdCustom(window_size=window_size, min_count=5, mode=1)
        self.mp_ret_skew_kurt = SlidingWindowSkewKurt(window_size=window_size, min_count=5)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        if self.cache["mul"] is None:
            self.cache["mul"] = self.preload["stk_close"] / tick.tick_pre_close
            self.cache["adj_pre_hp"] = self.preload["stk_high"] * self.cache["mul"]
            self.cache["adj_pre_lp"] = self.preload["stk_low"] * self.cache["mul"]
            self.cache["pre_mp"] = tick.tick_pre_close  # type: ignore

        mp = round((tick.bp1 + tick.ap1) / 2, 3)

        # 计算基础价格指标
        mp_twap = self.mp_mean.on_tick(mp)
        mp_max, mp_min, mp_st = self.mp_minmax.on_tick(mp)

        mp_ed2mp_st = math.log(mp / mp_st)
        mp_range = (mp - mp_min) / (mp_max - mp_min) if mp_max != mp_min else float("nan")
        mp_arpp = (mp_twap - mp_min) / (mp_max - mp_min) if mp_max != mp_min else float("nan")

        _, _, mp_ed2mp_st_navg = self.mp_ed2mp_st_metrics.on_tick(mp_ed2mp_st)
        _, _, mp_range_navg = self.mp_range_metrics.on_tick(mp_range)
        _, _, mp_arpp_navg = self.mp_arpp_metrics.on_tick(mp_arpp)

        mp_ed2pre_cp = math.log(mp / tick.tick_pre_close)
        mp_ed2pre_hp = math.log(mp / self.cache["adj_pre_hp"])  # type: ignore
        mp_ed2pre_lp = math.log(mp / self.cache["adj_pre_lp"])  # type: ignore

        mp_diff = mp - self.cache["pre_mp"]  # type: ignore
        mp_ret = math.log(mp / self.cache["pre_mp"])  # type: ignore
        self.cache["pre_mp"] = mp  # type: ignore

        mp_trendstr = self.mp_diff_trend.on_tick(mp_diff)

        mp_ret_ud, mp_ret_upcnt_pct = self.mp_ret_metrics_v2.on_tick(mp_ret)

        pvt = self.pvt.on_tick(tick.tick_vol * mp_ret)
        mp_pvt_corr = self.mp_vol_corr.on_tick(mp, pvt)

        # 新增的流式指标计算
        _mp_ret_std = self.mp_ret_std.on_tick(mp_ret)
        _mp_ret_upstd = self.mp_ret_upstd.on_tick(mp_ret)
        mp_ret_skew, mp_ret_kurt = self.mp_ret_skew_kurt.on_tick(mp_ret)

        # 计算 mp_ret_upstd_pct
        mp_ret_upstd_pct = (
            _mp_ret_upstd / _mp_ret_std if not math.isnan(_mp_ret_std) and _mp_ret_std != 0 else float("nan")
        )

        return {
            "mp_ed2mp_st": mp_ed2mp_st,
            "mp_ed2mp_st_navg": mp_ed2mp_st_navg,
            "mp_range": mp_range,
            "mp_range_navg": mp_range_navg,
            "mp_arpp": mp_arpp,
            "mp_arpp_navg": mp_arpp_navg,
            "mp_ed2pre_cp": mp_ed2pre_cp,
            "mp_ed2pre_hp": mp_ed2pre_hp,
            "mp_ed2pre_lp": mp_ed2pre_lp,
            "mp_ret_ud": mp_ret_ud,
            "mp_ret_upcnt_pct": mp_ret_upcnt_pct,
            "mp_trendstr": mp_trendstr,
            "mp_ret_upstd_pct": mp_ret_upstd_pct,
            "mp_ret_skew": mp_ret_skew,
            "mp_ret_kurt": mp_ret_kurt,
            "mp_pvt_corr": mp_pvt_corr,
        }
