from __future__ import annotations

import math

from merak.define import Tick, TickStream
from merak.ops.window import SlidingWindowMetrics


class TickVOI(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        super().__init__()
        self.ticker_id = ticker_id

        # 缓存前一个tick的数据，用于计算差分
        self.cache: dict[str, float | None] = {
            "prev_bp1": None,
            "prev_ap1": None,
            "prev_bv1": None,
            "prev_av1": None,
        }

        # 滑动窗口用于计算统计指标
        max_window_size = 400  # 400 ticks = 400 x 3s = 20min
        self.voi_metrics = SlidingWindowMetrics(window_size=max_window_size)
        self.voi2vol_metrics = SlidingWindowMetrics(window_size=max_window_size)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        # 初始化时，设置前一个tick的值
        if self.cache["prev_bp1"] is None:
            self.cache["prev_bp1"] = tick.bp1
            self.cache["prev_ap1"] = tick.ap1
            self.cache["prev_bv1"] = tick.bv1
            self.cache["prev_av1"] = tick.av1

            # 第一个tick时，差分为0，VOI也为0
            voi = float("nan")
            voi2vol = float("nan")
        else:
            # 计算价格和成交量的差分
            bp1_diff = tick.bp1 - self.cache["prev_bp1"]  # type: ignore
            ap1_diff = tick.ap1 - self.cache["prev_ap1"]  # type: ignore
            bv1_diff = tick.bv1 - self.cache["prev_bv1"]  # type: ignore
            av1_diff = tick.av1 - self.cache["prev_av1"]  # type: ignore

            # 计算 v_bid
            if math.isnan(bp1_diff):
                v_bid = float("nan")
            elif bp1_diff == 0:
                v_bid = bv1_diff
            elif bp1_diff > 0:
                v_bid = math.sqrt(tick.bv1)
            else:
                v_bid = 0.0

            # 计算 v_ask
            if math.isnan(ap1_diff):
                v_ask = float("nan")
            elif ap1_diff == 0:
                v_ask = av1_diff
            elif ap1_diff < 0:
                v_ask = math.sqrt(tick.av1)
            else:
                v_ask = 0.0

            # 应用符号平方根变换
            if not math.isnan(v_bid):
                v_bid = math.sqrt(abs(v_bid)) * (1 if v_bid >= 0 else -1)
            if not math.isnan(v_ask):
                v_ask = math.sqrt(abs(v_ask)) * (1 if v_ask >= 0 else -1)

            # 计算VOI (Volume Order Imbalance)
            if math.isnan(v_bid) or math.isnan(v_ask):
                voi = float("nan")
            else:
                voi = v_bid - v_ask

            # 计算VOI相对于成交量的比例
            if math.isnan(voi) or tick.tick_vol == 0:
                voi2vol = float("nan")
            else:
                voi2vol = voi / tick.tick_vol

        # 更新缓存
        self.cache["prev_bp1"] = tick.bp1
        self.cache["prev_ap1"] = tick.ap1
        self.cache["prev_bv1"] = tick.bv1
        self.cache["prev_av1"] = tick.av1

        # 使用滑动窗口计算统计指标
        if math.isnan(voi):
            _, _, voi_navg = float("nan"), float("nan"), float("nan")
        else:
            _, _, voi_navg = self.voi_metrics.on_tick(voi)

        if math.isnan(voi2vol):
            _, _, voi2vol_navg = float("nan"), float("nan"), float("nan")
        else:
            _, _, voi2vol_navg = self.voi2vol_metrics.on_tick(voi2vol)

        return {
            "voi_ed": voi,
            "voi_navg": voi_navg,
            "voi2vol_ed": voi2vol,
            "voi2vol_navg": voi2vol_navg,
        }
