from __future__ import annotations

from merak.define import Tick, TickStream
from merak.ops.window import SlidingWindowMetrics, SlidingWindowSum


class TickVolPct(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        super().__init__()
        self.ticker_id = ticker_id

        # 滑动窗口用于计算成交量总和
        max_window_size = 400  # 400 ticks = 400 x 3s = 20min
        self.tick_vol_sum = SlidingWindowSum(window_size=max_window_size)
        self.vol_pct_metrics = SlidingWindowMetrics(window_size=max_window_size)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        # 获取滑动窗口内的成交量总和
        total_vol = self.tick_vol_sum.on_tick(tick.tick_vol)

        # 计算成交量百分比
        if total_vol == 0:
            vol_pct = float("nan")
        else:
            vol_pct = tick.tick_vol / total_vol

        # 使用滑动窗口计算统计指标
        _, _, vol_pct_navg = self.vol_pct_metrics.on_tick(vol_pct if vol_pct == vol_pct else 0.0)  # NaN check

        return {
            "vol_pct_ed": vol_pct,
            "vol_pct_navg": vol_pct_navg,
        }
