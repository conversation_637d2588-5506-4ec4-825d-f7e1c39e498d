from __future__ import annotations

from merak.define import Tick, TickStream
from merak.ops.window import SlidingWindowMetrics


class TickBALvnPct(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        super().__init__()
        self.ticker_id = ticker_id

        # 为不同深度（5档、10档）创建滑动窗口
        max_window_size = 400  # 400 ticks = 400 x 3s = 20min

        # 买方5档和10档的滑动窗口
        self.bid_lv5_pct_metrics = SlidingWindowMetrics(window_size=max_window_size)
        self.bid_lv10_pct_metrics = SlidingWindowMetrics(window_size=max_window_size)

        # 卖方5档和10档的滑动窗口
        self.ask_lv5_pct_metrics = SlidingWindowMetrics(window_size=max_window_size)
        self.ask_lv10_pct_metrics = SlidingWindowMetrics(window_size=max_window_size)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        # 计算买方5档成交量占比
        bid_lv5_vol = tick.bv1 + tick.bv2 + tick.bv3 + tick.bv4 + tick.bv5
        if tick.bv_tot == 0:
            bid_lv5_pct = float("nan")
        else:
            bid_lv5_pct = bid_lv5_vol / tick.bv_tot

        # 计算买方10档成交量占比
        bid_lv10_vol = bid_lv5_vol + tick.bv6 + tick.bv7 + tick.bv8 + tick.bv9 + tick.bv10
        if tick.bv_tot == 0:
            bid_lv10_pct = float("nan")
        else:
            bid_lv10_pct = bid_lv10_vol / tick.bv_tot

        # 计算卖方5档成交量占比
        ask_lv5_vol = tick.av1 + tick.av2 + tick.av3 + tick.av4 + tick.av5
        if tick.av_tot == 0:
            ask_lv5_pct = float("nan")
        else:
            ask_lv5_pct = ask_lv5_vol / tick.av_tot

        # 计算卖方10档成交量占比
        ask_lv10_vol = ask_lv5_vol + tick.av6 + tick.av7 + tick.av8 + tick.av9 + tick.av10
        if tick.av_tot == 0:
            ask_lv10_pct = float("nan")
        else:
            ask_lv10_pct = ask_lv10_vol / tick.av_tot

        # 使用滑动窗口计算统计指标
        _, _, bid_lv5_pct_navg = self.bid_lv5_pct_metrics.on_tick(
            bid_lv5_pct if bid_lv5_pct == bid_lv5_pct else 0.0  # NaN check
        )
        _, _, bid_lv10_pct_navg = self.bid_lv10_pct_metrics.on_tick(
            bid_lv10_pct if bid_lv10_pct == bid_lv10_pct else 0.0  # NaN check
        )
        _, _, ask_lv5_pct_navg = self.ask_lv5_pct_metrics.on_tick(
            ask_lv5_pct if ask_lv5_pct == ask_lv5_pct else 0.0  # NaN check
        )
        _, _, ask_lv10_pct_navg = self.ask_lv10_pct_metrics.on_tick(
            ask_lv10_pct if ask_lv10_pct == ask_lv10_pct else 0.0  # NaN check
        )

        return {
            "bid_lv5_pct_ed": bid_lv5_pct,
            "bid_lv5_pct_navg": bid_lv5_pct_navg,
            "bid_lv10_pct_ed": bid_lv10_pct,
            "bid_lv10_pct_navg": bid_lv10_pct_navg,
            "ask_lv5_pct_ed": ask_lv5_pct,
            "ask_lv5_pct_navg": ask_lv5_pct_navg,
            "ask_lv10_pct_ed": ask_lv10_pct,
            "ask_lv10_pct_navg": ask_lv10_pct_navg,
        }
