from __future__ import annotations

import wy_tdates

from merak.data.eod import get_eod_data
from merak.define import Tick, TickStream
from merak.ops.window import (
    SlidingWindowCorr,
    SlidingWindowMean,
    SlidingWindowMetrics,
    SlidingWindowReturnMetrics,
    SlidingWindowStdCustom,
    SlidingWindowSum,
    SlidingWindowTrend,
)


class TickMPBaseV1(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        self.ticker_id = ticker_id

        self.preload = {}
        prev_day = str(wy_tdates.prev_trading_day(trading_day))

        self.preload["stk_close"] = get_eod_data(
            field="stk_close",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
            forward_fill=True,
        )

        self.preload["stk_high"] = get_eod_data(
            field="stk_high",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.preload["stk_low"] = get_eod_data(
            field="stk_low",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.cache = {
            "mul": None,
            "adj_pre_hp": None,
            "adj_pre_lp": None,
            "pre_mp": None,
        }

        windown_size = 400  # 400 ticks = 400 x 3s = 20min
        self.tick_tvr_sum = SlidingWindowSum(window_size=windown_size)
        self.tick_vol_sum = SlidingWindowSum(window_size=windown_size)
        self.mp_sum = SlidingWindowSum(window_size=windown_size)
        self.twap_mean = SlidingWindowMean(window_size=windown_size)

        self.mp2pre_cp_metrics = SlidingWindowMetrics(window_size=windown_size)
        self.mp2op_metrics = SlidingWindowMetrics(window_size=windown_size)
        self.mp2vwap_metrics = SlidingWindowMetrics(window_size=windown_size)
        self.mp2twap_metrics = SlidingWindowMetrics(window_size=windown_size)
        self.avg_p2mp_lag_metrics = SlidingWindowMetrics(window_size=windown_size)

        self.mp_return_metrics = SlidingWindowReturnMetrics(window_size=windown_size)
        self.mp_diff_trend = SlidingWindowTrend(window_size=windown_size)
        self.mp_vol_corr = SlidingWindowCorr(window_size=windown_size, min_count=5)
        self.mp_ret_std = SlidingWindowStdCustom(window_size=windown_size, min_count=5)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        if self.cache["mul"] is None:
            self.cache["mul"] = self.preload["stk_close"] / tick.tick_pre_close
            self.cache["adj_pre_hp"] = self.preload["stk_high"] * self.cache["mul"]
            self.cache["adj_pre_lp"] = self.preload["stk_low"] * self.cache["mul"]

        mp = round((tick.bp1 + tick.ap1) / 2, 3)

        mp2pre_cp = mp / tick.tick_pre_close - 1
        mp2op = mp / tick.tick_op - 1

        window_sum_tick_tvr = self.tick_tvr_sum.on_tick(tick.tick_tvr)
        window_sum_tick_vol = self.tick_vol_sum.on_tick(tick.tick_vol)

        _vwap = window_sum_tick_tvr / window_sum_tick_vol
        mp2vwap = mp / _vwap - 1

        _twap = self.twap_mean.on_tick(mp)
        mp2twap = mp / _twap - 1

        _avg_p = tick.tick_tvr / tick.tick_vol
        if self.cache["pre_mp"] is None:
            avg_p2mp_lag = 0.0
        else:
            avg_p2mp_lag = _avg_p / self.cache["pre_mp"] - 1

        # TODO: 多算了前两个指标，可以优化
        _, _, mp2pre_cp_navg = self.mp2pre_cp_metrics.on_tick(mp2pre_cp)
        _, _, mp2op_navg = self.mp2op_metrics.on_tick(mp2op)
        _, _, mp2vwap_navg = self.mp2vwap_metrics.on_tick(mp2vwap)
        _, _, mp2twap_navg = self.mp2twap_metrics.on_tick(mp2twap)
        _, _, avg_p2mp_lag_navg = self.avg_p2mp_lag_metrics.on_tick(avg_p2mp_lag)

        mp2pre_hp_ed = mp / self.cache["adj_pre_hp"] - 1  # type: ignore
        mp2pre_lp_ed = mp / self.cache["adj_pre_lp"] - 1  # type: ignore
        mp2hp_ed = mp / tick.tick_hp - 1
        mp2lp_ed = mp / tick.tick_lp - 1

        if self.cache["pre_mp"] is None:
            mp_diff = float("nan")
        else:
            mp_diff = mp - self.cache["pre_mp"]
        mp_trend = self.mp_diff_trend.on_tick(mp_diff)

        if self.cache["pre_mp"] is None:
            mp_ret = float("nan")
        else:
            mp_ret = mp / self.cache["pre_mp"] - 1  # type: ignore
        self.cache["pre_mp"] = mp  # type: ignore

        mp_up_ret, mp_down_ret, mp_up_ret_ptk, mp_down_ret_ptk, mp_ud_ret_ptk = self.mp_return_metrics.on_tick(mp_ret)
        mp_ret_std = self.mp_ret_std.on_tick(mp_ret)
        mp_vol_corr = self.mp_vol_corr.on_tick(mp, tick.tick_vol)

        return {
            "mp2pre_cp_ed": mp2pre_cp,
            "mp2pre_cp_navg": mp2pre_cp_navg,
            "mp2op_ed": mp2op,
            "mp2op_navg": mp2op_navg,
            "mp2vwap_ed": mp2vwap,
            "mp2vwap_navg": mp2vwap_navg,
            "mp2twap_ed": mp2twap,
            "mp2twap_navg": mp2twap_navg,
            "avg_p2mp_lag_ed": avg_p2mp_lag,
            "avg_p2mp_lag_navg": avg_p2mp_lag_navg,
            "mp2pre_hp_ed": mp2pre_hp_ed,
            "mp2pre_lp_ed": mp2pre_lp_ed,
            "mp2hp_ed": mp2hp_ed,
            "mp2lp_ed": mp2lp_ed,
            "mp_up_ret": mp_up_ret,
            "mp_down_ret": mp_down_ret,
            "mp_up_ret_ptk": mp_up_ret_ptk,
            "mp_down_ret_ptk": mp_down_ret_ptk,
            "mp_trend": mp_trend,
            "mp_ud_ret_ptk": mp_ud_ret_ptk,
            "mp_ret_std": mp_ret_std,
            "mp_vol_corr": mp_vol_corr,
        }
