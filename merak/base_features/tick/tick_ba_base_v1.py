from __future__ import annotations

import wy_tdates

from merak.data.eod import get_eod_data
from merak.define import Tick, TickStream
from merak.ops.window import SlidingWindowMetrics


class TickBABaseV1(TickStream):
    def __init__(self, ticker_id: int, trading_day: str):
        super().__init__()
        self.ticker_id = ticker_id

        self.preload = {}
        prev_day = str(wy_tdates.prev_trading_day(trading_day))

        self.preload["stk_float_shr"] = get_eod_data(
            field="stk_float_shr",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
        )

        self.preload["stk_close"] = get_eod_data(
            field="stk_close",
            ticker_index=self.ticker_id,
            tradingday=prev_day,
            ndays=1,
            forward_fill=True,
        )

        self.cache = {
            "mul": None,
        }

        max_windown_size = 400  # 400 ticks = 400 x 3s = 20min
        self.swm_bid_tvr = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ask_tvr = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_bid_tr = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ask_tr = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ba_tvr_diff = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ba_tr_diff = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ba_tvr_imba = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ba_wp_imba = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_bid_wp_imba = SlidingWindowMetrics(window_size=max_windown_size)
        self.swm_ask_wp_imba = SlidingWindowMetrics(window_size=max_windown_size)

    def on_tick(self, tick: Tick) -> dict[str, float]:
        if self.cache["mul"] is None:
            self.cache["mul"] = self.preload["stk_close"] / tick.tick_pre_close
            self.cache["float_shr_cur"] = self.cache["mul"] * self.preload["stk_float_shr"] * 10000  # 万股

        mp = round((tick.bp1 + tick.ap1) / 2, 3)
        bid_tvr = tick.bv_tot * tick.bvwp_tot
        ask_tvr = tick.av_tot * tick.avwp_tot
        bid_tr = tick.bv_tot / self.cache["float_shr_cur"]  # type: ignore
        ask_tr = tick.av_tot / self.cache["float_shr_cur"]  # type: ignore
        bid_wp_imba = (tick.bvwp_tot - mp) / (tick.bvwp_tot + mp)
        ask_wp_imba = (tick.avwp_tot - mp) / (tick.avwp_tot + mp)

        ba_tvr_diff = bid_tvr - ask_tvr
        ba_tr_diff = bid_tr - ask_tr
        ba_tvr_imba = ba_tvr_diff / (bid_tvr + ask_tvr)
        ba_wp_imba = (tick.bvwp_tot - tick.avwp_tot) / (tick.bvwp_tot + tick.avwp_tot)

        bid_tvr_avg, bid_tvr_pmr, bid_tvr_navg = self.swm_bid_tvr.on_tick(bid_tvr)
        ask_tvr_avg, ask_tvr_pmr, ask_tvr_navg = self.swm_ask_tvr.on_tick(ask_tvr)
        bid_tr_avg, bid_tr_pmr, bid_tr_navg = self.swm_bid_tr.on_tick(bid_tr)
        ask_tr_avg, ask_tr_pmr, ask_tr_navg = self.swm_ask_tr.on_tick(ask_tr)
        ba_tvr_diff_avg, ba_tvr_diff_pmr, ba_tvr_diff_navg = self.swm_ba_tvr_diff.on_tick(ba_tvr_diff)
        ba_tr_diff_avg, ba_tr_diff_pmr, ba_tr_diff_navg = self.swm_ba_tr_diff.on_tick(ba_tr_diff)
        ba_tvr_imba_avg, ba_tvr_imba_pmr, ba_tvr_imba_navg = self.swm_ba_tvr_imba.on_tick(ba_tvr_imba)
        ba_wp_imba_avg, ba_wp_imba_pmr, ba_wp_imba_navg = self.swm_ba_wp_imba.on_tick(ba_wp_imba)
        bid_wp_imba_avg, bid_wp_imba_pmr, bid_wp_imba_navg = self.swm_bid_wp_imba.on_tick(bid_wp_imba)
        ask_wp_imba_avg, ask_wp_imba_pmr, ask_wp_imba_navg = self.swm_ask_wp_imba.on_tick(ask_wp_imba)

        return {
            "bid_tvr_ed": bid_tvr,
            "bid_tvr_avg": bid_tvr_avg,
            "bid_tvr_pmr": bid_tvr_pmr,
            "bid_tvr_navg": bid_tvr_navg,
            "ask_tvr_ed": ask_tvr,
            "ask_tvr_avg": ask_tvr_avg,
            "ask_tvr_pmr": ask_tvr_pmr,
            "ask_tvr_navg": ask_tvr_navg,
            "bid_tr_ed": bid_tr,
            "bid_tr_avg": bid_tr_avg,
            "bid_tr_pmr": bid_tr_pmr,
            "bid_tr_navg": bid_tr_navg,
            "ask_tr_ed": ask_tr,
            "ask_tr_avg": ask_tr_avg,
            "ask_tr_pmr": ask_tr_pmr,
            "ask_tr_navg": ask_tr_navg,
            "ba_tvr_diff_ed": ba_tvr_diff,
            "ba_tvr_diff_avg": ba_tvr_diff_avg,
            "ba_tvr_diff_pmr": ba_tvr_diff_pmr,
            "ba_tvr_diff_navg": ba_tvr_diff_navg,
            "ba_tr_diff_ed": ba_tr_diff,
            "ba_tr_diff_avg": ba_tr_diff_avg,
            "ba_tr_diff_pmr": ba_tr_diff_pmr,
            "ba_tr_diff_navg": ba_tr_diff_navg,
            "ba_tvr_imba_ed": ba_tvr_imba,
            "ba_tvr_imba_avg": ba_tvr_imba_avg,
            "ba_tvr_imba_pmr": ba_tvr_imba_pmr,
            "ba_tvr_imba_navg": ba_tvr_imba_navg,
            "ba_wp_imba_ed": ba_wp_imba,
            "ba_wp_imba_avg": ba_wp_imba_avg,
            "ba_wp_imba_pmr": ba_wp_imba_pmr,
            "ba_wp_imba_navg": ba_wp_imba_navg,
            "bid_wp_imba_ed": bid_wp_imba,
            "bid_wp_imba_avg": bid_wp_imba_avg,
            "bid_wp_imba_pmr": bid_wp_imba_pmr,
            "bid_wp_imba_navg": bid_wp_imba_navg,
            "ask_wp_imba_ed": ask_wp_imba,
            "ask_wp_imba_avg": ask_wp_imba_avg,
            "ask_wp_imba_pmr": ask_wp_imba_pmr,
            "ask_wp_imba_navg": ask_wp_imba_navg,
        }
