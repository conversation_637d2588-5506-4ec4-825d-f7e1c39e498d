"""
数据结构定义模块
包含字段映射和DataClass类型定义
"""

from collections import OrderedDict
from dataclasses import dataclass

TICK_FIELDS = [
    "UpdateTime",
    "BidPrice1",
    "BidPrice2",
    "BidPrice3",
    "BidPrice4",
    "BidPrice5",
    "BidPrice6",
    "BidPrice7",
    "BidPrice8",
    "BidPrice9",
    "BidPrice10",
    "BidVol1",
    "BidVol2",
    "BidVol3",
    "BidVol4",
    "BidVol5",
    "BidVol6",
    "BidVol7",
    "BidVol8",
    "BidVol9",
    "BidVol10",
    "AskPrice1",
    "AskPrice2",
    "AskPrice3",
    "AskPrice4",
    "AskPrice5",
    "AskPrice6",
    "AskPrice7",
    "AskPrice8",
    "AskPrice9",
    "AskPrice10",
    "AskVol1",
    "AskVol2",
    "AskVol3",
    "AskVol4",
    "AskVol5",
    "AskVol6",
    "AskVol7",
    "AskVol8",
    "AskVol9",
    "AskVol10",
    "PreClose",
    "OpenPrice",
    "HighPrice",
    "LowPrice",
    "AccVolume",
    "AccTurnover",
    "TotalBidVol",
    "TotalAskVol",
    "BidWAvgPrice",
    "AskWAvgPrice",
    "UpLimitPrice",
    "DownLimitPrice",
]

# 字段映射：原始字段名 -> 处理后字段名（保持顺序）
FIELD_MAPPING = OrderedDict(
    [
        ("UpdateTime", "tick_ut"),  # 交易所 在snap行情里打的时间戳，格式化为了 HHMMSS 字符串
        ("BidPrice1", "bp1"),  # 买一价
        ("BidPrice2", "bp2"),
        ("BidPrice3", "bp3"),
        ("BidPrice4", "bp4"),
        ("BidPrice5", "bp5"),
        ("BidPrice6", "bp6"),
        ("BidPrice7", "bp7"),
        ("BidPrice8", "bp8"),
        ("BidPrice9", "bp9"),
        ("BidPrice10", "bp10"),
        ("BidVol1", "bv1"),  # 买一量
        ("BidVol2", "bv2"),
        ("BidVol3", "bv3"),
        ("BidVol4", "bv4"),
        ("BidVol5", "bv5"),
        ("BidVol6", "bv6"),
        ("BidVol7", "bv7"),
        ("BidVol8", "bv8"),
        ("BidVol9", "bv9"),
        ("BidVol10", "bv10"),
        ("AskPrice1", "ap1"),  # 卖一价
        ("AskPrice2", "ap2"),
        ("AskPrice3", "ap3"),
        ("AskPrice4", "ap4"),
        ("AskPrice5", "ap5"),
        ("AskPrice6", "ap6"),
        ("AskPrice7", "ap7"),
        ("AskPrice8", "ap8"),
        ("AskPrice9", "ap9"),
        ("AskPrice10", "ap10"),
        ("AskVol1", "av1"),  # 卖一量
        ("AskVol2", "av2"),
        ("AskVol3", "av3"),
        ("AskVol4", "av4"),
        ("AskVol5", "av5"),
        ("AskVol6", "av6"),
        ("AskVol7", "av7"),
        ("AskVol8", "av8"),
        ("AskVol9", "av9"),
        ("AskVol10", "av10"),
        ("PreClose", "tick_pre_close"),  # 昨收价
        ("OpenPrice", "tick_op"),  # 开盘价
        ("HighPrice", "tick_hp"),  # 累计最高价
        ("LowPrice", "tick_lp"),  # 累计最低价
        ("AccVolume", "tick_vol"),  # 累计成交量的差分：当前snap成交量
        ("AccTurnover", "tick_tvr"),  # 累计成交额的差分: 当前snap成交额
        ("TotalBidVol", "bv_tot"),  # 全市场总委买量
        ("TotalAskVol", "av_tot"),  # 全市场总委卖量
        ("BidWAvgPrice", "bvwp_tot"),  # 委买成交均价 （全市场委买额/量）
        ("AskWAvgPrice", "avwp_tot"),  # 委卖成交均价 （全市场委卖额/量）
        ("UpLimitPrice", "up_limit_p"),  # 涨停价
        ("DownLimitPrice", "down_limit_p"),  # 跌停价
    ]
)

# 从映射中获取处理后的字段名
PROCESSED_FIELD_NAMES = list(FIELD_MAPPING.values())


@dataclass(frozen=True)
class Tick:
    """
    Tick数据结构
    """

    # === 时间信息 ===
    tick_ut: str
    """交易所时间戳，格式化为HHMMSS，3s一个"""

    # === 买盘价格（10档深度）===
    bp1: float
    """买一价，来源于BidPrice1字段，当前最优买入价格"""
    bp2: float
    """买二价，来源于BidPrice2字段，第二优先买入价格"""
    bp3: float
    """买三价，来源于BidPrice3字段，第三优先买入价格"""
    bp4: float
    """买四价，来源于BidPrice4字段，第四优先买入价格"""
    bp5: float
    """买五价，来源于BidPrice5字段，第五优先买入价格"""
    bp6: float
    """买六价，来源于BidPrice6字段，第六优先买入价格"""
    bp7: float
    """买七价，来源于BidPrice7字段，第七优先买入价格"""
    bp8: float
    """买八价，来源于BidPrice8字段，第八优先买入价格"""
    bp9: float
    """买九价，来源于BidPrice9字段，第九优先买入价格"""
    bp10: float
    """买十价，来源于BidPrice10字段，第十优先买入价格"""

    # === 买盘量（10档深度）===
    bv1: float
    """买一量，来源于BidVol1字段，买一价对应的委托数量"""
    bv2: float
    """买二量，来源于BidVol2字段，买二价对应的委托数量"""
    bv3: float
    """买三量，来源于BidVol3字段，买三价对应的委托数量"""
    bv4: float
    """买四量，来源于BidVol4字段，买四价对应的委托数量"""
    bv5: float
    """买五量，来源于BidVol5字段，买五价对应的委托数量"""
    bv6: float
    """买六量，来源于BidVol6字段，买六价对应的委托数量"""
    bv7: float
    """买七量，来源于BidVol7字段，买七价对应的委托数量"""
    bv8: float
    """买八量，来源于BidVol8字段，买八价对应的委托数量"""
    bv9: float
    """买九量，来源于BidVol9字段，买九价对应的委托数量"""
    bv10: float
    """买十量，来源于BidVol10字段，买十价对应的委托数量"""

    # === 卖盘价格（10档深度）===
    ap1: float
    """卖一价，来源于AskPrice1字段，当前最优卖出价格"""
    ap2: float
    """卖二价，来源于AskPrice2字段，第二优先卖出价格"""
    ap3: float
    """卖三价，来源于AskPrice3字段，第三优先卖出价格"""
    ap4: float
    """卖四价，来源于AskPrice4字段，第四优先卖出价格"""
    ap5: float
    """卖五价，来源于AskPrice5字段，第五优先卖出价格"""
    ap6: float
    """卖六价，来源于AskPrice6字段，第六优先卖出价格"""
    ap7: float
    """卖七价，来源于AskPrice7字段，第七优先卖出价格"""
    ap8: float
    """卖八价，来源于AskPrice8字段，第八优先卖出价格"""
    ap9: float
    """卖九价，来源于AskPrice9字段，第九优先卖出价格"""
    ap10: float
    """卖十价，来源于AskPrice10字段，第十优先卖出价格"""

    # === 卖盘量（10档深度）===
    av1: float
    """卖一量，来源于AskVol1字段，卖一价对应的委托数量"""
    av2: float
    """卖二量，来源于AskVol2字段，卖二价对应的委托数量"""
    av3: float
    """卖三量，来源于AskVol3字段，卖三价对应的委托数量"""
    av4: float
    """卖四量，来源于AskVol4字段，卖四价对应的委托数量"""
    av5: float
    """卖五量，来源于AskVol5字段，卖五价对应的委托数量"""
    av6: float
    """卖六量，来源于AskVol6字段，卖六价对应的委托数量"""
    av7: float
    """卖七量，来源于AskVol7字段，卖七价对应的委托数量"""
    av8: float
    """卖八量，来源于AskVol8字段，卖八价对应的委托数量"""
    av9: float
    """卖九量，来源于AskVol9字段，卖九价对应的委托数量"""
    av10: float
    """卖十量，来源于AskVol10字段，卖十价对应的委托数量"""

    # === 基础价格信息 ===
    tick_pre_close: float
    """昨收价，来源于PreClose字段，上一交易日的收盘价"""
    tick_op: float
    """开盘价，来源于OpenPrice字段，当日第一笔交易价格"""
    tick_hp: float
    """累计最高价，来源于HighPrice字段，当日至目前的最高成交价"""
    tick_lp: float
    """累计最低价，来源于LowPrice字段，当日至目前的最低成交价"""

    # === 成交信息（已差分处理）===
    tick_vol: float
    """成交量增量，原AccVolume字段的差分值，表示当前tick的实际成交量"""
    tick_tvr: float
    """成交额增量，原AccTurnover字段的差分值，表示当前tick的实际成交额"""

    # === 市场总量信息 ===
    bv_tot: float
    """全市场总委买量，来源于TotalBidVol字段，包含所有价位的买单总量"""
    av_tot: float
    """全市场总委卖量，来源于TotalAskVol字段，包含所有价位的卖单总量"""
    bvwp_tot: float
    """委买加权均价，来源于BidWAvgPrice字段，计算公式为全市场委买额/委买量"""
    avwp_tot: float
    """委卖加权均价，来源于AskWAvgPrice字段，计算公式为全市场委卖额/委卖量"""

    # === 价格限制 ===
    up_limit_p: float
    """涨停价，来源于UpLimitPrice字段，当日股价不能超过此价格的上限"""
    down_limit_p: float
    """跌停价，来源于DownLimitPrice字段，当日股价不能低于此价格的下限"""
