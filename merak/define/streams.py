from __future__ import annotations

from abc import abstractmethod

from merak.define.kbar import KBar
from merak.define.tick import Tick


class TickStream:
    def __init__(self):
        pass

    @abstractmethod
    def on_tick(self, tick: Tick) -> dict[str, float]:
        raise NotImplementedError


class KBarStream:
    def __init__(self):
        pass

    @abstractmethod
    def on_kbar(self, kbar: KBar) -> dict[str, float]:
        raise NotImplementedError

    @abstractmethod
    def write_back(self):
        raise NotImplementedError
