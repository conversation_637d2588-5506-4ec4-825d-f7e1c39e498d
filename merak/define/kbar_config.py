from __future__ import annotations

from dataclasses import dataclass

import numpy as np
import pandas as pd

from merak.settings import MAX_KBAR_WINDOW, DEFAULT_FREQUENCIES_SECONDS


@dataclass
class KBarConfig:
    """K线配置信息

    包含特定时间周期的分钟K线配置信息，如标签、阈值、天数等。
    时间周期以秒为单位。
    """

    labels: list[str]  # 时间周期标签
    thresholds: list[str]  # 时间周期阈值
    days: int  # 需要的数据天数
    bars_per_day: int  # 每天的数据点数
    total_samples: int  # 总样本数


# TODO: return int float or str?
def equal_range(freq_seconds: int) -> tuple[list[str], list[str]]:
    """生成指定时间周期的K线标签和阈值

    Args:
        freq_seconds: 时间周期（单位：秒）

    Returns:
        Tuple[np.ndarray, np.ndarray]: (标签数组, 阈值数组)
    """
    # 上午时段：9:30-11:30
    am = pd.date_range(start="09:30:00", end="11:30:00", freq=f"{freq_seconds}s").strftime("%H%M%S").values
    # 下午时段：13:00-15:00
    pm = pd.date_range(start="13:00:00", end="15:00:00", freq=f"{freq_seconds}s").strftime("%H%M%S").values

    # 合并上午和下午的标签
    labels = np.hstack((am, pm[1:-1], ["150000"]))

    # 调整上午时段的起始和结束时间
    am[0] = "092959"  # 9:29:59
    am[-1] = "113030"  # 11:30:30

    # 生成阈值
    # TODO 1/2: 确定结束时间
    thresholds = np.hstack((am, pm[1:-1], ["153000"]))

    return labels.tolist(), thresholds.tolist()


def generate_minute_bar_configs(frequencies_seconds: list[int], window_size: int) -> dict[str, KBarConfig]:
    """生成不同时间周期的分钟K线配置

    Args:
        frequencies_seconds: 时间周期列表（单位：秒）
        window_size: 窗口大小

    Returns:
        Dict[int, MinuteBarConfig]: 各时间周期的配置信息
    """
    configs = {}

    for freq in frequencies_seconds:
        labels, thresholds = equal_range(freq)

        # 计算每天的数据点数
        if freq < 3 * 60:  # 小于3分钟的周期
            bars_per_day = (240 * 60 // freq + 1) - (3 * 60 // freq - 1)
        else:  # 大于等于3分钟的周期
            bars_per_day = 240 * 60 // freq + 1

        # 计算需要的数据天数
        days = max(1, int(np.ceil(window_size / bars_per_day)))

        configs[f"{freq}s"] = KBarConfig(
            labels=labels,
            thresholds=thresholds,
            days=days,
            bars_per_day=bars_per_day,
            total_samples=days * bars_per_day,
        )

    return configs


KBAR_CONFIGS = generate_minute_bar_configs(frequencies_seconds=DEFAULT_FREQUENCIES_SECONDS, window_size=MAX_KBAR_WINDOW)
KBAR_CONFIGS["day"] = KBarConfig(
    labels=["150000"],
    thresholds=["153000"],  # TODO 1/2: 确定结束时间
    days=MAX_KBAR_WINDOW,
    bars_per_day=1,
    total_samples=MAX_KBAR_WINDOW,
)
