import traceback
import time
from typing import Dict

import bottleneck as bn
import numpy as np
import numpy_groupies as npg

from kunpeng.feature.feature_preload import (
    get_eod_data,
)
from kunpeng.market_data.kbar_data_creator import kbar_data
from kunpeng.market_data.market_data_fields import (
    FIELD_RENAME_MAPPING,
    KBAR_FIELDS,
    MARKET_DATA_FIELDS,
    PRICE_FIELDS,
)
from kunpeng.market_data.minute_bar_config import (
    DEFAULT_FREQUENCIES_SECONDS as mb_freqs,
)
from kunpeng.market_data.minute_bar_config import MAX_WINDOW_SIZE, MINUTE_BAR_CONFIGS
from kunpeng.ops.ndarray_ops import (
    Abs,
    Add,
    Diff,
    DiffAcc,
    Div,
    FillNanBy,
    Filter,
    FMax,
    FMin,
    Imbalance,
    LnRet,
    Log,
    MinMaxScaler,
    MoveArgMax,
    MoveArgMin,
    MoveCount,
    MoveEMean,
    MoveFirst,
    MoveMax,
    MoveMean,
    MoveMin,
    MoveStd,
    MoveSum,
    MoveTBeta,
    MoveWMean,
    Mul,
    NanArgMax,
    NanArgMin,
    NanArrayMean,
    NanArraySum,
    NanCorrN,
    NanEMean,
    NanEMeanN,
    NanESum,
    NanFirst,
    NanKurtN,
    NanMax,
    NanMean,
    NanMeanN,
    NanMedianN,
    NanMin,
    NanPctRankN,
    NanSkewN,
    NanStdN,
    NanSum,
    NanSumN,
    NanTBetaN,
    NanTBetaTStatN,
    NanUpStdN,
    NotNan,
    Ret,
    Shift,
    Sqrt,
    Sub,
)
from kunpeng.utils.constant import (
    idx_tickers,
)
from kunpeng.utils.logger import wylogger
from kunpeng.utils.time import get_previous_n_tradingday, get_time_n_minutes_before


class CalcTickFeat:
    def __init__(
        self,
        ticker,
        tradingday,
        data,
        timecut: str,
        kbar_data: Dict[str, np.ndarray],
        pipelines,
        pipelines_info,
    ):
        self.tradingday = tradingday
        self.ticker = ticker
        self.ticker_index = idx_tickers[ticker]
        self.snap_cnt = data.shape[1]
        self.kbar_data = {}
        for key in kbar_data:
            self.kbar_data[key] = np.array(kbar_data[key][self.ticker_index])
        self.pipelines = pipelines
        self.pipelines_info = pipelines_info

        self.cache = {}
        self.res = {}
        self.tmp = {}
        self.load_snap_raw(data)

        self.tech_all = {}
        self.tech = {}
        self.tech_tmp_n = None
        self.tech_tmp_v = None

        self.timecut = timecut
        self.cut_st = 0
        self.cut_ed = None

    def control(self):
        start_time = time.time_ns()
        self.calc_snap_deri()
        self.cut_st = 0
        self.cut_ed = len(self.cache["tick_ut"])

        res = {}
        if self.cut_ed > 0:
            self.calc_tech_all()

            for _func_set_name, func_set_config in self.pipelines.items():
                if "freqs" in func_set_config:
                    tech_n = func_set_config["window_size"]
                    if len(func_set_config["freqs"]) > 1:
                        freqs_idx = [self.pipelines_info["freqs_map"][x] for x in func_set_config["freqs"]]
                        self.tech_tmp_n = []
                        self.tech_tmp_v = np.full((tech_n, len(freqs_idx), 777), np.nan)
                    else:
                        freqs_idx = self.pipelines_info["freqs_map"][func_set_config["freqs"][0]]
                        self.tech_tmp_n = []
                        self.tech_tmp_v = np.full((tech_n, 777), np.nan)

                    for field in self.tech_all:
                        self.tech[field] = self.tech_all[field][-tech_n:, freqs_idx]
                    for func_name in func_set_config["func_list"]:
                        try:
                            getattr(self, func_name)()
                        except Exception:
                            wylogger.error(
                                f"Tick base feat calc error: {func_name}, {self.ticker}, {self.timecut}, {traceback.format_exc()}"
                            )

                    if len(func_set_config["freqs"]) > 1:
                        for f_n in self.res:
                            for num, freq in enumerate(func_set_config["freqs"]):
                                if freq == "day":
                                    res[f_n] = self.res[f_n][num]
                                else:
                                    res[f"{f_n}_{freq}s"] = self.res[f_n][num]
                    else:
                        freq = func_set_config["freqs"][0]
                        if freq == "day":
                            res.update(self.res)
                        else:
                            for f_n in self.res:
                                res[f"{f_n}_{freq}s"] = self.res[f_n]
                    self.res.clear()
                    self.tech.clear()

                elif "periods" in func_set_config:
                    for period in func_set_config["periods"]:
                        if period == "all":
                            for func_name in func_set_config["func_list"]:
                                try:
                                    getattr(self, func_name)()
                                except Exception as e:
                                    wylogger.error(
                                        f"Tick base feat calc error: {func_name}, {self.ticker}, {self.timecut}, {e}"
                                    )
                            res.update(self.res)
                        else:
                            cut_t_lag_n = get_time_n_minutes_before(timecut=self.timecut, minutes=period)
                            if cut_t_lag_n > "093000":
                                self.cut_st = np.searchsorted(
                                    self.cache["tick_ut"],
                                    int(cut_t_lag_n),
                                    side="right",
                                )
                            else:
                                self.cut_st = 0

                            if self.cut_ed != self.cut_st:
                                for func_name in func_set_config["func_list"]:
                                    try:
                                        getattr(self, func_name)()
                                    except Exception as e:
                                        wylogger.error(
                                            f"Tick base feat calc error: {func_name}, {self.ticker}, {self.timecut}, {e}"
                                        )
                                for f_n in self.res:
                                    res[f"{f_n}_c{period}min"] = self.res[f_n]
                        self.res.clear()

            self.tech_all.clear()
        end = time.time_ns()
        dura = (end - start_time) / 1000 / 1000  # ns -> ms
        wylogger.warning(f"Tick base feat calc time: {dura} ms")
        return res

    def load_snap_raw(self, data):
        for num, field in enumerate(MARKET_DATA_FIELDS.stock_tick):
            if field not in PRICE_FIELDS:
                data[num] = FillNanBy(data[num], 0.0)
            if field in ["BidWAvgPrice", "AskWAvgPrice"]:
                self.cache[FIELD_RENAME_MAPPING["tick"][field]] = np.round(data[num], 3)
            else:
                self.cache[FIELD_RENAME_MAPPING["tick"][field]] = np.round(data[num], 2)

    def calc_snap_deri(self):
        last_tdate = get_previous_n_tradingday(tradingday=self.tradingday)
        mul = Div(
            get_eod_data(
                field="stk_close",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
                forward_fill=True,
            ),
            self.cache["tick_pre_close"][-1],
        )
        self.cache["adj_f_cur"] = mul * get_eod_data(
            field="stk_adjust_factor",
            ticker_index=self.ticker_index,
            tradingday=last_tdate,
            ndays=1,
        )
        self.cache["float_shr_cur"] = (
            mul
            * get_eod_data(
                field="stk_float_shr",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
            )
            * 10000
        )
        if len(self.pipelines_info["mb_freqs"]) > 0:
            for freq in self.pipelines_info["mb_freqs"]:
                mb_st = MINUTE_BAR_CONFIGS[freq].total_samples
                mb_ed = MINUTE_BAR_CONFIGS[freq].total_samples + MINUTE_BAR_CONFIGS[freq].bars_per_day
                self.kbar_data[f"stk_float_shr_{freq}"][mb_st:mb_ed] = self.cache["float_shr_cur"]
                self.kbar_data[f"stk_adjust_factor_{freq}"][mb_st:mb_ed] = self.cache["adj_f_cur"]

        self.cache["mp"] = np.round(bn.push(NanArrayMean([self.cache["bp1"], self.cache["ap1"]])), 3)

        if "tick_base_auc_v1" in self.pipelines_info["func_list"]:
            self.cache["adj_pre_hp"] = (
                get_eod_data(
                    field="stk_high",
                    ticker_index=self.ticker_index,
                    tradingday=last_tdate,
                    ndays=1,
                )
                / mul
            )
            self.cache["adj_pre_lp"] = (
                get_eod_data(
                    field="stk_low",
                    ticker_index=self.ticker_index,
                    tradingday=last_tdate,
                    ndays=1,
                )
                / mul
            )

        if "tick_ba_base_v1" in self.pipelines_info["func_list"]:
            for bs in ["bid", "ask"]:
                v_tot = self.cache[f"{bs[:1]}v_tot"]
                vwp_tot = FillNanBy(self.cache[f"{bs[:1]}vwp_tot"], 0.0)
                self.cache[f"{bs}_tvr"] = v_tot * vwp_tot

                self.cache[f"{bs}_tr"] = Div(self.cache[f"{bs[:1]}v_tot"], self.cache["float_shr_cur"])
                numerator = Sub(self.cache[f"{bs[:1]}vwp_tot"], self.cache["mp"])
                denominator = Add(self.cache[f"{bs[:1]}vwp_tot"], self.cache["mp"])
                self.cache[f"{bs}_wp_imba"] = Div(numerator, denominator)

            self.cache["ba_tvr_diff"] = self.cache["bid_tvr"] - self.cache["ask_tvr"]
            self.cache["ba_tr_diff"] = self.cache["bid_tr"] - self.cache["ask_tr"]
            self.cache["ba_tvr_imba"] = Div(
                self.cache["ba_tvr_diff"],
                (self.cache["bid_tvr"] + self.cache["ask_tvr"]),
            )
            self.cache["ba_wp_imba"] = Div(
                (self.cache["bvwp_tot"] - self.cache["avwp_tot"]),
                (self.cache["bvwp_tot"] + self.cache["avwp_tot"]),
            )

        if "tick_mp_base_v1" in self.pipelines_info["func_list"]:
            self.cache["adj_pre_hp"] = mul * get_eod_data(
                field="stk_high",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
            )
            self.cache["adj_pre_lp"] = mul * get_eod_data(
                field="stk_low",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
            )
            self.cache["mp_diff_intra"] = Diff(self.cache["mp"])
            self.cache["mp_ret_intra"] = Ret(self.cache["mp"])

        if "tick_mp_base_v2" in self.pipelines_info["func_list"]:
            self.cache["adj_pre_hp"] = mul * get_eod_data(
                field="stk_high",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
            )
            self.cache["adj_pre_lp"] = mul * get_eod_data(
                field="stk_low",
                ticker_index=self.ticker_index,
                tradingday=last_tdate,
                ndays=1,
            )
            self.cache["mp_diff"] = Diff(self.cache["mp"])
            self.cache["mp_ret"] = LnRet(self.cache["mp"])
            self.cache["mp_diff"][0] = self.cache["mp"][0] - self.cache["tick_pre_close"][-1]
            self.cache["mp_ret"][0] = Log(Div(self.cache["mp"][0], self.cache["tick_pre_close"][-1]))
            self.cache["pvt"] = MoveSum(
                self.cache["tick_vol"] * self.cache["mp_ret"],
                self.snap_cnt,
                min_n=1,
            )

    def calc_tech_all(self):
        if len(self.pipelines_info["freqs"]) > 0:
            for field in KBAR_FIELDS:
                self.tech_all[field] = np.full((MAX_WINDOW_SIZE, len(self.pipelines_info["freqs"])), np.nan)

            for num, freq in enumerate(self.pipelines_info["freqs"]):
                if freq == "day":
                    self.tech_all["pre_close"][:, num] = np.append(
                        get_eod_data(
                            field="stk_pre_close",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["tick_pre_close"][self.cut_ed - 1],
                    )
                    self.tech_all["open"][:, num] = np.append(
                        get_eod_data(
                            field="stk_open",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["tick_op"][self.cut_ed - 1],
                    )
                    self.tech_all["close"][:, num] = np.append(
                        get_eod_data(
                            field="stk_close",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["mp"][self.cut_ed - 1],
                    )
                    self.tech_all["high"][:, num] = np.append(
                        get_eod_data(
                            field="stk_high",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["tick_hp"][self.cut_ed - 1],
                    )
                    self.tech_all["low"][:, num] = np.append(
                        get_eod_data(
                            field="stk_low",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["tick_lp"][self.cut_ed - 1],
                    )
                    self.tech_all["volume"][:, num] = np.append(
                        get_eod_data(
                            field="stk_volume",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        )
                        * 100,
                        NanSum(self.cache["tick_vol"][: self.cut_ed]),
                    )
                    self.tech_all["amount"][:, num] = np.append(
                        get_eod_data(
                            field="stk_amount",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        )
                        * 1000,
                        NanSum(self.cache["tick_tvr"][: self.cut_ed]),
                    )
                    self.tech_all["float_shr"][:, num] = np.append(
                        get_eod_data(
                            field="stk_float_shr",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        )
                        * 10000,
                        self.cache["float_shr_cur"],
                    )
                    self.tech_all["adjust_factor"][:, num] = np.append(
                        get_eod_data(
                            field="stk_adjust_factor",
                            ticker_index=self.ticker_index,
                            tradingday=self.tradingday,
                            ndays=MAX_WINDOW_SIZE,
                        ),
                        self.cache["adj_f_cur"],
                    )
                elif freq in mb_freqs:
                    mb_bar_idx = np.searchsorted(
                        MINUTE_BAR_CONFIGS[freq].thresholds,
                        self.cache["tick_ut"][: self.cut_ed],
                    )
                    mb_ed = MINUTE_BAR_CONFIGS[freq].total_samples + np.max(mb_bar_idx) + 1
                    mb_st_w = MINUTE_BAR_CONFIGS[freq].total_samples + np.min(mb_bar_idx)
                    mb_st_r = max(0, mb_ed - MAX_WINDOW_SIZE)
                    n = mb_ed - mb_st_r

                    mp_ed = bn.push(
                        npg.aggregate(
                            mb_bar_idx,
                            self.cache["mp"][: self.cut_ed],
                            "nanlast",
                            fill_value=np.nan,
                        )
                    )
                    self.kbar_data[f"stk_mp_ed_{freq}"][mb_st_w:mb_ed] = mp_ed
                    self.tech_all["pre_close"][-n:, num] = Shift(self.kbar_data[f"stk_mp_ed_{freq}"][:mb_ed])[-n:]
                    self.tech_all["close"][-n:, num] = self.kbar_data[f"stk_mp_ed_{freq}"][mb_st_r:mb_ed]

                    self.kbar_data[f"stk_mp_st_{freq}"][mb_st_w:mb_ed] = FillNanBy(
                        npg.aggregate(
                            mb_bar_idx,
                            self.cache["mp"][: self.cut_ed],
                            "nanfirst",
                            fill_value=np.nan,
                        ),
                        mp_ed,
                    )
                    self.tech_all["open"][-n:, num] = self.kbar_data[f"stk_mp_st_{freq}"][mb_st_r:mb_ed]

                    self.kbar_data[f"stk_mp_max_{freq}"][mb_st_w:mb_ed] = FillNanBy(
                        npg.aggregate(
                            mb_bar_idx,
                            self.cache["mp"][: self.cut_ed],
                            "nanmax",
                            fill_value=np.nan,
                        ),
                        mp_ed,
                    )
                    self.tech_all["high"][-n:, num] = self.kbar_data[f"stk_mp_max_{freq}"][mb_st_r:mb_ed]

                    self.kbar_data[f"stk_mp_min_{freq}"][mb_st_w:mb_ed] = FillNanBy(
                        npg.aggregate(
                            mb_bar_idx,
                            self.cache["mp"][: self.cut_ed],
                            "nanmin",
                            fill_value=np.nan,
                        ),
                        mp_ed,
                    )
                    self.tech_all["low"][-n:, num] = self.kbar_data[f"stk_mp_min_{freq}"][mb_st_r:mb_ed]

                    self.kbar_data[f"stk_vol_sum_{freq}"][mb_st_w:mb_ed] = npg.aggregate(
                        mb_bar_idx,
                        self.cache["tick_vol"][: self.cut_ed],
                        "sum",
                        fill_value=0.0,
                    )
                    self.tech_all["volume"][-n:, num] = self.kbar_data[f"stk_vol_sum_{freq}"][mb_st_r:mb_ed]

                    self.kbar_data[f"stk_tvr_sum_{freq}"][mb_st_w:mb_ed] = npg.aggregate(
                        mb_bar_idx,
                        self.cache["tick_tvr"][: self.cut_ed],
                        "sum",
                        fill_value=0.0,
                    )
                    self.tech_all["amount"][-n:, num] = self.kbar_data[f"stk_tvr_sum_{freq}"][mb_st_r:mb_ed]

                    self.tech_all["float_shr"][-n:, num] = self.kbar_data[f"stk_float_shr_{freq}"][mb_st_r:mb_ed]
                    self.tech_all["adjust_factor"][-n:, num] = self.kbar_data[f"stk_adjust_factor_{freq}"][
                        mb_st_r:mb_ed
                    ]

            self.tech_all["typ"] = NanArrayMean([self.tech_all["close"], self.tech_all["high"], self.tech_all["low"]])
            self.tech_all["hlap"] = np.round(NanArrayMean([self.tech_all["high"], self.tech_all["low"]]), 3)

            self.tech_all["float_tr"] = Div(self.tech_all["volume"], self.tech_all["float_shr"])

            self.tech_all["adjust_factor"] = Div(self.tech_all["adjust_factor"], self.tech_all["adjust_factor"][-1])
            self.tech_all["adj_op"] = self.tech_all["open"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_cp"] = self.tech_all["close"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_hp"] = self.tech_all["high"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_lp"] = self.tech_all["low"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_typ"] = self.tech_all["typ"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_hlap"] = self.tech_all["hlap"] * self.tech_all["adjust_factor"]
            self.tech_all["adj_vol"] = Div(self.tech_all["volume"], self.tech_all["adjust_factor"])

            self.tech_all["adj_cp_diff"] = np.round(
                (self.tech_all["close"] - self.tech_all["pre_close"]) * self.tech_all["adjust_factor"],
                7,
            )

            self.tech_all["vol_cond"] = NotNan(self.tech_all["volume"])
            self.tech_all["adj_cp_up"] = np.maximum(self.tech_all["adj_cp_diff"], 0)
            self.tech_all["adj_cp_down"] = np.minimum(self.tech_all["adj_cp_diff"], 0)
            self.tech_all["adj_cp_ret"] = Log(Div(self.tech_all["close"], self.tech_all["pre_close"]))

            self.tech_all["adj_op_diff"] = np.round(
                Filter(self.tech_all["vol_cond"], Diff(self.tech_all["adj_op"], 1, 0)),
                7,
            )

            self.tech_all["adj_hp_diff"] = np.round(
                Filter(self.tech_all["vol_cond"], Diff(self.tech_all["adj_hp"], 1, 0)),
                7,
            )

            self.tech_all["adj_lp_diff"] = np.round(
                Filter(self.tech_all["vol_cond"], Diff(self.tech_all["adj_lp"], 1, 0)),
                7,
            )
            self.tech_all["adj_typ_diff"] = np.round(
                Filter(self.tech_all["vol_cond"], Diff(self.tech_all["adj_typ"], 1, 0)),
                7,
            )
            self.tech_all["adj_hlap_diff"] = np.round(
                Filter(self.tech_all["vol_cond"], Diff(self.tech_all["adj_hlap"], 1, 0)),
                7,
            )

            self.tech_all["range"] = np.round(
                Filter(
                    self.tech_all["vol_cond"],
                    self.tech_all["high"] - self.tech_all["low"],
                ),
                7,
            )
            self.tech_all["ad_w"] = Div(
                2 * self.tech_all["close"] - self.tech_all["low"] - self.tech_all["high"],
                self.tech_all["range"],
            )
            self.tech_all["wvad_w"] = Div(self.tech_all["close"] - self.tech_all["open"], self.tech_all["range"])

            self.tech_all["accumulation"] = np.round(
                Filter(
                    self.tech_all["vol_cond"],
                    self.tech_all["close"] - FMin(self.tech_all["low"], self.tech_all["pre_close"]),
                ),
                7,
            )
            self.tech_all["distribution"] = np.round(
                Filter(
                    self.tech_all["vol_cond"],
                    self.tech_all["close"] - FMax(self.tech_all["high"], self.tech_all["pre_close"]),
                ),
                7,
            )
            self.tech_all["ture_range"] = np.round(self.tech_all["accumulation"] - self.tech_all["distribution"], 7)

    def limit_idx(self):
        if (self.cache["bv_tot"][-1] > 0) & (self.cache["av_tot"][-1] == 0) or (self.cache["bv_tot"][-1] == 0) & (
            self.cache["av_tot"][-1] > 0
        ):
            self.res["limit_idx"] = 1
        else:
            self.res["limit_idx"] = 0

    def tick_base_auc_v1(self):
        for bs in ["b", "a"]:
            self.tmp[f"auc_{bs}tvr1"] = self.cache[f"{bs}v1"][-1] * FillNanBy(self.cache[f"{bs}p1"][-1], 0.0)
            self.res[f"auc_{bs}tvr_tot"] = self.cache[f"{bs}v_tot"][-1] * FillNanBy(self.cache[f"{bs}vwp_tot"][-1], 0.0)
            self.res[f"auc_{bs}vwp_tot_bias"] = Abs(Div(self.cache[f"{bs}vwp_tot"][-1], self.cache["mp"][-1]) - 1)
            for n in [5, 10]:
                self.res[f"auc_{bs}v_{n}lv_pct"] = Div(
                    NanSum([self.cache[f"{bs}v{x}"][-1] for x in range(1, n + 1)]),
                    self.cache[f"{bs}v_tot"][-1],
                )

        self.res["auc_ba_tvr1_imba"] = Imbalance(self.tmp["auc_btvr1"], self.tmp["auc_atvr1"])
        self.res["auc_ba_tvr_tot_imba"] = Imbalance(self.res["auc_btvr_tot"], self.res["auc_atvr_tot"])
        full_tvr = self.res["auc_btvr_tot"] + self.res["auc_atvr_tot"] + self.cache["tick_tvr"][-1]
        self.res["auc_ba_tvr_tot_full_imba"] = Div(self.res["auc_btvr_tot"] - self.res["auc_atvr_tot"], full_tvr)
        self.res["auc_ba_tvr_tot_full_pct"] = Div(self.res["auc_btvr_tot"] + self.res["auc_atvr_tot"], full_tvr)

        self.res["auc_ba_p1_imba"] = Imbalance(self.cache["bp1"][-1], self.cache["ap1"][-1])
        self.res["auc_ba_vwp_tot_imba"] = Imbalance(self.cache["bvwp_tot"][-1], self.cache["avwp_tot"][-1])
        self.res["auc_ba_vwp_tot_bias"] = (
            Div(
                Div(
                    self.res["auc_btvr_tot"] + self.res["auc_atvr_tot"],
                    self.cache["bv_tot"][-1] + self.cache["av_tot"][-1],
                ),
                self.cache["mp"][-1],
            )
            - 1
        )

        self.res["auc_mp2pre_cp"] = Div(self.cache["mp"][-1], self.cache["tick_pre_close"][-1]) - 1
        for n in [2, 3, 5]:
            last_nd = get_previous_n_tradingday(tradingday=self.tradingday, days=n)
            self.res[f"auc_mp2pre{n}d_cp"] = (
                Div(
                    self.cache["mp"][-1] * self.cache["adj_f_cur"],
                    get_eod_data(
                        field="stk_close",
                        ticker_index=self.ticker_index,
                        tradingday=last_nd,
                        ndays=1,
                    )
                    * get_eod_data(
                        field="stk_adjust_factor",
                        ticker_index=self.ticker_index,
                        tradingday=last_nd,
                        ndays=1,
                    ),
                )
                - 1
            )
        self.res["auc_mp2pre_hp"] = Div(self.cache["mp"][-1], self.cache["adj_pre_hp"]) - 1
        self.res["auc_mp2pre_lp"] = Div(self.cache["mp"][-1], self.cache["adj_pre_lp"]) - 1
        self.res["auc_mp_pre_range"] = Div(
            self.cache["mp"][-1] - self.cache["adj_pre_lp"],
            self.cache["adj_pre_hp"] - self.cache["adj_pre_lp"],
        )

        p_diff = Diff(self.cache["mp"])
        p_ret = Ret(self.cache["mp"])
        auc_vol = DiffAcc(self.cache["bv1"])
        ba_vol_dif = self.cache["bv2"] - self.cache["av2"]
        ba_vol_imba = Div(ba_vol_dif, self.cache["bv2"] + self.cache["av2"] + 2 * self.cache["bv1"])

        split_idx = np.searchsorted(self.cache["tick_ut"], 92000)
        self.res["auc_vol_drop_cnt_p1"] = NanSum((auc_vol[:split_idx] < 0).astype(np.float64))

        p_ranges = [range(0, split_idx), range(split_idx, len(p_ret) - 1)]
        for num, p_range in enumerate(p_ranges):
            self.res[f"auc_ret_up_cnt_pct_p{num + 1}"] = NanMean(np.sign(np.maximum(p_ret, 0)[p_range]))

            if len(p_range) == 0:
                self.res[f"auc_ret_p{num + 1}"] = 0.0
                self.res[f"auc_cmo_p{num + 1}"] = np.nan
                self.res[f"auc_ba_vol_imba_avg_p{num + 1}"] = np.nan
            else:
                self.res[f"auc_ret_p{num + 1}"] = Div(self.cache["mp"][p_range[-1]], self.cache["mp"][p_range[0]]) - 1
                self.res[f"auc_cmo_p{num + 1}"] = Div(
                    NanSum(p_diff[p_range]),
                    NanSum(Abs(p_diff[p_range]), high_precision=True),
                )
                self.res[f"auc_ba_vol_imba_avg_p{num + 1}"] = NanMean(ba_vol_imba[p_range])

        for field in [
            "ret",
            "ret_up_cnt_pct",
            "cmo",
            "ba_vol_imba_avg",
        ]:
            self.res[f"auc_{field}_p2vs1"] = NanArraySum([self.res[f"auc_{field}_p2"], -self.res[f"auc_{field}_p1"]])

    def tick_ba_base_v1(self):
        fields = [
            "bid_tvr",
            "ask_tvr",
            "bid_tr",
            "ask_tr",
            "ba_tvr_diff",
            "ba_tr_diff",
            "ba_tvr_imba",
            "ba_wp_imba",
            "bid_wp_imba",
            "ask_wp_imba",
        ]
        for field in fields:
            self.res["%s_ed" % field] = self.cache[field][self.cut_ed - 1]
            self.res["%s_avg" % field] = NanMean(self.cache[field][self.cut_st : self.cut_ed])
            self.res["%s_pmr" % field] = Sub(
                self.res["%s_ed" % field],
                NanFirst(self.cache[field][self.cut_st : self.cut_ed]),
            )
            self.res["%s_navg" % field] = self.res["%s_ed" % field] - self.res["%s_avg" % field]

    def tick_mp_base_v1(self):
        n = self.cut_ed - self.cut_st
        mp2pre_cp = (
            Div(
                self.cache["mp"][self.cut_st : self.cut_ed],
                self.cache["tick_pre_close"][self.cut_st : self.cut_ed],
            )
            - 1
        )
        mp2op = (
            Div(
                self.cache["mp"][self.cut_st : self.cut_ed],
                self.cache["tick_op"][self.cut_st : self.cut_ed],
            )
            - 1
        )
        mp2vwap = (
            Div(
                self.cache["mp"][self.cut_st : self.cut_ed],
                Div(
                    MoveSum(self.cache["tick_tvr"][self.cut_st : self.cut_ed], n, min_n=1),
                    MoveSum(
                        self.cache["tick_vol"][self.cut_st : self.cut_ed],
                        n,
                        min_n=1,
                        high_precision=True,
                    ),
                ),
            )
            - 1
        )
        mp2twap = (
            Div(
                self.cache["mp"][self.cut_st : self.cut_ed],
                MoveMean(self.cache["mp"][self.cut_st : self.cut_ed], n, min_n=1),
            )
            - 1
        )
        avg_p2mp_lag = FillNanBy(
            Div(
                Div(self.cache["tick_tvr"], self.cache["tick_vol"])[self.cut_st : self.cut_ed],
                Shift(self.cache["mp"])[self.cut_st : self.cut_ed],
            )
            - 1,
            0,
        )

        for field in ["mp2pre_cp", "mp2op", "mp2vwap", "mp2twap", "avg_p2mp_lag"]:
            self.res["%s_ed" % field] = locals()[field][-1]
            self.res["%s_navg" % field] = locals()[field][-1] - NanMean(locals()[field])

        self.res["mp2pre_hp_ed"] = Div(self.cache["mp"][self.cut_ed - 1], self.cache["adj_pre_hp"]) - 1
        self.res["mp2pre_lp_ed"] = Div(self.cache["mp"][self.cut_ed - 1], self.cache["adj_pre_lp"]) - 1
        self.res["mp2hp_ed"] = (
            Div(
                self.cache["mp"][self.cut_ed - 1],
                self.cache["tick_hp"][self.cut_ed - 1],
            )
            - 1
        )
        self.res["mp2lp_ed"] = (
            Div(
                self.cache["mp"][self.cut_ed - 1],
                self.cache["tick_lp"][self.cut_ed - 1],
            )
            - 1
        )

        p_diff = self.cache["mp_diff_intra"][self.cut_st : self.cut_ed]
        p_ret = self.cache["mp_ret_intra"][self.cut_st : self.cut_ed]
        p_up = np.maximum(p_ret, 0.0)
        p_down = np.minimum(p_ret, 0.0)
        self.res["mp_up_ret"] = NanSumN(p_up, min_n=1)
        self.res["mp_down_ret"] = NanSumN(p_down, min_n=1)
        p_up_cnt = NanSumN(np.sign(p_up), min_n=1)
        if p_up_cnt == 0:
            self.res["mp_up_ret_ptk"] = 0.0
        else:
            self.res["mp_up_ret_ptk"] = Div(self.res["mp_up_ret"], p_up_cnt)
        p_down_cnt = NanSumN(np.sign(np.abs(p_down)), min_n=1)
        if p_down_cnt == 0:
            self.res["mp_down_ret_ptk"] = 0.0
        else:
            self.res["mp_down_ret_ptk"] = Div(self.res["mp_down_ret"], p_down_cnt)
        self.res["mp_ud_ret_ptk"] = NanArraySum([self.res["mp_up_ret_ptk"], self.res["mp_down_ret_ptk"]])
        self.res["mp_trend"] = Div(NanSum(p_diff), NanSum(Abs(p_diff), high_precision=True))
        if self.timecut in ["093000", "093003", "093006"]:
            self.res["mp_ret_std"] = np.nan
            self.res["mp_vol_corr"] = np.nan
        else:
            self.res["mp_ret_std"] = NanStdN(p_ret, min_n=5, avg=0)
            self.res["mp_vol_corr"] = NanCorrN(
                self.cache["mp"][self.cut_st : self.cut_ed],
                self.cache["tick_vol"][self.cut_st : self.cut_ed],
                min_n=5,
            )

    def tick_voi(self):
        bp1_diff = Diff(self.cache["bp1"])[self.cut_st : self.cut_ed]
        ap1_diff = Diff(self.cache["ap1"])[self.cut_st : self.cut_ed]
        bv1_diff = Diff(self.cache["bv1"])[self.cut_st : self.cut_ed]
        av1_diff = Diff(self.cache["av1"])[self.cut_st : self.cut_ed]
        v_bid = np.where(
            np.isnan(bp1_diff),
            np.nan,
            np.where(
                bp1_diff == 0,
                bv1_diff,
                np.where(bp1_diff > 0, Sqrt(self.cache["bv1"][self.cut_st : self.cut_ed]), 0),
            ),
        )
        v_ask = np.where(
            np.isnan(ap1_diff),
            np.nan,
            np.where(
                ap1_diff == 0,
                av1_diff,
                np.where(ap1_diff < 0, Sqrt(self.cache["av1"][self.cut_st : self.cut_ed]), 0),
            ),
        )
        v_bid = np.sqrt(np.abs(v_bid)) * np.sign(v_bid)
        v_ask = np.sqrt(np.abs(v_ask)) * np.sign(v_ask)
        voi = v_bid - v_ask
        voi2vol = Div(voi, self.cache["tick_vol"][self.cut_st : self.cut_ed])
        self.res["voi_ed"] = voi[-1]
        self.res["voi_navg"] = Sub(voi[-1], NanMean(voi))
        self.res["voi2vol_ed"] = voi2vol[-1]
        self.res["voi2vol_navg"] = Sub(voi2vol[-1], NanMean(voi2vol))

    def tick_vol_pct(self):
        n = self.cut_ed - self.cut_st
        vol_pct = Div(
            self.cache["tick_vol"][self.cut_st : self.cut_ed],
            MoveSum(
                self.cache["tick_vol"][self.cut_st : self.cut_ed],
                n,
                min_n=1,
                high_precision=True,
            ),
        )
        self.res["vol_pct_ed"] = vol_pct[-1]
        self.res["vol_pct_navg"] = Sub(vol_pct[-1], NanMean(vol_pct))

    def tick_ba_lvn_pct(self):
        for n in [5, 10]:
            bid_lvn_pct = Div(
                NanArraySum([self.cache["bv%d" % x][self.cut_st : self.cut_ed] for x in range(1, n + 1)]),
                self.cache["bv_tot"][self.cut_st : self.cut_ed],
            )
            self.res["bid_lv%d_pct_ed" % n] = bid_lvn_pct[-1]
            self.res["bid_lv%d_pct_navg" % n] = Sub(bid_lvn_pct[-1], NanMean(bid_lvn_pct))
            ask_lvn_pct = Div(
                NanArraySum([self.cache["av%d" % x][self.cut_st : self.cut_ed] for x in range(1, n + 1)]),
                self.cache["av_tot"][self.cut_st : self.cut_ed],
            )
            self.res["ask_lv%d_pct_ed" % n] = ask_lvn_pct[-1]
            self.res["ask_lv%d_pct_navg" % n] = Sub(ask_lvn_pct[-1], NanMean(ask_lvn_pct))

    def tick_mp_base_v2(self):
        n = self.cut_ed - self.cut_st
        cut_st = max(self.cut_st - n, 0)
        mp_st = MoveFirst(self.cache["mp"][cut_st : self.cut_ed], n, min_n=1)
        mp_max = MoveMax(self.cache["mp"][cut_st : self.cut_ed], n, min_n=1)
        mp_min = MoveMin(self.cache["mp"][cut_st : self.cut_ed], n, min_n=1)
        mp_twap = MoveMean(self.cache["mp"][cut_st : self.cut_ed], n, min_n=1)
        # mp_vwap = FillNanBy(MoveWMean(
        #     self.cache['mp'][cut_st:self.cut_ed],
        #     self.cache['tick_vol'][cut_st:self.cut_ed], n, min_n=1), mp_twap)

        locals()["mp_ed2mp_st"] = Log(
            Div(self.cache["mp"][cut_st : self.cut_ed], mp_st)  # stage2&3
        )
        locals()["mp_range"] = Div(self.cache["mp"][cut_st : self.cut_ed] - mp_min, mp_max - mp_min)
        locals()["mp_arpp"] = Div(mp_twap - mp_min, mp_max - mp_min)
        # locals()['mp_apb'] = Log(Div(mp_twap, mp_vwap))
        # locals()['mp_ed2mp_twap'] = Log(Div(self.cache['mp'][cut_st:self.cut_ed], mp_twap))
        # locals()['mp_ed2mp_vwap'] = Log(Div(self.cache['mp'][cut_st:self.cut_ed], mp_vwap))
        # locals()['avg_p2mp_ed'] = FillNanBy(Log(Div(
        #     Div(self.cache['tick_tvr'][cut_st:self.cut_ed], self.cache['tick_vol'][cut_st:self.cut_ed]),
        #     NanArrayMean([self.cache['mp'][cut_st:self.cut_ed], Shift(self.cache['mp'][cut_st:self.cut_ed])]))), 0)

        for field in [
            "mp_ed2mp_st",
            "mp_range",
            "mp_arpp",
            # 'mp_apb', 'mp_ed2mp_twap', 'mp_ed2mp_vwap', 'avg_p2mp_ed'
        ]:
            self.res[field] = locals()[field][-1]
            self.res["%s_navg" % field] = Sub(locals()[field][-1], NanMean(locals()[field][-n:]))

        self.res["mp_ed2pre_cp"] = Log(
            Div(
                self.cache["mp"][self.cut_ed - 1],
                self.cache["tick_pre_close"][self.cut_ed - 1],
            )
        )
        self.res["mp_ed2pre_hp"] = Log(Div(self.cache["mp"][self.cut_ed - 1], self.cache["adj_pre_hp"]))
        self.res["mp_ed2pre_lp"] = Log(Div(self.cache["mp"][self.cut_ed - 1], self.cache["adj_pre_lp"]))
        # self.res['mp_ed2mp_max'] = Log(Div(self.cache['mp'][self.cut_ed - 1], mp_max[-1]))
        # self.res['mp_ed2mp_min'] = Log(Div(self.cache['mp'][self.cut_ed - 1], mp_min[-1]))

        p_diff = self.cache["mp_diff"][self.cut_st : self.cut_ed]
        p_ret = self.cache["mp_ret"][self.cut_st : self.cut_ed]
        p_up = np.maximum(p_ret, 0.0)
        p_down = np.minimum(p_ret, 0.0)
        self.res["mp_ret_ud"] = NanSumN(p_up, min_n=1) - NanSumN(p_down, min_n=1)
        self.res["mp_ret_upcnt_pct"] = (p_diff > 0.0).sum() / (self.cut_ed - self.cut_st)
        # self.res['mp_ret_down_cnt'] = float((p_diff < 0.).sum())
        # self.res['mp_ret_ud_cnt'] = self.res['mp_ret_up_cnt'] - self.res['mp_ret_down_cnt']
        # self.res['mp_ret_w_vol'] = NanWSumN(p_ret, self.cache['tick_vol'][self.cut_st:self.cut_ed], min_n=1)
        self.res["mp_trendstr"] = Div(NanSum(p_diff), NanSum(Abs(p_diff), high_precision=True))
        if self.timecut in ["093000", "093003", "093006"]:
            self.res["mp_ret_upstd_pct"] = np.nan
            self.res["mp_ret_skew"] = np.nan
            self.res["mp_ret_kurt"] = np.nan
            self.res["mp_pvt_corr"] = np.nan
        else:
            self.res["mp_ret_upstd_pct"] = Div(
                NanUpStdN(p_ret, min_n=5, avg=0),
                NanStdN(p_ret, min_n=5, avg=0, high_precision=True),
            )
            self.res["mp_ret_skew"] = NanSkewN(p_ret, min_n=5, avg=0)
            self.res["mp_ret_kurt"] = NanKurtN(p_ret, min_n=5, avg=0)
            self.res["mp_pvt_corr"] = NanCorrN(
                self.cache["mp"][self.cut_st : self.cut_ed],
                self.cache["pvt"][self.cut_st : self.cut_ed],
                min_n=5,
            )

    def write_to_tech_tmp(self, f_n, f_v):
        if self.tech_tmp_v.ndim == 3:
            if isinstance(f_n, str):
                self.tech_tmp_v[-f_v.shape[0] :, :, len(self.tech_tmp_n)] = f_v
                self.tech_tmp_n.append(f_n)
            else:
                self.tech_tmp_v[
                    -f_v.shape[0] :,
                    :,
                    len(self.tech_tmp_n) : len(self.tech_tmp_n) + len(f_n),
                ] = f_v
                self.tech_tmp_n.extend(f_n)
        elif self.tech_tmp_v.ndim == 2:
            if isinstance(f_n, str):
                self.tech_tmp_v[-f_v.shape[0] :, len(self.tech_tmp_n)] = f_v
                self.tech_tmp_n.append(f_n)
            else:
                self.tech_tmp_v[
                    -f_v.shape[0] :,
                    len(self.tech_tmp_n) : len(self.tech_tmp_n) + len(f_n),
                ] = f_v
                self.tech_tmp_n.extend(f_n)

    def tech_v2_last(self):
        u_fields = [
            "forceindex5",
            "bop5",
            "cmo5",
            "rsi10",
            "srsi10",
            "rvi5&10",
            "mfi5",
            "vr5",
            "ar5",
            "br5",
            "arbr5",
            "cr5",
            "ddi5",
            "adtm5",
            "cmf5",
            "acd5",
            "tsi10&5",
            "atrp10",
            "plusdi10",
            "minusdi10",
            "adxr10",
            "ulcer5",
            "emv5",
            "ckv5",
            "stdamb5",
            "pvtx5",
            "nvtx5",
            "vtx5",
            "trix5&9",
            "plrc5",
            "dpo5",
            "squeeze10",
            "chdexit_long10&3",
            "chdexit_short10&3",
            "fso5",
            "sso5",
            "dsso10",
            "illiq5",
            "cko3&10",
            "kvo34&55",
            "forceindex10",
            "bop10",
            "cmo10",
            "rsi20",
            "srsi20",
            "cmb27&9&5&3",
            "rvi10&20",
            "mfi10",
            "vr10",
            "ar10",
            "br10",
            "arbr10",
            "cr10",
            "ddi10",
            "adtm10",
            "cmf10",
            "acd10",
            "dev5",
            "cci5",
            "dev60",
            "cci60",
            "tsi20&10",
            "pvi",
            "nvi",
            "atrp20",
            "plusdi20",
            "minusdi20",
            "adxr20",
            "massindex9&25",
            "ulcer10",
            "emv10",
            "ckv10",
            "stdamb10",
            "pvtx10",
            "nvtx10",
            "vtx10",
            "trix10&9",
            "plrc10",
            "macd12&26&9",
            "vmacd12&26&9",
            "dpo10",
            "bb_loc10&1.5",
            "bb_width10&1.5",
            "bb_loc50&2.5",
            "bb_width50&2.5",
            "kb_loc10&1.5",
            "kb_width10&1.5",
            "kb_loc50&2.5",
            "kb_width50&2.5",
            "squeeze20",
            "chdexit_long20&3",
            "chdexit_short20&3",
            "fso10",
            "sso10",
            "dsso20",
            "uo7&14&28",
            "illiq10",
            "kst",
        ]
        u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
        u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
        last_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-1]
        last_fields = [f"tech_v2_{x}" for x in u_fields]
        self.res.update(dict(zip(last_fields, last_v.T)))

    def tech_v2_tz(self):
        u_fields_dict = {
            10: [
                "forceindex5",
                "bop5",
                "cmo5",
                "rsi10",
                "srsi10",
                "rvi5&10",
                "mfi5",
                "vr5",
                "ar5",
                "br5",
                "arbr5",
                "cr5",
                "ddi5",
                "adtm5",
                "cmf5",
                "acd5",
                "tsi10&5",
                "atrp10",
                "plusdi10",
                "minusdi10",
                "adxr10",
                "ulcer5",
                "emv5",
                "ckv5",
                "stdamb5",
                "pvtx5",
                "nvtx5",
                "vtx5",
                "trix5&9",
                "plrc5",
                "dpo5",
                "squeeze10",
                "chdexit_long10&3",
                "chdexit_short10&3",
                "fso5",
                "sso5",
                "dsso10",
                "illiq5",
                "obv",
                "pvt",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "forceindex10",
                "bop10",
                "cmo10",
                "rsi20",
                "srsi20",
                "cmb27&9&5&3",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "arbr10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "dev5",
                "cci5",
                "dev60",
                "cci60",
                "tsi20&10",
                "pvi",
                "nvi",
                "atrp20",
                "plusdi20",
                "minusdi20",
                "adxr20",
                "massindex9&25",
                "ulcer10",
                "emv10",
                "ckv10",
                "stdamb10",
                "pvtx10",
                "nvtx10",
                "vtx10",
                "trix10&9",
                "plrc10",
                "macd12&26&9",
                "vmacd12&26&9",
                "dpo10",
                "bb_loc10&1.5",
                "bb_width10&1.5",
                "bb_loc50&2.5",
                "bb_width50&2.5",
                "kb_loc10&1.5",
                "kb_width10&1.5",
                "kb_loc50&2.5",
                "kb_width50&2.5",
                "squeeze20",
                "chdexit_long20&3",
                "chdexit_short20&3",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
                "illiq10",
                "kst",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:]
            tz_v = Div(
                u_v[-1] - NanMeanN(u_v, axis=0),
                NanStdN(u_v, axis=0, high_precision=True),
            )
            tz_fields = [f"tech_v2_{x}_tz{n}" for x in u_fields]
            self.res.update(dict(zip(tz_fields, tz_v.T)))

    def tech_v2_avg(self):
        u_fields_dict = {
            10: [
                "cmo10",
                "rsi20",
                "srsi20",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "arbr10",
                "cr10",
                "ddi10",
                "adtm10",
                "acd10",
                "cci10",
                "tsi20&10",
                "bb_loc20&2",
                "kb_loc20&2",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:]
            avg_v = NanMeanN(u_v, axis=0)
            avg_fields = [f"tech_v2_{x}_avg{n}" for x in u_fields]
            self.res.update(dict(zip(avg_fields, avg_v.T)))

    def tech_v2_upewa(self):
        u_fields_dict = {
            10: [
                "cko3&10",
                "forceindex10",
                "bop10",
                "cmo10",
                "rsi20",
                "srsi20",
                "cmb27&9&5&3",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "arbr10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci10",
                "tsi20&10",
                "vtx10",
                "trix10&9",
                "plrc10",
                "macd12&26&9",
                "vmacd12&26&9",
                "bb_loc20&2",
                "kb_loc20&2",
                "chdexit_long20&3",
                "chdexit_short20&3",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
                "kst",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:]
            upewa_v = NanEMeanN((u_v > 0).astype(np.float64), axis=0)
            upewa_fields = [f"tech_{x}_upewa{n}" for x in u_fields]
            self.res.update(dict(zip(upewa_fields, upewa_v.T)))

    def tech_v2_tslope_tacce(self):
        u_fields_dict = {
            10: [
                "forceindex5",
                "bop5",
                "cmo5",
                "rsi10",
                "srsi10",
                "rvi5&10",
                "mfi5",
                "vr5",
                "ar5",
                "br5",
                "arbr5",
                "cr5",
                "ddi5",
                "adtm5",
                "cmf5",
                "acd5",
                "tsi10&5",
                "atrp10",
                "plusdi10",
                "minusdi10",
                "adxr10",
                "ulcer5",
                "emv5",
                "ckv5",
                "stdamb5",
                "pvtx5",
                "nvtx5",
                "vtx5",
                "trix5&9",
                "plrc5",
                "dpo5",
                "squeeze10",
                "chdexit_long10&3",
                "chdexit_short10&3",
                "fso5",
                "sso5",
                "dsso10",
                "illiq5",
                "adj_cp",
                "obv",
                "pvt",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "forceindex10",
                "bop10",
                "cmo10",
                "rsi20",
                "srsi20",
                "cmb27&9&5&3",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "arbr10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "dev5",
                "cci5",
                "dev60",
                "cci60",
                "tsi20&10",
                "pvi",
                "nvi",
                "atrp20",
                "plusdi20",
                "minusdi20",
                "adxr20",
                "massindex9&25",
                "ulcer10",
                "emv10",
                "ckv10",
                "stdamb10",
                "pvtx10",
                "nvtx10",
                "vtx10",
                "trix10&9",
                "plrc10",
                "macd12&26&9",
                "vmacd12&26&9",
                "dpo10",
                "bb_loc10&1.5",
                "bb_width10&1.5",
                "bb_loc50&2.5",
                "bb_width50&2.5",
                "kb_loc10&1.5",
                "kb_width10&1.5",
                "kb_loc50&2.5",
                "kb_width50&2.5",
                "squeeze20",
                "chdexit_long20&3",
                "chdexit_short20&3",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
                "illiq10",
                "kst",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = MinMaxScaler(np.take(self.tech_tmp_v, u_idx, axis=-1)[-(n + 2) :], axis=0)
            u_v_diff = Diff(u_v, 1, 0)
            tslope_v = NanEMeanN(u_v_diff[-n:], axis=0)
            tslope_fields = [f"tech_v2_{x}_tslope{n}" for x in u_fields]
            self.res.update(dict(zip(tslope_fields, tslope_v.T)))
            tacce_v = NanEMeanN(Diff(u_v_diff, 1, 0), axis=0)
            tacce_fields = [f"tech_v2_{x}_tacce{n}" for x in u_fields]
            self.res.update(dict(zip(tacce_fields, tacce_v.T)))

    def tech_v2_pconf(self):
        u_fields_dict = {
            20: [
                "forceindex5",
                "bop5",
                "cmo5",
                "rsi10",
                "srsi10",
                "rvi5&10",
                "mfi5",
                "vr5",
                "ar5",
                "br5",
                "cr5",
                "ddi5",
                "adtm5",
                "cmf5",
                "acd5",
                "tsi10&5",
                "trix5&9",
                "plrc5",
                "fso5",
                "sso5",
                "dsso10",
                "obv",
                "pvt",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "forceindex10",
                "bop10",
                "cmo10",
                "rsi20",
                "srsi20",
                "cmb27&9&5&3",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci5",
                "cci60",
                "tsi20&10",
                "trix10&9",
                "plrc10",
                "macd12&26&9",
                "vmacd12&26&9",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
                "kst",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = MinMaxScaler(np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:], axis=0)

            h_cond = (
                DiffAcc(
                    (Diff(MoveMax(self.tech["adj_cp"][-n:], n, 0, 1), 1, 0) > 0).astype(float),
                    -1,
                    0,
                )
                > 0
            )
            h_v = Filter(np.expand_dims(h_cond, axis=-1), u_v)
            h_v_chg = h_v - bn.push(np.vstack([u_v[[0]], h_v[:-1]]), axis=0)

            l_cond = (
                DiffAcc(
                    (Diff(MoveMin(self.tech["adj_cp"][-n:], n, 0, 1), 1, 0) < 0).astype(float),
                    -1,
                    0,
                )
                > 0
            )
            l_v = Filter(np.expand_dims(l_cond, axis=-1), u_v)
            l_v_chg = l_v - bn.push(np.vstack([u_v[[0]], l_v[:-1]]), axis=0)

            pconf_v = FillNanBy(NanESum(h_v_chg, axis=0), 0.0) + FillNanBy(NanESum(l_v_chg, axis=0), 0.0)
            pconf_fields = [f"tech_v2_{x}_pconf{n}" for x in u_fields]
            self.res.update(dict(zip(pconf_fields, pconf_v.T)))

    def tech_v2_pcor(self):
        u_fields_dict = {
            20: [
                "obv",
                "pvt",
                "ad",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "cmo10",
                "rsi20",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci5",
                "cci60",
                "macd12&26&9",
                "vmacd12&26&9",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
            ],
            60: [
                "obv",
                "pvt",
                "ad",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "cmo10",
                "rsi20",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci5",
                "cci60",
                "macd12&26&9",
                "vmacd12&26&9",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:]
            pcor_v = NanCorrN(u_v, np.expand_dims(self.tech["adj_cp"][-n:], -1), axis=0)
            pcor_fields = [f"tech_v2_{x}_pcor{n}" for x in u_fields]
            self.res.update(dict(zip(pcor_fields, pcor_v.T)))

    def tech_v2_tbt(self):
        u_fields_dict = {
            20: [
                "adj_cp",
                "obv",
                "pvt",
                "ad",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "cmo10",
                "rsi20",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci5",
                "cci60",
                "macd12&26&9",
                "vmacd12&26&9",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
            ],
            60: [
                "adj_cp",
                "obv",
                "pvt",
                "ad",
                "cko3&10",
                "kvo34&55",
                "wvad",
                "cmo10",
                "rsi20",
                "rvi10&20",
                "mfi10",
                "vr10",
                "ar10",
                "br10",
                "cr10",
                "ddi10",
                "adtm10",
                "cmf10",
                "acd10",
                "cci5",
                "cci60",
                "macd12&26&9",
                "vmacd12&26&9",
                "fso10",
                "sso10",
                "dsso20",
                "uo7&14&28",
            ],
        }
        for n, u_fields in u_fields_dict.items():
            u_fields = np.array(u_fields)[np.isin(u_fields, self.tech_tmp_n, True)]
            u_idx = [self.tech_tmp_n.index(x) for x in u_fields]
            u_v = np.take(self.tech_tmp_v, u_idx, axis=-1)[-n:]
            tbt_v = NanTBetaTStatN(u_v, axis=0)
            tbt_fields = [f"tech_v2_{x}_tbt{n}" for x in u_fields]
            self.res.update(dict(zip(tbt_fields, tbt_v.T)))

    def tech_v2_adj_cp(self):
        self.write_to_tech_tmp("adj_cp", self.tech["adj_cp"])

    # Volume Cumsum

    def tech_v2_obv(self):
        c = 120
        obv = MoveSum(
            self.tech["adj_vol"][-c:] * np.sign(self.tech["adj_cp_ret"][-c:]),
            c,
            axis=0,
            min_n=1,
        )
        obv = Filter(self.tech["vol_cond"][-c:], obv)
        self.write_to_tech_tmp("obv", obv)

    def tech_v2_pvt(self):
        c = 120
        pvt = MoveSum(self.tech["adj_vol"][-c:] * self.tech["adj_cp_ret"][-c:], c, axis=0, min_n=1)
        pvt = Filter(self.tech["vol_cond"][-c:], pvt)
        self.write_to_tech_tmp("pvt", pvt)

    def tech_v2_cko(self):
        s, l = 3, 10
        c = 120
        ad = MoveSum(self.tech["adj_vol"][-c:] * self.tech["ad_w"][-c:], c, axis=0, min_n=1)
        ad = Filter(self.tech["vol_cond"][-c:], ad)
        cko = MoveEMean(ad, s, axis=0) - MoveEMean(ad, l, axis=0)
        self.write_to_tech_tmp(f"cko{s}&{l}", cko)

    def tech_v2_kvo(self):
        s, l = 34, 55
        c = 120
        vf = MoveSum(
            self.tech["adj_vol"][-c:] * np.sign(self.tech["adj_typ_diff"][-c:]),
            c,
            axis=0,
            min_n=1,
        )
        vf = Filter(self.tech["vol_cond"][-c:], vf)
        kvo = MoveEMean(vf, s, axis=0) - MoveEMean(vf, l, axis=0)
        self.write_to_tech_tmp(f"kvo{s}&{l}", kvo)

    def tech_v2_wvad(self):
        c = 120
        wvad = MoveSum(self.tech["adj_vol"][-c:] * self.tech["wvad_w"][-c:], c, axis=0, min_n=1)
        wvad = Filter(self.tech["vol_cond"][-c:], wvad)
        self.write_to_tech_tmp("wvad", wvad)

    # Up Down Ratio | Oscillator

    def tech_v2_forceindex10(self):
        n = 10
        c = n + 21
        adj_rvol = Div(self.tech["adj_vol"], NanMeanN(self.tech["adj_vol"], 0, min_n=30))
        forceindex = MoveEMean(adj_rvol[-c:] * self.tech["adj_cp_ret"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"forceindex{n}", forceindex)

    def tech_v2_forceindex5(self):
        n = 5
        c = n + 21
        adj_rvol = Div(self.tech["adj_vol"], NanMeanN(self.tech["adj_vol"], 0, min_n=30))
        forceindex = MoveEMean(adj_rvol[-c:] * self.tech["adj_cp_ret"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"forceindex{n}", forceindex)

    def tech_v2_bop10(self):
        n = 10
        c = n + 21
        bop = MoveMean(self.tech["wvad_w"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"bop{n}", bop)

    def tech_v2_bop5(self):
        n = 5
        c = n + 21
        bop = MoveMean(self.tech["wvad_w"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"bop{n}", bop)

    def tech_v2_cmo10(self):
        n = 10
        c = n + 21
        up_sum = MoveSum(self.tech["adj_cp_up"][-c:], n, axis=0)
        down_sum = MoveSum(Abs(self.tech["adj_cp_down"][-c:]), n, axis=0)
        cmo = Div(up_sum - down_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"cmo{n}", cmo)

    def tech_v2_cmo5(self):
        n = 5
        c = n + 21
        up_sum = MoveSum(self.tech["adj_cp_up"][-c:], n, axis=0)
        down_sum = MoveSum(Abs(self.tech["adj_cp_down"][-c:]), n, axis=0)
        cmo = Div(up_sum - down_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"cmo{n}", cmo)

    def tech_v2_rsi20(self):
        n = 20
        c = 2 * n + 21
        up_avg = MoveEMean(self.tech["adj_cp_up"][-c:], n, axis=0)
        down_avg = MoveEMean(Abs(self.tech["adj_cp_down"][-c:]), n, axis=0)
        rsi = Div(up_avg, up_avg + down_avg)
        self.write_to_tech_tmp(f"rsi{n}", rsi)
        srsi = Div(
            rsi - MoveMin(rsi, n, axis=0),
            MoveMax(rsi, n, axis=0) - MoveMin(rsi, n, axis=0),
        )
        self.write_to_tech_tmp(f"srsi{n}", srsi)

    def tech_v2_rsi10(self):
        n = 10
        c = 2 * n + 21
        up_avg = MoveEMean(self.tech["adj_cp_up"][-c:], n, axis=0)
        down_avg = MoveEMean(Abs(self.tech["adj_cp_down"][-c:]), n, axis=0)
        rsi = Div(up_avg, up_avg + down_avg)
        self.write_to_tech_tmp(f"rsi{n}", rsi)
        srsi = Div(
            rsi - MoveMin(rsi, n, axis=0),
            MoveMax(rsi, n, axis=0) - MoveMin(rsi, n, axis=0),
        )
        self.write_to_tech_tmp(f"srsi{n}", srsi)

    def tech_v2_crsi(self):
        n1, n2, n3 = 5, 3, 60
        up_avg = NanEMean(self.tech["adj_cp_up"][-n1:], axis=0)
        down_avg = NanEMean(Abs(self.tech["adj_cp_down"][-n1:]), axis=0)
        rsi = Div(up_avg, up_avg + down_avg)

        streak = np.zeros(self.tech["close"][-3 * n2 :].shape)
        streak[0] = np.sign(self.tech["adj_cp_diff"][-3 * n2])
        for i in range(1, 3 * n2):
            sign_i = np.sign(self.tech["adj_cp_diff"][-(3 * n2 - i)])
            streak[i] = np.where(sign_i == np.sign(streak[i - 1]), streak[i - 1] + sign_i, sign_i)
        up_avg = NanEMean(np.maximum(streak, 0)[-n2:], axis=0)
        down_avg = NanEMean(Abs(np.minimum(streak, 0)[-n2:]), axis=0)
        streak_rsi = Div(up_avg, up_avg + down_avg)

        pct_rank = NanPctRankN(self.tech["adj_cp_ret"], axis=0)[-1]

        crsi = NanArrayMean([rsi, streak_rsi, pct_rank])
        self.res[f"tech_v2_crsi{n1}&{n2}&{n3}"] = crsi

    def tech_v2_cmb(self):
        n1, m1, n2, m2 = 27, 9, 5, 3
        c = n1 + m1 + 21
        up_avg = MoveEMean(self.tech["adj_cp_up"][-c:], 27, axis=0, high_precision=True)
        down_avg = MoveEMean(Abs(self.tech["adj_cp_down"][-c:]), 27, axis=0, high_precision=True)
        rsi27_pct_chg9 = Ret(Div(up_avg, up_avg + down_avg), 9, axis=0)

        up_avg = MoveEMean(self.tech["adj_cp_up"][-c:], 5, axis=0)
        down_avg = MoveEMean(Abs(self.tech["adj_cp_down"][-c:]), 5, axis=0)
        rsi5_avg3 = MoveMean(Div(up_avg, up_avg + down_avg), 3, axis=0)

        cmb = rsi27_pct_chg9 + rsi5_avg3
        self.write_to_tech_tmp(f"cmb{n1}&{m1}&{n2}&{m2}", cmb)

    def tech_v2_rvi10(self):
        s, n = 10, 20
        c = s + n + 21
        adj_cp_std = MoveStd(self.tech["adj_cp"][-c:], s, axis=0)
        up_rvi = MoveEMean(np.sign(self.tech["adj_cp_up"][-c:]) * adj_cp_std, n, axis=0)
        down_rvi = MoveEMean(np.sign(Abs(self.tech["adj_cp_down"][-c:])) * adj_cp_std, n, axis=0)
        rvi = Div(up_rvi, up_rvi + down_rvi)
        self.write_to_tech_tmp(f"rvi{s}&{n}", rvi)

    def tech_v2_rvi5(self):
        s, n = 5, 10
        c = s + n + 21
        adj_cp_std = MoveStd(self.tech["adj_cp"][-c:], s, axis=0)
        up_rvi = MoveEMean(np.sign(self.tech["adj_cp_up"][-c:]) * adj_cp_std, n, axis=0)
        down_rvi = MoveEMean(np.sign(Abs(self.tech["adj_cp_down"][-c:])) * adj_cp_std, n, axis=0)
        rvi = Div(up_rvi, up_rvi + down_rvi)
        self.write_to_tech_tmp(f"rvi{s}&{n}", rvi)

    def tech_v2_mfi10(self):
        n = 10
        c = n + 21
        mf = self.tech["adj_typ"][-c:] * self.tech["adj_vol"][-c:]
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_typ_diff"][-c:], 0)) * mf, n, axis=0)
        down_sum = MoveSum(np.sign(Abs(np.minimum(self.tech["adj_typ_diff"][-c:], 0))) * mf, n, axis=0)
        mfi = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"mfi{n}", mfi)

    def tech_v2_mfi5(self):
        n = 5
        c = n + 21
        mf = self.tech["adj_typ"][-c:] * self.tech["adj_vol"][-c:]
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_typ_diff"][-c:], 0)) * mf, n, axis=0)
        down_sum = MoveSum(np.sign(Abs(np.minimum(self.tech["adj_typ_diff"][-c:], 0))) * mf, n, axis=0)
        mfi = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"mfi{n}", mfi)

    def tech_v2_vr10(self):
        n = 10
        c = n + 21
        dif = self.tech["adj_cp_diff"][-c:]
        avs = MoveSum(np.sign(self.tech["adj_cp_up"][-c:]) * self.tech["adj_vol"][-c:], n, axis=0)
        bvs = MoveSum(
            np.sign(Abs(self.tech["adj_cp_down"][-c:])) * self.tech["adj_vol"][-c:],
            n,
            axis=0,
        )
        cvs = MoveSum(
            np.where(np.isnan(dif), np.nan, np.where(dif == 0, self.tech["adj_vol"][-c:], 0)),
            n,
            axis=0,
        )
        vr = Div(avs + cvs / 2, avs + bvs + cvs)
        self.write_to_tech_tmp(f"vr{n}", vr)

    def tech_v2_vr5(self):
        n = 5
        c = n + 21
        dif = self.tech["adj_cp_diff"][-c:]
        avs = MoveSum(np.sign(self.tech["adj_cp_up"][-c:]) * self.tech["adj_vol"][-c:], n, axis=0)
        bvs = MoveSum(
            np.sign(Abs(self.tech["adj_cp_down"][-c:])) * self.tech["adj_vol"][-c:],
            n,
            axis=0,
        )
        cvs = MoveSum(
            np.where(np.isnan(dif), np.nan, np.where(dif == 0, self.tech["adj_vol"][-c:], 0)),
            n,
            axis=0,
        )
        vr = Div(avs + cvs / 2, avs + bvs + cvs)
        self.write_to_tech_tmp(f"vr{n}", vr)

    def tech_v2_arbr10(self):
        n = 10
        c = n + 21
        up_move = self.tech["adj_hp"][-c:] - self.tech["adj_op"][-c:]
        down_move = self.tech["adj_op"][-c:] - self.tech["adj_lp"][-c:]
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        ar = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"ar{n}", ar)
        adj_cp_lag = Shift(bn.push(self.tech["adj_cp"], axis=0), axis=0)[-c:]
        up_move = np.maximum(self.tech["adj_hp"][-c:] - adj_cp_lag, 0)
        down_move = np.maximum(adj_cp_lag - self.tech["adj_lp"][-c:], 0)
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        br = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"br{n}", br)
        arbr = ar - br
        self.write_to_tech_tmp(f"arbr{n}", arbr)

    def tech_v2_arbr5(self):
        n = 5
        c = n + 21
        up_move = self.tech["adj_hp"][-c:] - self.tech["adj_op"][-c:]
        down_move = self.tech["adj_op"][-c:] - self.tech["adj_lp"][-c:]
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        ar = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"ar{n}", ar)
        adj_cp_lag = Shift(bn.push(self.tech["adj_cp"], axis=0), axis=0)[-c:]
        up_move = np.maximum(self.tech["adj_hp"][-c:] - adj_cp_lag, 0)
        down_move = np.maximum(adj_cp_lag - self.tech["adj_lp"][-c:], 0)
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        br = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"br{n}", br)
        arbr = ar - br
        self.write_to_tech_tmp(f"arbr{n}", arbr)

    def tech_v2_cr10(self):
        n = 10
        c = n + 21
        up_move = np.maximum(self.tech["adj_hp"][-c:] - Shift(self.tech["adj_typ"], axis=0)[-c:], 0)
        down_move = np.maximum(Shift(self.tech["adj_typ"], axis=0)[-c:] - self.tech["adj_lp"][-c:], 0)
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        cr = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"cr{n}", cr)

    def tech_v2_cr5(self):
        n = 5
        c = n + 21
        up_move = np.maximum(self.tech["adj_hp"][-c:] - Shift(self.tech["adj_typ"], axis=0)[-c:], 0)
        down_move = np.maximum(Shift(self.tech["adj_typ"], axis=0)[-c:] - self.tech["adj_lp"][-c:], 0)
        up_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], up_move), n, axis=0)
        down_sum = MoveSum(Filter(self.tech["vol_cond"][-c:], down_move), n, axis=0)
        cr = Div(up_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"cr{n}", cr)

    def tech_v2_ddi10(self):
        n = 10
        c = n + 21
        tmp = FMax(Abs(self.tech["adj_hp_diff"][-c:]), Abs(self.tech["adj_lp_diff"][-c:]))
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_hlap_diff"][-c:], 0)) * tmp, n, axis=0)
        down_sum = MoveSum(
            np.sign(Abs(np.minimum(self.tech["adj_hlap_diff"][-c:], 0))) * tmp,
            n,
            axis=0,
        )
        ddi = Div(up_sum - down_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"ddi{n}", ddi)

    def tech_v2_ddi5(self):
        n = 5
        c = n + 21
        tmp = FMax(Abs(self.tech["adj_hp_diff"][-c:]), Abs(self.tech["adj_lp_diff"][-c:]))
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_hlap_diff"][-c:], 0)) * tmp, n, axis=0)
        down_sum = MoveSum(
            np.sign(Abs(np.minimum(self.tech["adj_hlap_diff"][-c:], 0))) * tmp,
            n,
            axis=0,
        )
        ddi = Div(up_sum - down_sum, up_sum + down_sum)
        self.write_to_tech_tmp(f"ddi{n}", ddi)

    def tech_v2_adtm10(self):
        n = 10
        c = n + 21
        up_move = np.maximum(
            self.tech["adj_hp"][-c:] - self.tech["adj_op"][-c:],
            self.tech["adj_op_diff"][-c:],
        )
        down_move = np.maximum(
            self.tech["adj_op"][-c:] - self.tech["adj_lp"][-c:],
            -self.tech["adj_op_diff"][-c:],
        )
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_op_diff"][-c:], 0)) * up_move, n, axis=0)
        down_sum = MoveSum(
            np.sign(Abs(np.minimum(self.tech["adj_op_diff"][-c:], 0))) * down_move,
            n,
            axis=0,
        )
        adtm = Div(up_sum - down_sum, FMax(up_sum, down_sum))
        self.write_to_tech_tmp(f"adtm{n}", adtm)

    def tech_v2_adtm5(self):
        n = 5
        c = n + 21
        up_move = np.maximum(
            self.tech["adj_hp"][-c:] - self.tech["adj_op"][-c:],
            self.tech["adj_op_diff"][-c:],
        )
        down_move = np.maximum(
            self.tech["adj_op"][-c:] - self.tech["adj_lp"][-c:],
            -self.tech["adj_op_diff"][-c:],
        )
        up_sum = MoveSum(np.sign(np.maximum(self.tech["adj_op_diff"][-c:], 0)) * up_move, n, axis=0)
        down_sum = MoveSum(
            np.sign(Abs(np.minimum(self.tech["adj_op_diff"][-c:], 0))) * down_move,
            n,
            axis=0,
        )
        adtm = Div(up_sum - down_sum, FMax(up_sum, down_sum))
        self.write_to_tech_tmp(f"adtm{n}", adtm)

    def tech_v2_cmf10(self):
        n = 10
        c = n + 21
        cmf = MoveWMean(self.tech["ad_w"][-c:], self.tech["adj_vol"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"cmf{n}", cmf)

    def tech_v2_cmf5(self):
        n = 5
        c = n + 21
        cmf = MoveWMean(self.tech["ad_w"][-c:], self.tech["adj_vol"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"cmf{n}", cmf)

    def tech_v2_acd10(self):
        n = 10
        c = n + 21
        adj_a = self.tech["accumulation"][-c:] * self.tech["adjust_factor"][-c:]
        adj_d = self.tech["distribution"][-c:] * self.tech["adjust_factor"][-c:]
        adj_a_sum = MoveSum(np.sign(self.tech["adj_cp_up"][-c:]) * adj_a, n, axis=0)
        adj_d_sum = MoveSum(np.sign(self.tech["adj_cp_down"][-c:]) * adj_d, n, axis=0)
        acd = Div(adj_a_sum - adj_d_sum, adj_a_sum + adj_d_sum)
        self.write_to_tech_tmp(f"acd{n}", acd)

    def tech_v2_acd5(self):
        n = 5
        c = n + 21
        adj_a = self.tech["accumulation"][-c:] * self.tech["adjust_factor"][-c:]
        adj_d = self.tech["distribution"][-c:] * self.tech["adjust_factor"][-c:]
        adj_a_sum = MoveSum(np.sign(self.tech["adj_cp_up"][-c:]) * adj_a, n, axis=0)
        adj_d_sum = MoveSum(np.sign(self.tech["adj_cp_down"][-c:]) * adj_d, n, axis=0)
        acd = Div(adj_a_sum - adj_d_sum, adj_a_sum + adj_d_sum)
        self.write_to_tech_tmp(f"acd{n}", acd)

    # DEV, CCI
    def tech_v2_cci(self):
        n_list = [5, 60]
        for n in n_list:
            c = 2 * n + 21
            adj_typ = Filter(self.tech["vol_cond"][-c:], self.tech["adj_typ"][-c:])
            adj_typ_diff = adj_typ - MoveMean(adj_typ, n, axis=0)
            dev = MoveMean(Abs(adj_typ_diff), n, axis=0)
            self.write_to_tech_tmp(f"dev{n}", dev)
            cci = Div(adj_typ_diff, dev) / 0.015
            self.write_to_tech_tmp(f"cci{n}", cci)

    def tech_v2_tsi20(self):
        s, n = 20, 10
        c = s + n + 21
        dspc = MoveEMean(MoveEMean(self.tech["adj_cp_diff"][-c:], s, axis=0), n, axis=0)
        dsapc = MoveEMean(
            MoveEMean(Abs(self.tech["adj_cp_diff"][-c:]), s, axis=0, high_precision=True),
            n,
            axis=0,
            high_precision=True,
        )
        tsi = Div(dspc, dsapc)
        self.write_to_tech_tmp(f"tsi{s}&{n}", tsi)

    def tech_v2_tsi10(self):
        s, n = 10, 5
        c = s + n + 21
        dspc = MoveEMean(MoveEMean(self.tech["adj_cp_diff"][-c:], s, axis=0), n, axis=0)
        dsapc = MoveEMean(
            MoveEMean(Abs(self.tech["adj_cp_diff"][-c:]), s, axis=0, high_precision=True),
            n,
            axis=0,
            high_precision=True,
        )
        tsi = Div(dspc, dsapc)
        self.write_to_tech_tmp(f"tsi{s}&{n}", tsi)

    def tech_v2_pvi_nvi(self):
        c = 120
        rate = np.exp(self.tech["adj_cp_ret"][-c:])
        vol_dif = DiffAcc(self.tech["adj_vol"], axis=0)[-c:]
        pvi = np.cumprod(np.where(vol_dif > 0, rate, 1), axis=0)
        pvi = Filter(self.tech["vol_cond"][-c:], pvi)
        self.write_to_tech_tmp("pvi", pvi)
        nvi = np.cumprod(np.where(vol_dif < 0, rate, 1), axis=0)
        nvi = Filter(self.tech["vol_cond"][-c:], nvi)
        self.write_to_tech_tmp("nvi", nvi)

        for n in [20]:
            self.res[f"tech_v2_pnvi_cor{n}"] = NanCorrN(pvi[-n:], nvi[-n:], axis=0)

    def tech_v2_atrp_adxr20(self):
        n = 20
        c = 3 * n + 21
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        atrp = Div(atr, bn.push(self.tech["adj_cp"], axis=0)[-c:])
        up_move = np.maximum(self.tech["adj_hp_diff"][-c:], 0)
        down_move = Abs(np.minimum(self.tech["adj_lp_diff"][-c:], 0))
        dif = Filter(self.tech["vol_cond"][-c:], up_move - down_move)
        plusdi = Div(MoveEMean(np.sign(np.maximum(dif, 0)) * up_move, n, axis=0), atr)
        minusdi = Div(MoveEMean(Abs(np.sign(np.minimum(dif, 0))) * down_move, n, axis=0), atr)
        # ndi = Div(plusdi - minusdi, plusdi + minusdi)
        adx = MoveEMean(Div(Abs(plusdi - minusdi), plusdi + minusdi), n, axis=0)
        adxr = NanArrayMean([adx, Shift(adx, n, axis=0)])
        # csi = adxr * atrp
        # locals()['ndi&csi'] = ndi * csi

        for f_ in ["atrp", "plusdi", "minusdi", "adxr"]:  # 'ndi', 'ndi&csi' , 'csi'
            self.write_to_tech_tmp(f"{f_}{n}", locals()[f_])

    def tech_v2_atrp_adxr10(self):
        n = 10
        c = 3 * n + 21
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        atrp = Div(atr, bn.push(self.tech["adj_cp"], axis=0)[-c:])
        up_move = np.maximum(self.tech["adj_hp_diff"][-c:], 0)
        down_move = Abs(np.minimum(self.tech["adj_lp_diff"][-c:], 0))
        dif = Filter(self.tech["vol_cond"][-c:], up_move - down_move)
        plusdi = Div(MoveEMean(np.sign(np.maximum(dif, 0)) * up_move, n, axis=0), atr)
        minusdi = Div(MoveEMean(Abs(np.sign(np.minimum(dif, 0))) * down_move, n, axis=0), atr)
        # ndi = Div(plusdi - minusdi, plusdi + minusdi)
        adx = MoveEMean(Div(Abs(plusdi - minusdi), plusdi + minusdi), n, axis=0)
        adxr = NanArrayMean([adx, Shift(adx, n, axis=0)])
        # csi = adxr * atrp
        # locals()['ndi&csi'] = ndi * csi

        for f_ in ["atrp", "plusdi", "minusdi", "adxr"]:  # 'ndi', 'ndi&csi' , 'csi'
            self.write_to_tech_tmp(f"{f_}{n}", locals()[f_])

    def tech_v2_massindex(self):
        s_list = [9]
        n_list = [25]
        for s, n in zip(s_list, n_list):
            c = 2 * s + n + 21
            range_ema = MoveEMean(
                self.tech["range"][-c:] * self.tech["adjust_factor"][-c:],
                s,
                axis=0,
                high_precision=True,
            )
            massindex = MoveSum(
                Div(range_ema, MoveEMean(range_ema, s, axis=0, high_precision=True)),
                n,
                axis=0,
            )
            self.write_to_tech_tmp(f"massindex{s}&{n}", massindex)

    def tech_v2_ulcer10(self):
        n = 10
        c = 2 * n + 21
        ret = Div(self.tech["adj_cp"][-c:], MoveMax(self.tech["adj_cp"][-c:], n, axis=0)) - 1
        ulcer = Sqrt(MoveMean(ret**2, n, axis=0))
        self.write_to_tech_tmp(f"ulcer{n}", ulcer)

    def tech_v2_ulcer5(self):
        n = 5
        c = 2 * n + 21
        ret = Div(self.tech["adj_cp"][-c:], MoveMax(self.tech["adj_cp"][-c:], n, axis=0)) - 1
        ulcer = Sqrt(MoveMean(ret**2, n, axis=0))
        self.write_to_tech_tmp(f"ulcer{n}", ulcer)

    def tech_v2_dhilo(self):
        n = 90
        self.res[f"tech_v2_dhilo{n}"] = NanMedianN(Log(Div(self.tech["high"][-n:], self.tech["low"][-n:])), axis=0)

    def tech_v2_emv10(self):
        n = 10
        c = n + 21
        adj_rvol = Div(self.tech["adj_vol"], NanMeanN(self.tech["adj_vol"], 0, min_n=30))
        emv1 = Div(
            self.tech["adj_hlap_diff"][-c:],
            Div(adj_rvol[-c:], self.tech["range"][-c:] * self.tech["adjust_factor"][-c:]),
        )
        emv = MoveMean(emv1, n, axis=0)
        self.write_to_tech_tmp(f"emv{n}", emv)

    def tech_v2_emv5(self):
        n = 5
        c = n + 21
        adj_rvol = Div(self.tech["adj_vol"], NanMeanN(self.tech["adj_vol"], 0, min_n=30))
        emv1 = Div(
            self.tech["adj_hlap_diff"][-c:],
            Div(adj_rvol[-c:], self.tech["range"][-c:] * self.tech["adjust_factor"][-c:]),
        )
        emv = MoveMean(emv1, n, axis=0)
        self.write_to_tech_tmp(f"emv{n}", emv)

    # ChaikinVolatility
    def tech_v2_ckv10(self):
        n = 10
        c = 2 * n + 21
        ckv = Ret(
            MoveEMean(
                self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
                n,
                axis=0,
                high_precision=True,
            ),
            n,
            axis=0,
        )
        self.write_to_tech_tmp(f"ckv{n}", ckv)

    # ChaikinVolatility
    def tech_v2_ckv5(self):
        n = 5
        c = 2 * n + 21
        ckv = Ret(
            MoveEMean(
                self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
                n,
                axis=0,
                high_precision=True,
            ),
            n,
            axis=0,
        )
        self.write_to_tech_tmp(f"ckv{n}", ckv)

    def tech_v2_stdamb10(self):
        s, n = 10, 20
        c = 2 * s + n + 21
        stdamb = MoveStd(MoveStd(self.tech["adj_cp_ret"][-c:], s, axis=0), s, axis=0)
        self.write_to_tech_tmp(f"stdamb{s}", stdamb)
        self.res[f"tech_v2_stdamb{s}_tvr_cor{n}"] = NanCorrN(stdamb[-n:], self.tech["amount"][-n:], axis=0)

        self.res[f"tech_v2_lstdamb{s}_tvr_ratio{n}"] = Div(
            NanMean(
                Filter(
                    stdamb[-n:] > NanMeanN(stdamb[-n:], axis=0),
                    self.tech["amount"][-n:],
                ),
                axis=0,
            ),
            NanMeanN(self.tech["amount"][-n:], axis=0),
        )
        l_vol_ratio = Div(
            NanMean(
                Filter(
                    stdamb[-n:] > NanMeanN(stdamb[-n:], axis=0),
                    self.tech["adj_vol"][-n:],
                ),
                axis=0,
            ),
            NanMeanN(self.tech["adj_vol"][-n:], axis=0),
        )
        self.res[f"tech_v2_lstdamb{s}_tv_ratio{n}_dif"] = self.res[f"tech_v2_lstdamb{s}_tvr_ratio{n}"] - l_vol_ratio

    def tech_v2_stdamb5(self):
        s, n = 5, 10
        c = 2 * s + n + 21
        stdamb = MoveStd(MoveStd(self.tech["adj_cp_ret"][-c:], s, axis=0), s, axis=0)
        self.write_to_tech_tmp(f"stdamb{s}", stdamb)
        self.res[f"tech_v2_stdamb{s}_tvr_cor{n}"] = NanCorrN(stdamb[-n:], self.tech["amount"][-n:], axis=0)

        self.res[f"tech_v2_lstdamb{s}_tvr_ratio{n}"] = Div(
            NanMean(
                Filter(
                    stdamb[-n:] > NanMeanN(stdamb[-n:], axis=0),
                    self.tech["amount"][-n:],
                ),
                axis=0,
            ),
            NanMeanN(self.tech["amount"][-n:], axis=0),
        )
        l_vol_ratio = Div(
            NanMean(
                Filter(
                    stdamb[-n:] > NanMeanN(stdamb[-n:], axis=0),
                    self.tech["adj_vol"][-n:],
                ),
                axis=0,
            ),
            NanMeanN(self.tech["adj_vol"][-n:], axis=0),
        )
        self.res[f"tech_v2_lstdamb{s}_tv_ratio{n}_dif"] = self.res[f"tech_v2_lstdamb{s}_tvr_ratio{n}"] - l_vol_ratio

    def tech_v2_aroon(self):
        n = 27
        adj_hp = Filter(self.tech["vol_cond"][-n:], self.tech["adj_hp"][-n:])
        self.res[f"tech_v2_aroon_up{n}"] = (NanArgMax(adj_hp, axis=0) + 1) / n
        adj_lp = Filter(self.tech["vol_cond"][-n:], self.tech["adj_lp"][-n:])
        self.res[f"tech_v2_aroon_down{n}"] = (NanArgMin(adj_lp, axis=0) + 1) / n
        self.res[f"tech_v2_aroon{n}"] = self.res[f"tech_v2_aroon_up{n}"] - self.res[f"tech_v2_aroon_down{n}"]

    def tech_v2_vtx10(self):
        n = 10
        c = n + 21
        tr_sum = MoveSum(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        pvtx = Div(
            MoveSum(
                Abs(self.tech["adj_hp"][-c:] - Shift(self.tech["adj_lp"], axis=0)[-c:]),
                n,
                axis=0,
            ),
            tr_sum,
        )
        self.write_to_tech_tmp(f"pvtx{n}", pvtx)
        nvtx = Div(
            MoveSum(
                Abs(self.tech["adj_lp"][-c:] - Shift(self.tech["adj_hp"], axis=0)[-c:]),
                n,
                axis=0,
            ),
            tr_sum,
        )
        self.write_to_tech_tmp(f"nvtx{n}", nvtx)
        vtx = pvtx - nvtx
        self.write_to_tech_tmp(f"vtx{n}", vtx)

    def tech_v2_vtx5(self):
        n = 5
        c = n + 21
        tr_sum = MoveSum(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        pvtx = Div(
            MoveSum(
                Abs(self.tech["adj_hp"][-c:] - Shift(self.tech["adj_lp"], axis=0)[-c:]),
                n,
                axis=0,
            ),
            tr_sum,
        )
        self.write_to_tech_tmp(f"pvtx{n}", pvtx)
        nvtx = Div(
            MoveSum(
                Abs(self.tech["adj_lp"][-c:] - Shift(self.tech["adj_hp"], axis=0)[-c:]),
                n,
                axis=0,
            ),
            tr_sum,
        )
        self.write_to_tech_tmp(f"nvtx{n}", nvtx)
        vtx = pvtx - nvtx
        self.write_to_tech_tmp(f"vtx{n}", vtx)

    def tech_v2_trix10(self):
        s, n = 10, 9
        c = 3 * s + n + 21
        trix = Ret(
            MoveEMean(
                MoveEMean(MoveEMean(self.tech["adj_cp"][-c:], n, axis=0), n, axis=0),
                n,
                axis=0,
            ),
            axis=0,
        )
        trix = trix - MoveEMean(trix, n, axis=0)
        self.write_to_tech_tmp(f"trix{s}&{n}", trix)

    def tech_v2_trix5(self):
        s, n = 5, 9
        c = 3 * s + n + 21
        trix = Ret(
            MoveEMean(
                MoveEMean(MoveEMean(self.tech["adj_cp"][-c:], n, axis=0), n, axis=0),
                n,
                axis=0,
            ),
            axis=0,
        )
        trix = trix - MoveEMean(trix, n, axis=0)
        self.write_to_tech_tmp(f"trix{s}&{n}", trix)

    def tech_v2_plrc10(self):
        n = 10
        c = n + 21
        plrc = MoveTBeta(self.tech["adj_cp"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"plrc{n}", plrc)

    def tech_v2_plrc5(self):
        n = 5
        c = n + 21
        plrc = MoveTBeta(self.tech["adj_cp"][-c:], n, axis=0)
        self.write_to_tech_tmp(f"plrc{n}", plrc)

    def tech_v2_macd(self):
        s, l, n = 12, 26, 9
        c = l + n + 21
        macd = (
            Div(
                MoveEMean(self.tech["adj_cp"][-c:], s, axis=0),
                MoveEMean(self.tech["adj_cp"][-c:], l, axis=0),
            )
            - 1
        )
        macd = macd - MoveEMean(macd, n, axis=0)
        self.write_to_tech_tmp(f"macd{s}&{l}&{n}", macd)

    def tech_v2_vmacd(self):
        s, l, n = 12, 26, 9
        c = l + n + 21
        vmacd = (
            Div(
                MoveEMean(self.tech["adj_vol"][-c:], s, axis=0),
                MoveEMean(self.tech["adj_vol"][-c:], l, axis=0),
            )
            - 1
        )
        vmacd = vmacd - MoveEMean(vmacd, n, axis=0)
        self.write_to_tech_tmp(f"vmacd{s}&{l}&{n}", vmacd)

    def tech_v2_dbcd(self):
        s, n, l = 5, 16, 17
        c = s + n + l
        bias = Div(self.tech["adj_cp"][-c:], MoveMean(self.tech["adj_cp"][-c:], s, axis=0)) - 1
        self.res[f"tech_v2_dbcd{s}&{n}&{l}"] = NanEMeanN(bias[-l:] - Shift(bias, n, axis=0)[-l:], axis=0)

    def tech_v2_dpo10(self):
        n = 10
        c = n + 21
        dpo = (
            Div(
                Shift(bn.push(self.tech["adj_cp"], axis=0)[-c:], n // 2 - 1, axis=0),
                MoveMean(self.tech["adj_cp"][-c:], n, axis=0),
            )
            - 1
        )
        self.write_to_tech_tmp(f"dpo{n}", dpo)

    def tech_v2_dpo5(self):
        n = 5
        c = n + 21
        dpo = (
            Div(
                Shift(bn.push(self.tech["adj_cp"], axis=0)[-c:], n // 2 - 1, axis=0),
                MoveMean(self.tech["adj_cp"][-c:], n, axis=0),
            )
            - 1
        )
        self.write_to_tech_tmp(f"dpo{n}", dpo)

    def tech_v2_bb(self):
        n_list = [10, 50]
        k_list = [1.5, 2.5]
        for n, k in zip(n_list, k_list):
            c = n + 21
            bb_mid = MoveMean(self.tech["adj_cp"][-c:], n, axis=0)
            std = MoveStd(self.tech["adj_cp"][-c:], n, axis=0, high_precision=True)
            # std = MoveStd(self.tech['adj_cp'][-c:], n, axis=0)
            # std[np.isclose(std, 0., atol=1e-5)] = 0.
            bb_up = bb_mid + k * std
            bb_down = bb_mid - k * std
            bb_loc = Div(self.tech["adj_cp"][-c:] - bb_down, bb_up - bb_down)
            self.write_to_tech_tmp(f"bb_loc{n}&{k}", bb_loc)
            bb_width = Div(bb_up - bb_down, bb_mid)
            self.write_to_tech_tmp(f"bb_width{n}&{k}", bb_width)

    # Keltner Bands
    def tech_v2_kb(self):
        n_list = [10, 50]
        k_list = [1.5, 2.5]
        for n, k in zip(n_list, k_list):
            c = n + 21
            adj_typ = Filter(self.tech["vol_cond"][-c:], self.tech["adj_typ"][-c:])
            kb_mid = MoveMean(adj_typ, n, axis=0)
            atr = MoveMean(
                self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
                n,
                axis=0,
                high_precision=True,
            )
            kb_up = kb_mid + k * atr
            kb_down = kb_mid - k * atr
            kb_loc = Div(adj_typ - kb_down, kb_up - kb_down)
            self.write_to_tech_tmp(f"kb_loc{n}&{k}", kb_loc)
            kb_width = Div(kb_up - kb_down, kb_mid)
            self.write_to_tech_tmp(f"kb_width{n}&{k}", kb_width)

    def tech_v2_squeeze20(self):
        n = 20
        c = n + 21
        std = MoveStd(self.tech["adj_cp"][-c:], n, axis=0)
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        squeeze = Div(std, atr)
        self.write_to_tech_tmp(f"squeeze{n}", squeeze)

    def tech_v2_squeeze10(self):
        n = 10
        c = n + 21
        std = MoveStd(self.tech["adj_cp"][-c:], n, axis=0)
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        squeeze = Div(std, atr)
        self.write_to_tech_tmp(f"squeeze{n}", squeeze)

    def tech_v2_chdexit20(self):
        n, k = 20, 3
        c = n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        long = adj_hp_max - k * atr
        chdexit_long = Div(self.tech["adj_cp"][-c:], long) - 1
        self.write_to_tech_tmp(f"chdexit_long{n}&{k}", chdexit_long)
        short = adj_lp_min + k * atr
        chdexit_short = Div(self.tech["adj_cp"][-c:], short) - 1
        self.write_to_tech_tmp(f"chdexit_short{n}&{k}", chdexit_short)

    def tech_v2_chdexit10(self):
        n, k = 10, 3
        c = n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        atr = MoveEMean(
            self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:],
            n,
            axis=0,
            high_precision=True,
        )
        long = adj_hp_max - k * atr
        chdexit_long = Div(self.tech["adj_cp"][-c:], long) - 1
        self.write_to_tech_tmp(f"chdexit_long{n}&{k}", chdexit_long)
        short = adj_lp_min + k * atr
        chdexit_short = Div(self.tech["adj_cp"][-c:], short) - 1
        self.write_to_tech_tmp(f"chdexit_short{n}&{k}", chdexit_short)

    # Fast Stochastic
    def tech_v2_fso10(self):
        n = 10
        c = n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        fso = Div(self.tech["adj_cp"][-c:] - adj_lp_min, adj_hp_max - adj_lp_min)
        self.write_to_tech_tmp(f"fso{n}", fso)

    # Fast Stochastic
    def tech_v2_fso5(self):
        n = 5
        c = n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        fso = Div(self.tech["adj_cp"][-c:] - adj_lp_min, adj_hp_max - adj_lp_min)
        self.write_to_tech_tmp(f"fso{n}", fso)

    # Slow Stochastic
    def tech_v2_sso10(self):
        n = 10
        c = 2 * n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        sso = Div(
            MoveSum(self.tech["adj_cp"][-c:] - adj_lp_min, n, axis=0),
            MoveSum(adj_hp_max - adj_lp_min, n, axis=0, high_precision=True),
        )
        self.write_to_tech_tmp(f"sso{n}", sso)

    # Slow Stochastic
    def tech_v2_sso5(self):
        n = 5
        c = 2 * n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        sso = Div(
            MoveSum(self.tech["adj_cp"][-c:] - adj_lp_min, n, axis=0),
            MoveSum(adj_hp_max - adj_lp_min, n, axis=0, high_precision=True),
        )
        self.write_to_tech_tmp(f"sso{n}", sso)

    # Double Smoothed Stochastic
    def tech_v2_dsso20(self):
        n = 20
        c = 2 * n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        dsso = Div(
            MoveEMean(self.tech["adj_cp"][-c:] - adj_lp_min, n, axis=0),
            MoveEMean(adj_hp_max - adj_lp_min, n, axis=0, high_precision=True),
        )
        self.write_to_tech_tmp(f"dsso{n}", dsso)

    # Double Smoothed Stochastic
    def tech_v2_dsso10(self):
        n = 10
        c = 2 * n + 21
        adj_hp_max = MoveMax(Filter(self.tech["vol_cond"][-c:], self.tech["adj_hp"][-c:]), n, axis=0)
        adj_lp_min = MoveMin(Filter(self.tech["vol_cond"][-c:], self.tech["adj_lp"][-c:]), n, axis=0)
        dsso = Div(
            MoveEMean(self.tech["adj_cp"][-c:] - adj_lp_min, n, axis=0),
            MoveEMean(adj_hp_max - adj_lp_min, n, axis=0, high_precision=True),
        )
        self.write_to_tech_tmp(f"dsso{n}", dsso)

    def tech_v2_uo(self):
        s, i, l = 7, 14, 28
        c = l + 21
        bp = self.tech["accumulation"][-c:] * self.tech["adjust_factor"][-c:]
        tr = self.tech["ture_range"][-c:] * self.tech["adjust_factor"][-c:]
        rs = Div(MoveSum(bp, s, axis=0), MoveSum(tr, s, axis=0, high_precision=True))
        ri = Div(MoveSum(bp, i, axis=0), MoveSum(tr, i, axis=0, high_precision=True))
        rl = Div(MoveSum(bp, l, axis=0), MoveSum(tr, l, axis=0, high_precision=True))
        uo = Div(rs * i * l + ri * s * l + rl * s * i, i * l + s * l + s * i)
        self.write_to_tech_tmp(f"uo{s}&{i}&{l}", uo)

    def tech_v2_illiq10(self):
        n = 10
        c = n + 21
        illiq = (
            Div(
                MoveSum(Abs(self.tech["adj_cp_ret"][-c:]), n, axis=0),
                MoveSum(self.tech["amount"][-c:], n, axis=0),
            )
            * 1e8
        )
        self.write_to_tech_tmp(f"illiq{n}", illiq)

    def tech_v2_illiq5(self):
        n = 5
        c = n + 21
        illiq = (
            Div(
                MoveSum(Abs(self.tech["adj_cp_ret"][-c:]), n, axis=0),
                MoveSum(self.tech["amount"][-c:], n, axis=0),
            )
            * 1e8
        )
        self.write_to_tech_tmp(f"illiq{n}", illiq)

    def tech_v2_bias(self):
        n_list = [10, 60]
        for n in n_list:
            self.res[f"tech_v2_tbias{n}"] = Div(self.tech["adj_cp"][-1], NanMeanN(self.tech["adj_cp"][-n:], axis=0)) - 1
            adj_vwap = Div(
                NanSumN(self.tech["amount"][-n:], axis=0),
                NanSumN(self.tech["adj_vol"][-n:], axis=0),
            )
            self.res[f"tech_v2_apb{n}"] = (
                Div(
                    NanMeanN(Div(self.tech["amount"][-n:], self.tech["adj_vol"][-n:]), axis=0),
                    adj_vwap,
                )
                - 1
            )

    def tech_v2_srmi(self):
        for n in [5, 20, 60]:
            adj_cp_lag_n = bn.push(self.tech["adj_cp"], axis=0)[-(n + 1)]
            self.res[f"tech_v2_srmi{n}"] = Div(
                self.tech["adj_cp"][-1] - adj_cp_lag_n,
                FMax(self.tech["adj_cp"][-1], adj_cp_lag_n),
            )
        for n in [20, 60]:
            self.res[f"tech_v2_srmio5&{n}"] = self.res["tech_v2_srmi5"] - self.res[f"tech_v2_srmi{n}"]

    def tech_v2_kst(self):
        c = 60
        adj_cp = bn.push(self.tech["adj_cp"], axis=0)[-c:]
        kst = (
            1 * MoveMean(Ret(adj_cp, 10, axis=0), 10, axis=0)
            + 2 * MoveMean(Ret(adj_cp, 15, axis=0), 10, axis=0)
            + 3 * MoveMean(Ret(adj_cp, 20, axis=0), 10, axis=0)
            + 4 * MoveMean(Ret(adj_cp, 30, axis=0), 15, axis=0)
        )
        kst = kst - MoveMean(kst, 9, axis=0)
        self.write_to_tech_tmp("kst", kst)

    def tech_v2_rvol(self):
        n_list = [10, 60]
        for n in n_list:
            self.res[f"tech_v2_rvol{n}"] = Div(self.tech["adj_vol"][-1], NanMeanN(self.tech["adj_vol"][-n:], axis=0))

    def tech_v2_tr(self):
        for n in [120]:
            self.res[f"tech_v2_tr{n}"] = NanMeanN(self.tech["float_tr"][-n:], axis=0, min_n=(n // 4))
        tr5 = NanMeanN(self.tech["float_tr"][-5:], axis=0)
        for n in [20, 60]:
            self.res[f"tech_v2_tro5&{n}"] = tr5 - NanMeanN(self.tech["float_tr"][-n:], axis=0)

    def tech_v2_tvrshp(self):
        n_list = [5, 20]
        for n in n_list:
            self.res[f"tech_v2_tvrshp{n}"] = Div(
                NanMeanN(self.tech["amount"][-n:], axis=0),
                NanStdN(self.tech["amount"][-n:], axis=0, high_precision=True),
            )

    def tech_v2_si(self, n=5):
        a = Abs(self.tech["adj_hp"][-n:] - Shift(self.tech["adj_cp"], axis=0)[-n:])
        b = Abs(self.tech["adj_lp"][-n:] - Shift(self.tech["adj_cp"], axis=0)[-n:])
        c = Abs(self.tech["adj_hp"][-n:] - Shift(self.tech["adj_lp"], axis=0)[-n:])
        d = Abs(Shift(self.tech["adj_cp"], axis=0)[-n:] - Shift(self.tech["adj_op"], axis=0)[-n:])
        e = self.tech["adj_cp"][-n:] - Shift(self.tech["adj_cp"], axis=0)[-n:]
        f = self.tech["adj_cp"][-n:] - self.tech["adj_op"][-n:]
        g = Shift(self.tech["adj_cp"], axis=0)[-n:] - Shift(self.tech["adj_op"], axis=0)[-n:]
        x = e + f / 2 + g
        k = FMax(a, b)
        r = np.where((a > b) & (a > c), a + b / 2 + d / 4, np.nan)
        r = np.where((b > a) & (b > c), a / 2 + b + d / 4, r)
        r = np.where((c > a) & (c > b), c + d / 4, r)
        si = 16 * Div(x, r) * k
        self.res[f"tech_v2_si_ewa{n}"] = NanEMeanN(si, axis=0)
